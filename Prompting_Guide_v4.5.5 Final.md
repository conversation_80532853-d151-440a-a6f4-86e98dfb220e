# **📘 Prompting Guide – v4.5.5 (P1 System)**

Codified structure for DR chain, narrative surfacing, and Layer 5 integration  
**Version: Zero Loss Edition**  
**Last Updated: August 2025**

---

## **🔧 PURPOSE**

This guide governs the construction of franchise brand evaluations under the P1 System. It defines how to generate the DR chain (DR-A through DR-B), how to enforce editorial and data standards, and how to assemble a complete P1 Snapshot.  
It is designed for use by human and AI contributors. All outputs must be fully replicable, buyer-first, and grounded in primary source evidence (FDD and DR).

---

## **🧱 CHAIN STRUCTURE (v4.5.5)**

The P1 System follows this strict chain-of-custody logic:

* **DR-A** → Extract raw, non-editorialized data from the FDD  
* **DR-V** → Validate or challenge the surface-level story using external signals  
* **DR-C** → Translate those signals into strategic implications (buyer-facing)  
* **DR-B** → Generate segmented smart questions that reflect surfaced risk and opportunity  
* **P1** → Assemble a complete, editorially structured profile with all standard sections

📌 If a signal appears in the P1, it must be traceable to DR-A, DR-V, or DR-C.  
📌 If a DR layer includes interpretation, it must be clearly labeled and buyer-relevant.

---

## **🔷 DR-A Prompt**

Extract the following from the FDD (no interpretation allowed):

### **🧠 What They Do**

* Clear, jargon-free description of the business model  
* Must include what franchisees actually do — daily activities, role, scope  
* Mention if the business is seasonal, home-based, manager-run, etc.  
* If ownership group is surfaced in Item 1, include the name and umbrella type (e.g. PE-backed)  
* Growth strategy (if surfaced in FDD) should be noted factually (e.g. “offered nationwide”)  
* If the brand’s founding region, home state, or geographic identity is surfaced in Item 1 or public signals, include it in the business model description (e.g., “Midwestern roots,” “Texas-based,” “Southeast expansion”). This may also be reinforced narratively in Unit Growth or Fit Signal if buyer-relevant.

### **💰 Startup Costs & Fees**

* Structured table required  
* Columns: Type, Amount / Range, Notes  
* Include: total investment, franchise fee, royalty, brand fund (MAP), local marketing, tech/CRM, SEO, renewal, transfer  
* Clarify uncommon fees (e.g. onboarding, licensing, call center, field trainers) in Notes  
* Include per-household, minimums, and tiered fees if disclosed

### **📊 Unit Growth (Item 20\)**

* 3-year table with franchised units, company-owned units, net change  
* Flag any of the following:  
  * Affiliate-held units  
  * Signed-but-not-open backlog  
  * Terminations  
  * Material slowdown or churn  
* Use “📌” callouts below the table for growth commentary  
* Growth strategy field may be inferred from open territory map or Item 1 language

### **❌ Earnings Disclosure (Item 19\)**

* Declare Mode 1 (Gross Only) or Mode 2 (Net Disclosed)  
* Summarize available metrics: quartiles, averages, medians, retention rate, recurring rate  
* State sample size and if company units were included or excluded  
* Flag if profit, owner discretionary income, or breakeven timelines are omitted  
* Use bullet list if complex

### **⚠️ What to Watch (FDD-Only Risks)**

* Surface FDD-derived risks only — no DR-V insight allowed  
* Call out fee stacking, required spend, royalty floors, licensing, lack of resale, etc.  
* Use bullets or short narrative  
* Do not editorialize — keep this section factual and FDD-based

### **💸 How This Brand Makes Money**

* Explain revenue model for both franchisee and franchisor  
* Must include:  
  * Basis for royalty (gross sales vs. billings)  
  * Marketing and tech platform fees  
  * Optional: resale program fees, rebates, supply margins  
* Keep phrasing tight and clean

## **🔷 DR-V Prompt**

Validate or challenge the DR-A story using public signals. Must cover all 6 categories:

### **✅ Validation Access**

* Does the system provide guided validation support?  
* Are there franchisee archetypes, regional groups, or curated access?  
* Is discovery managed by a sales team (e.g., Neighborly’s “Manager” division)?  
* Are validation resources mentioned or implied in DR sources?

### **😐 Franchisee Sentiment**

* Use tone gradient: “mixed,” “positive but cautious,” “regionally split”  
* Grounded in public reviews, podcasts, testimonials, or third-party reports  
* Flag if sentiment varies by territory, marketing budget, or operational skill

### **❌ Exitability**

* Is there a resale path?  
* Are listings or valuation pipelines visible?  
* Is resale support offered?  
* Flag if closures are unexplained or resale structure is missing

### **⚠️ Support Experience**

* What support is offered beyond tech?  
* Is onboarding scaffolded or self-directed?  
* Are there embedded coaches or benchmarks?  
* Does the franchisor frame support as profit-enablement or brand control?

### **🟡 Legal / PR Reputation**

* Surface litigation, major disputes, FPR-related lawsuits  
* Note managed PR, legal opacity, or absence of scrutiny  
* Interpretation allowed only when buyer-relevant

### **💖 Brand Devotion (Category 6 — Season 16 Addition)**

Surface any signals of emotional resonance, cultural cachet, or customer passion that exceed category norms.  
Sources must include:

* Repetitive buyer behavior (testimonials, social patterns)  
* Loyalty language or viral branding (Reddit, TikTok, blogs)  
* Multi-generational attachment or nostalgia  
  ⚠️ Do not surface Brand Devotion if only sourced from franchisor marketing or paid PR.

---

## **🔷 DR-C Prompt**

Translate validated signals into strategic implications. This is not a summary — it is structured interpretation for the buyer.

### **1\. 🔍 Mode Classification**

* Declare Mode 1 or Mode 2  
* Must appear at top of DR-C  
* Use bold callout formatting (e.g., **Mode 2 – Net Disclosed**)

### **2\. 💸 Fee & Margin Pressure**

Interpret cost structure from DR-A and DR-V  
Must reflect total economic burden on the franchisee, including:

* Royalty  
* Brand fund (MAP)  
* Local marketing  
* SEO/CRM/Tech stack  
* Onboarding, licensing, or programmatic spend  
* Required spend to drive revenue  
  Include:  
* Royalty minimums  
* Tiered models  
* Fee stacking  
  Must relate pressure back to buyer experience:  
* Breakeven complexity  
* Front-loaded risk  
* Low-margin warning  
  **Do not use generic language** (e.g. “fees are high”) — explain why they matter.

### **3\. 🧩 Enrichment Fields (1–3 Required)**

Each must explain its buyer relevance. Use only if surfaced in DR-V:

* Support Model  
* Exitability  
* Franchisee Strain  
* Buyer Mismatch Risk  
* System Maturity  
* CapEx Load / Ramp Block  
* Validation Curation  
* Saturation Signal  
* Brand Devotion (only if surfaced via DR-V Category 6\)

### **4\. 🧭 Buyer Fit Map (Season 16 Addition)**

Required table mapping six standard buyer personas:

* First-Time Franchisee  
* Career Switcher  
* Multi-Unit Builder  
* Hands-Off Investor  
* Capital-Limited Buyer  
* Mission-Driven Buyer  
  Each must be labeled:  
  ✅ Strong Fit / ⚠️ Conditional Fit / ❌ Not Recommended  
  Include 1–2 sentence justification per row  
  If 2+ personas are ❌, set `buyer_misalignment_flag = true` in Layer 5

### **5\. 🧠 Strategic Outlook**

Required final paragraph.  
Must connect DR layers into a buyer-facing POV, flagging burden vs. opportunity  
Include at least one surfaced insight (from DR-A, DR-V, or DR-C)  
**No DR labels or AI-sounding phrasing allowed**

### **6\. 🔍 Second-Order Insight (If Surfaced — Updated Guardrails)**

Optional paragraph, clearly labeled:

🔍 Second-Order Insight: \[text here\]  
Use **only** if structural risk isn’t obvious from DR-C  
Updated trigger logic:

* ✅ Required if onboarding monetization outweighs resale support  
* ✅ Required if profit hidden but revenue shown  
* ⚠️ Optional if ramp burden is subtle or unacknowledged  
* ❌ Do not use if buyer burden is already clear elsewhere

## **🔷 DR-B Prompt**

Generate 15 smart questions for buyers, segmented by discovery stage:

### **🏢 Franchisor — Before Reading the FDD**

* Focus on buyer fit, licensing, ramp, validation, ownership  
* Include 1 question on growth strategy if surfaced  
* Help buyers pre-screen themselves

### **📄 Franchisor — After Reading the FDD**

* Focus on earnings, ramp, fees, legal terms, resale clarity  
* Questions must directly reflect surfaced risks in DR-A and DR-C  
* Ask what’s missing — not just what’s present

### **👥 Franchisees — Owner Insight**

* Focus on onboarding, breakeven, net income, resale experience  
* Ask what buyers wouldn’t learn from the FDD or sales rep

🛑 Formatting Rules:

* Three blocks of 5 questions  
* Numbered 1–5 in each section (restart count)  
* No bullets, summaries, or DR-B references allowed  
* Must appear exactly as written in the P1

---

## **📄 P1 Snapshot Prompt**

Construct the full 13-section profile. Each section must follow P1 enforcement rules.

| Section | Must Include |
| ----- | ----- |
| 🧠 What They Do | Buyer-facing summary of franchisee role, staffing, licensing, and platform model. Surface `ownership_structure` or `growth_strategy` if present |
| 🎯 Fit Signal | Must start with “Best for…” clause. If `fit_signal_risk_modified = true`, use contrast phrasing (e.g. “those seeking \_\_\_ may find \_\_\_ too \_\_\_”) — never stack disqualifiers |
| ✅ System Positives | 2–3 quantifiable or structural bullets. No generic praise |
| 🧠 Strategic Outlook | Required final paragraph. Must interpret burden vs. opportunity clearly |
| 🔍 Second-Order Insight | Required if surfaced in DR-C. Must be labeled. |
| 📊 Unit Growth | 3-year table \+ 1–2 sentence summary. Must surface backlog, churn, or affiliate units if present |
| ❌ Earnings Disclosure | Mode label \+ earnings summary. Include sample size and any missing profitability data |
| 💰 Startup Costs & Fees | Structured table with explanatory notes. Must include platform fees, licensing, and royalty minimums |
| 💬 Smart Questions | Must exactly replicate DR-B, using 1–5 / 1–5 / 1–5 structure |
| 🕒 Ramp Expectation | Estimated breakeven \+ ramp factors. Must reflect DR-V and support structure |
| 🚪 Exitability Note | Must reflect resale friction or clarity. Must match DR-C claims |
| 💸 How This Brand Makes Money | Revenue model for franchisee and franchisor. Must include royalty basis and any margin streams |
| 🧠 Closing Thoughts | Clean, confident summary. If triggered, include Room to Grow Clause (non-customized). |

---

## **🧠 Room to Grow Clause (Optional – Season 16\)**

Use **only** if:

* The brand is lightly disclosed, early-stage, or structurally immature  
* No surfaced red flags exist in DR-C or DR-V  
  If triggered, include one standard clause (from Clause Library A–C)  
  Set `room_to_grow_clause_present = true` in Layer 5  
  📌 Do **not** customize this clause or use unless justified

## **📋 P1 Snapshot Section Requirements (QA \+ Narrative Enforcement Table)**

Each of the 13 sections must appear in every P1. The table below outlines what’s required — and what will trigger QA failure.

| Section | ✔️ Must Include | ❌ QA Fail Example |
| ----- | ----- | ----- |
| **🧠 What They Do** | Clear, buyer-facing summary of franchisee role, staffing, licensing, and platform structure. Mention if seasonal or manager-run. If `ownership_structure` or `growth_strategy` are present, surface here. | Generic claims (“great opportunity”) or vague industry buzzwords |
| **🎯 Fit Signal** | Must start with a concrete buyer type (e.g. “Best for first-time buyers seeking…”). Include national/regional availability if `growth_strategy = true`. If `fit_signal_risk_modified = true`, use contrast phrasing — never stack disqualifiers. Note: If geographic identity is surfaced in DR-A or DR-V, it may be echoed in the Fit Signal for buyer alignment — but must not be introduced narratively without grounding. | Hedging language (“may be best for…”) or platitudes (“great for all buyers”) |
| **✅ System Positives** | 2–3 quantitative or structural bullets. Should reflect support, ramp, disclosure clarity, or unit count health. | Filler praise (“great brand”), generic bullets (“proven model”) |
| **🧠 Strategic Outlook** | Connect all surfaced insight into a confident, buyer-facing interpretation. Must reflect burden, opportunity, or tone gradient. | High-level summary, metaphor, or vague advice |
| **🔍 Second-Order Insight** | Optional paragraph, clearly labeled. Required if system monetizes onboarding, hides resale support, or structural risk is non-obvious. | Used without label or when redundant to DR-C |
| **📊 Unit Growth** | 3-year table \+ 1–2 sentence commentary. Flag terminations or signed-but-not-open units. | Table without context or explanation |
| **❌ Earnings Disclosure** | Mode tag (1 or 2), summary table or bullets. Sample size, company units included?, profit visibility. | Missing tag or overly vague summary |
| **💰 Startup Costs & Fees** | Full structured table with notes. Must include royalty, tech/CRM, licensing, and any uncommon required spend. | Missing royalty or CRM. Table formatted inconsistently |
| **💬 Smart Questions** | Direct reuse of DR-B. Numbered 1–5 / 1–5 / 1–5 with no extras | Summary of questions. Missing one or more segments. Wrong formatting |
| **🕒 Ramp Expectation** | Estimated breakeven range (months). Must reflect support, spend, and DR-V signals. | Placeholder text (“varies widely”) or no ramp factors listed |
| **🚪 Exitability Note** | Summary of resale visibility and support. Must reflect real pathway friction or lack thereof. | Overly optimistic. Generic phrases (“may be able to sell”) |
| **💸 How This Brand Makes Money** | Revenue model for both franchisee and franchisor. Must include royalty basis, marketing/tech stack, and optional resale/supply margin. | “Makes money from fees” without specifics |
| **🧠 Closing Thoughts** | Clean buyer-facing summary. No repetition. Room to Grow clause allowed if justified and pulled from approved list. | Generic summary. Clause inserted without trigger logic |

# **🔁 Layer 5 Schema (v1.3 – Final)**

Layer 5 schema v1.3 includes surfacing enforcement \+ export format (JSON v1.2)

### **1\. 📌 Surfacing Rules (Narrative Enforcement)**

| Field | Narrative Surfacing Required? | Location |
| ----- | ----- | ----- |
| `ownership_structure` | Yes (if surfaced in Item 1 or DR-V) | DR-A \+ What They Do |
| `pe_backed_flag` | Optional (but recommended) | DR-V or Strategic Outlook |
| `growth_strategy` | Yes (if surfaced in DR-A) | What They Do; Fit Signal (if relevant); Unit Growth |
| `support_scaling_risk_flag` | Yes | DR-C \+ Strategic Outlook |
| `exitability_present = false` | Yes | DR-C \+ Exitability Note |
| `earnings_visibility_tier` | No (tone modifier only) | Not surfaced directly |
| `validation_curation_flag` | Yes (if surfaced) | DR-V \+ DR-C |
| `fit_signal_risk_modified` | Yes (if Fit Signal includes disqualifier) | Fit Signal |
| `second_order_insight_present` | Yes (if surfaced) | Strategic Outlook (labeled) |
| `room_to_grow_clause_present` | Yes (if surfaced) | Closing Thoughts |
| `buyer_fit_map_present` | Always `true` | Fit Map section |
| `buyer_misalignment_flag` | Yes (if 2+ ❌ in Fit Map) | Fit Map justification |
| `brand_devotion_present` | Yes (if surfaced in DR-V) | DR-V \+ DR-C |
| `dr_backed_insight_used` | Yes (if Strategic Outlook or Closing Thoughts embed DR-A/V/C content) | Inferred |
| `system_positives_strength` | Required | System Positives bullets |

---

### **2\. 📤 Structured Export (JSON Format – v1.2)**

{

  "franchise\_name": "",

  "p1\_version\_label": "v4.5.5",

  "ownership\_structure": "platform\_owned",

  "pe\_backed\_flag": true,

  "growth\_strategy": "open\_nationally",

  "sba\_registry\_flag": true,

  "support\_scaling\_risk\_flag": false,

  "exitability\_present": true,

  "earnings\_visibility\_tier": "moderate",

  "validation\_curation\_flag": false,

  "resale\_data\_absent": false,

  "fit\_signal\_risk\_modified": false,

  "dr\_backed\_insight\_used": true,

  "second\_order\_insight\_present": false,

  "room\_to\_grow\_clause\_present": false,

  "buyer\_fit\_map\_present": true,

  "buyer\_misalignment\_flag": false,

  "brand\_devotion\_present": false,

  "system\_positives\_strength": "strong",

  "unit\_growth\_table": {

    "year\_1": {},

    "year\_2": {},

    "year\_3": {}

  },

  "item\_19\_disclosed": true,

  "item\_19\_mode": "Mode 2",

  "sample\_size": 95,

  "net\_income\_data\_present": true,

  "ramp\_time": "12–18 months",

  "franchisee\_sentiment": "mixed",

  "resale\_support\_available": true,

  "support\_model": "platform \+ onboarding"

}

### **3\. 🧠 Layer 5 Field Dictionary (Finalized)**

| Field | Type | Accepted Values | Trigger / Enforcement Rule |
| ----- | ----- | ----- | ----- |
| `ownership_structure` | enum | `"independent"`, `"platform_owned"`, `"unknown"` | Surface in What They Do if ownership context is clear |
| `pe_backed_flag` | boolean | `true` / `false` | True if Item 1 or DR-V confirms PE investment (any kind). Optional narrative surfacing |
| `growth_strategy` | enum | `"open_nationally"`, `"regional_focus"`, `"legacy_saturated"`, `"newly_launching"`, `"international_first"`, `"unclear"` | Set from Item 1, DR-V, or territory map |
| `support_scaling_risk_flag` | boolean | `true` / `false` | True if onboarding/support gaps appear in DR-C |
| `exitability_present` | boolean | `true` / `false` | False if resale process is unclear, absent, or unsupported |
| `earnings_visibility_tier` | enum | `"high"`, `"moderate"`, `"low"`, `"none"` | Set based on depth of Item 19 (DR-A) |
| `validation_curation_flag` | boolean | `true` / `false` | True if validation is sales-managed, filtered, or scripted |
| `fit_signal_risk_modified` | boolean | `true` / `false` | True if Fit Signal includes disqualifier or conditional clause |
| `second_order_insight_present` | boolean | `true` / `false` | True if SOI paragraph appears in Strategic Outlook |
| `room_to_grow_clause_present` | boolean | `true` / `false` | True if Room to Grow clause is used in Closing Thoughts |
| `buyer_fit_map_present` | boolean | Always `true` | Required in all P1 Snapshots under v4.5.5+ |
| `buyer_misalignment_flag` | boolean | `true` / `false` | True if 2 or more personas are ❌ in Fit Map |
| `brand_devotion_present` | boolean | `true` / `false` | True if DR-V Category 6 is used |
| `dr_backed_insight_used` | boolean | `true` / `false` | True if DR-A/V/C insight appears in Strategic Outlook or Closing Thoughts |
| `system_positives_strength` | enum | `"strong"`, `"thin"`, `"omitted"` | Scoring judgment of System Positives bullets (required) |

📌 All fields must be populated in Layer 5\.

📌 No inferred or default values allowed unless explicitly triggered via DR-A, DR-V, or DR-C.

📌 QA failure if surfacing requirements are unmet for any triggered field.

---

## **🏛️ Gold Chain Validity Rule (Season 16 Governance)**

Gold Chain status is version-bound. Brands approved under prior versions of the P1 system (e.g. v4.5.4 or earlier) must be rebuilt under v4.5.5 (or later) to retain the designation.

A valid Gold Chain:

* Must be constructed using current prompt guidance, QA standards, and Layer 5 schema  
* Must include all Season 16 requirements (Fit Map, Room to Grow, Brand Devotion logic, etc.)  
* Must pass QA under current rules without exception

If a previously approved brand is missing required structure or enforcement fields, it must either be:

* 🔁 Retrofitted, which includes updating `ownership_structure`, Fit Map, and Layer 5 flags to v4.5.5 compliance  
* 🛑 Marked as expired and removed from badge eligibility

The badge means something — and version control protects it.