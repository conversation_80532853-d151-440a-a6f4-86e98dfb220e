# Franchise Output Generation System

## Overview

This Python-based automation system processes franchise data from a CSV file and generates structured outputs for each franchise using AI. The system combines franchise datapoints with formatting guidelines to produce consistent, professional P1 Snapshot analyses following the v4.5.5 system guidelines.

## Features

- **Automated CSV Processing**: Loads and parses franchise data with multi-level headers
- **AI-Powered Content Generation**: Uses Novita AI API to generate comprehensive franchise analyses
- **Structured JSON Outputs**: Creates properly formatted JSON files for each franchise
- **Professional HTML Index**: Generates a master index page with navigation links
- **Browser Integration**: Automatically opens results in the default browser
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Error Handling**: Robust error handling with retry mechanisms

## Requirements

### Python Dependencies
- Python 3.x
- pandas
- requests
- json (built-in)
- webbrowser (built-in)
- logging (built-in)

### Input Files
1. **`Datapoints - Actual_Datapoints.csv`** - Franchise data with multi-level headers
2. **`Prompting_Guide_v4.5.5 Final.md`** - Formatting guidelines and structure requirements
3. **`Output-Reference.md`** - Sample output demonstrating expected style and tone

### API Configuration
- **Novita AI API** access with the following configuration:
  - Base URL: `https://api.novita.ai/openai`
  - Model: `openai/gpt-oss-20b`
  - API Key: Configured in the script

## Installation

1. Ensure Python 3.x is installed
2. Install required packages:
   ```bash
   pip install pandas requests
   ```
3. Place all input files in the same directory as the script
4. Verify API access to Novita AI

## Usage

### Quick Start
```bash
python3 franchise_automation.py
```

### Test Single Franchise
```bash
python3 test_single_franchise.py
```

### Custom Processing
```python
from franchise_automation import FranchiseProcessor

processor = FranchiseProcessor()
# Process specific number of franchises
processor.run_complete_process(limit=5)
# Process all franchises
processor.run_complete_process()
```

## Output Structure

### Individual Franchise Files
- **Location**: `franchise_outputs/` directory
- **Format**: JSON files with `.json` extension
- **Naming**: Sanitized franchise names (e.g., `101_Mobility.json`)
- **Content**: Complete P1 Snapshot following v4.5.5 guidelines

### Master Index
- **File**: `Master_Index.html`
- **Content**: Professional HTML page with navigation links
- **Features**: Responsive design, download links, processing statistics

## System Architecture

### Core Components

1. **FranchiseProcessor**: Main processing class
2. **CSV Loading**: Multi-level header parsing with pandas
3. **AI Integration**: Novita AI API calls with retry logic
4. **Output Generation**: JSON file creation and HTML index generation
5. **Browser Automation**: Automatic result display

### Processing Workflow

1. **Data Loading**: Parse CSV with complex header structure
2. **Content Generation**: For each franchise:
   - Extract datapoints
   - Construct AI prompt with guidelines
   - Generate structured content via AI
   - Save as JSON file
3. **Index Creation**: Generate master HTML navigation page
4. **Browser Launch**: Open results automatically

## Configuration

### API Settings
```python
self.api_base_url = "https://api.novita.ai/openai"
self.api_key = "your-api-key-here"
self.model = "openai/gpt-oss-20b"
```

### File Paths
```python
self.csv_file = "Datapoints - Actual_Datapoints.csv"
self.prompting_guide_file = "Prompting_Guide_v4.5.5 Final.md"
self.output_reference_file = "Output-Reference.md"
self.output_dir = "franchise_outputs"
self.master_index_file = "Master_Index.html"
```

## Error Handling

- **API Failures**: Automatic retry with exponential backoff
- **CSV Issues**: Graceful handling of missing or malformed data
- **File Operations**: Comprehensive error logging and recovery
- **Network Issues**: Timeout handling and retry mechanisms

## Logging

The system provides detailed logging to both console and file:
- **Log File**: `franchise_processing.log`
- **Log Levels**: INFO, ERROR, WARNING
- **Content**: Processing progress, API calls, errors, timing

## Performance

- **Processing Time**: ~45-60 seconds per franchise (AI generation)
- **API Limits**: Built-in rate limiting with 1-second delays
- **Memory Usage**: Efficient pandas operations for large datasets
- **Scalability**: Designed to handle hundreds of franchises

## Troubleshooting

### Common Issues

1. **CSV Loading Errors**: Check header structure and encoding
2. **API Failures**: Verify API key and network connectivity
3. **File Permission Issues**: Ensure write access to output directory
4. **Browser Launch Issues**: Check default browser configuration

### Debug Mode
Enable detailed logging by setting log level to DEBUG in the script.

## License

This system is designed for franchise analysis automation following the P1 System v4.5.5 guidelines.
