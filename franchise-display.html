<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <title id="page-title">Franchise Analysis</title>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Base styles from sample template */
        * {margin: 0;padding: 0;box-sizing: border-box;}
        html, body {height: 100%;font-family: "Inter", sans-serif;font-optical-sizing: auto;color: var(--dark_gray);background-color: #FFF;font-weight: 500;line-height: 1.2;font-size: 13px;}
        body {line-height: 1.2;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;display: flex;flex-direction: column;}
        img, picture, video, canvas, svg {display: block;max-width: 100%;image-rendering: auto;transform: translateZ(0);}
        input, button, textarea, select {font: inherit;}
        a {text-decoration: none;color: inherit;}
        p {margin-bottom: 1em;}
        ul, ol {list-style: none;}
        li {padding-bottom: 1em;}
        h1, h2, h3, h4{color: var(--dark);font-weight: 700;margin-bottom: 1em;}
        h1 {font-weight: 800;font-size: 1.5rem;line-height: 1.2;}
        h2, h1.h2, strong.h2 {font-size: 1.25rem;}
        h3 {font-size: 1rem;}
        h4 {font-size: .875rem;}

        /* Color variables */
        :root {
            --dark: #282828;
            --menu_gray: #7A7A7A;
            --dark_gray: #888;
            --light_gray: #DCDCDC;
            --light: #F7F7F7;
            --light_red: #ff6d7e;
            --red: #E1253A;
            --button_red: #cc1209;
            --link_blue: #1A73E8;
            --blue: #5F9DEF;
            --light_blue: #ddeafc;
            --light_green: rgb(191 255 183 / 50%);
            --border_green: #0b9f00;
        }

        /* Layout */
        .container {width: 87.5rem;max-width: 100%;padding: 0 1.5rem;margin: 0 auto;}
        .container_medium{max-width: 75rem;width: 100%;margin: auto;}
        .container_small{max-width: 35rem;width: 100%;margin: auto;}

        /* Cards */
        .white_shell {border-radius: 1rem;overflow: hidden;background: #FFF;box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.2);position: relative;transition: all .2s ease-in;}
        .white_shell:not(article) {padding: 1rem;}
        
        /* Header */
        header#top {order: 1;background-color: #FFF;height: 4rem;min-height: 4rem;display: flex;flex-direction: row;align-items: center;justify-content: space-between;padding: 0 1rem;box-sizing: border-box;position: fixed;inset: 0 0 auto;z-index: 4;}
        header#top img {height: 1.5rem;width: auto;}
        @media only screen and (min-width: 441px) {header#top img {height: 1.75rem;}}
        .back {display: none;}

        /* Main content */
        main {order: 2;padding-top: 4rem;}

        /* Title section */
        #title_section {padding: 1.5rem 0;}
        #title_section h1{margin: 0;}
        #title_section h1 + h2 {font-size: 1rem;font-weight: 500;color: var(--dark_gray);margin: 0;}
        #title_section p {line-height: 1.4;}
        #title_section h1 + :is(p, h2) {padding-top: .25rem;}

        /* Copy section */
        .copy {padding: 2.5rem 0;}
        .copy p{line-height: 1.4;}
        :is(.copy, .login_modal, .login_page) a {color: var(--link_blue);}
        .copy :is(strong, b) {font-weight: 800;}
        .copy :is(ul, ol) {list-style-type: initial;padding-left: 1rem;margin-bottom: 1rem;}
        :is(ul, ol) li::marker {color: var(--dark);}
        .copy :is(ul, ol) li:nth-last-child(1) {padding-bottom: 0;}
        .copy .container > *:nth-last-child(1) {margin-bottom: 0;}
        .copy h2{margin-bottom: 1rem;}

        /* Tables */
        .copy table {background-color: #FFF;box-shadow: 0 0 0 .125rem var(--blue);width: 100%;max-width: 48rem;border-radius: .5rem;border-collapse: collapse;margin-bottom: 1em;overflow: hidden;}
        .copy table > *:first-child, .copy table tbody > *:first-child {border-radius: 1.5rem 1.5rem 0 0;}
        .copy table > *:last-child, .copy table tbody > *:last-child{border-radius: 0 0 1.5rem 1.5rem;}
        .copy thead {background-color: var(--link_blue);color: #FFF;font-size: 1.5rem;}
        .copy tr:nth-child(even) {background-color: var(--light_blue);}
        .copy :is(td, th) {padding: .75rem;border: none;}
        .copy hr {border: none;border-top: .0625rem solid var(--light_gray);margin: 1rem 0;}

        /* Charts */
        .chart-container {
            background: #FFF;
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.1);
            position: relative;
            height: 400px;
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 1rem;
            text-align: center;
        }

        /* DR Section styling */
        .dr-section {
            background: #FFF;
            border-radius: 1rem;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--link_blue);
        }

        .dr-section h1 {
            color: var(--link_blue);
            border-bottom: 2px solid var(--light_blue);
            padding-bottom: 0.5rem;
        }

        .dr-section h2 {
            color: var(--dark);
            margin-top: 2rem;
        }

        .dr-section h3 {
            color: var(--dark_gray);
            margin-top: 1.5rem;
        }
        
        .main-content {
            margin-top: -4rem;
            position: relative;
            z-index: 10;
        }

        .content-card {
            background: white;
            border-radius: 1.5rem;
            padding: 0;
            box-shadow: 0 10px 40px rgba(0,0,0,0.08);
            border: 1px solid var(--gray-200);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .content-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.12);
        }

        .content-card-body {
            padding: 2.5rem;
        }

        .section-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 2.5rem;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
        }

        .section-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1));
            transform: skewX(-15deg);
        }

        .section-header h2 {
            margin: 0;
            font-size: 1.75rem;
            font-weight: 700;
            letter-spacing: -0.01em;
        }

        .section-header i {
            font-size: 1.75rem;
            opacity: 0.9;
        }

        .dr-section {
            border-left: 4px solid var(--primary-color);
            margin-bottom: 3rem;
        }

        .dr-section .section-header {
            background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-700) 100%);
        }

        .dr-a { border-left-color: var(--primary-color); }
        .dr-v { border-left-color: var(--success-color); }
        .dr-c { border-left-color: var(--warning-color); }
        .dr-b { border-left-color: var(--info-color); }

        .chart-container {
            position: relative;
            height: 350px;
            margin: 2rem 0;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 1rem;
            border: 1px solid var(--gray-200);
        }

        .chart-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 1rem;
            text-align: center;
        }

        .integrated-chart {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border: 1px solid var(--gray-200);
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        
        .franchise-table {
            margin: 0;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid var(--gray-200);
        }

        .franchise-table th {
            background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-700) 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 1.25rem 1rem;
            font-size: 0.95rem;
            letter-spacing: 0.02em;
            text-transform: uppercase;
        }

        .franchise-table td {
            padding: 1.25rem 1rem;
            border-color: var(--gray-200);
            vertical-align: middle;
            font-weight: 500;
            color: var(--gray-700);
        }

        .franchise-table tbody tr {
            transition: all 0.2s ease;
        }

        .franchise-table tbody tr:hover {
            background-color: var(--gray-50);
            transform: scale(1.01);
        }

        .franchise-table tbody tr:nth-child(even) {
            background-color: var(--gray-50);
        }

        .franchise-table tbody tr:nth-child(even):hover {
            background-color: var(--gray-100);
        }

        .callout {
            background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
            border-left: 4px solid var(--info-color);
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 1rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            border: 1px solid var(--gray-200);
            position: relative;
        }

        .callout::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--info-color), var(--primary-color));
            border-radius: 1rem 1rem 0 0;
        }

        .callout i {
            margin-right: 0.75rem;
            color: var(--info-color);
            font-size: 1.1rem;
        }

        .content-display h1 {
            color: var(--gray-800);
            font-size: 2rem;
            font-weight: 700;
            margin: 2.5rem 0 1.5rem 0;
            letter-spacing: -0.02em;
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 1rem;
        }

        .content-display h2 {
            color: var(--gray-700);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 2rem 0 1rem 0;
            letter-spacing: -0.01em;
            border-left: 4px solid var(--primary-color);
            padding-left: 1rem;
        }

        .content-display h3 {
            color: var(--gray-700);
            font-size: 1.25rem;
            font-weight: 600;
            margin: 1.5rem 0 0.75rem 0;
        }

        .content-display p {
            color: var(--gray-600);
            line-height: 1.7;
            margin-bottom: 1.25rem;
            font-weight: 400;
        }

        .content-display ul {
            margin: 1.5rem 0;
            padding-left: 0;
        }

        .content-display li {
            list-style: none;
            margin: 1rem 0;
            padding: 1rem 1.5rem;
            background: var(--gray-50);
            border-left: 4px solid var(--primary-color);
            border-radius: 0.5rem;
            position: relative;
            transition: all 0.2s ease;
        }

        .content-display li:hover {
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transform: translateX(5px);
        }

        .content-display li::before {
            content: "▶";
            color: var(--primary-color);
            position: absolute;
            left: 0.75rem;
            font-size: 0.8rem;
        }

        .content-display strong {
            color: var(--gray-800);
            font-weight: 600;
        }

        .sidebar-card {
            background: white;
            border-radius: 1.5rem;
            padding: 0;
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
            border: 1px solid var(--gray-200);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 45px rgba(0,0,0,0.12);
        }

        .sidebar-card-body {
            padding: 2rem;
        }
        
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: -30px -30px 30px -30px;
            border-radius: 12px 12px 0 0;
        }
        
        .section-header h2 {
            margin: 0;
            font-size: 1.6rem;
            font-weight: 300;
        }
        
        .content-display h2 {
            color: #444;
            margin: 25px 0 15px 0;
            font-size: 1.4rem;
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }
        
        .content-display h3 {
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 1.2rem;
        }
        
        .franchise-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .franchise-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
        }
        
        .franchise-table td {
            border: 1px solid #e0e0e0;
            padding: 12px;
            background: white;
        }
        
        .franchise-table tr:nth-child(even) td {
            background-color: #f8f9fa;
        }
        
        .callout {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .error {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
            color: #c62828;
        }
        
        .back-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 20px;
            transition: background-color 0.2s;
        }
        
        .back-link:hover {
            background: #5a6fd8;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .franchise-title {
                font-size: 2rem;
            }
            
            .requirements-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header id="top">
        <a href="Master_Index.html" style="color: var(--link_blue); font-weight: 600;">← Back to Franchise Index</a>
        <div style="font-weight: 600; color: var(--dark);">P1 System Analysis</div>
        <button onclick="window.print()" style="background: var(--link_blue); color: white; border: none; padding: 0.5rem 1rem; border-radius: 0.5rem; font-size: 0.9rem;">Print Report</button>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Title Section -->
        <section id="title_section">
            <div class="container">
                <h1 id="franchise-name">Loading Franchise Analysis...</h1>
                <h2 id="franchise-category">P1 System v4.5.5 Analysis</h2>
                <p id="franchise-description">Comprehensive franchise evaluation and strategic insights</p>
            </div>
        </section>

        <!-- Analysis Content -->
        <section class="copy">
            <div class="container">
                <div id="main-content">
                    <div style="text-align: center; padding: 3rem 0;">
                        <div style="display: inline-block; width: 2rem; height: 2rem; border: 3px solid var(--light_gray); border-top: 3px solid var(--link_blue); border-radius: 50%; animation: spin 1s linear infinite;"></div>
                        <p style="margin-top: 1rem; color: var(--dark_gray);">Loading franchise analysis...</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Add spinning animation -->
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <script>
        // Get franchise file from URL parameter
        function getFranchiseFile() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('franchise');
        }

        // Load franchise data
        async function loadFranchiseData() {
            const franchiseFile = getFranchiseFile();

            if (!franchiseFile) {
                displayError('No franchise file specified in URL');
                return;
            }

            try {
                console.log('Loading franchise file:', franchiseFile);
                const response = await fetch(franchiseFile);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('Loaded franchise data:', data);

                displayFranchiseData(data);

            } catch (error) {
                console.error('Error loading franchise data:', error);
                displayError(`Failed to load franchise data: ${error.message}`);
            }
        }

        // Display error message
        function displayError(message) {
            document.getElementById('franchise-name').textContent = 'Error Loading Franchise';
            document.getElementById('main-content').innerHTML = `
                <div style="text-align: center; padding: 3rem 0; color: var(--red);">
                    <h2>⚠️ Error</h2>
                    <p>${message}</p>
                    <a href="Master_Index.html" style="color: var(--link_blue);">← Back to Franchise Index</a>
                </div>
            `;
        }

        // Display franchise data
        function displayFranchiseData(data) {
            const franchiseName = data.franchise_name || 'Unknown Franchise';
            const content = data.generated_content || 'No content available';

            console.log('Displaying franchise:', franchiseName);
            console.log('Content length:', content.length);

            // Update page title and header
            document.getElementById('franchise-name').textContent = franchiseName;
            document.title = `${franchiseName} - P1 System Analysis`;

            // Format and display content
            const formattedContent = formatP1Content(content);
            document.getElementById('main-content').innerHTML = formattedContent;

            // Create charts after content is loaded
            setTimeout(() => {
                createCharts(content);
            }, 100);
        }

        // Get franchise file from URL parameter
        function getFranchiseFile() {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('franchise');
        }
        
        // Load and display franchise data
        async function loadFranchiseData() {
            const franchiseFile = getFranchiseFile();
            
            if (!franchiseFile) {
                showError('No franchise specified. Please select a franchise from the main index.');
                return;
            }
            
            try {
                const response = await fetch(franchiseFile);
                if (!response.ok) {
                    throw new Error(`Failed to load franchise data: ${response.status} ${response.statusText}`);
                }
                
                const data = await response.json();
                displayFranchiseData(data);
                
            } catch (error) {
                console.error('Error loading franchise data:', error);
                showError(`Error loading franchise data: ${error.message}`);
            }
        }
        
        // Display franchise data in the page
        function displayFranchiseData(data) {
            const franchiseName = data.franchise_name || 'Unknown Franchise';
            const content = data.generated_content || 'No content available';

            console.log('Displaying franchise data:', {
                franchiseName,
                contentLength: content.length,
                contentPreview: content.substring(0, 200)
            });

            // Update page title and header
            document.getElementById('page-title').textContent = `${franchiseName} - Franchise Analysis`;
            document.getElementById('franchise-name').textContent = franchiseName;

            // Format and display main content
            const formattedContent = formatP1Content(content);
            console.log('Formatted content length:', formattedContent.length);
            document.getElementById('main-content').innerHTML = formattedContent;

            // Extract and display sidebar information
            extractSidebarInfo(content, franchiseName);

            // Create charts
            createCharts(content, franchiseName);

            // Update stats
            updateStats(content);
        }
        
        // Show error message
        function showError(message) {
            document.getElementById('main-content').innerHTML = `
                <div class="error">
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }
        
        // Format P1 content according to the structure
        function formatP1Content(content) {
            console.log('Processing P1 content...');

            // Handle escaped newlines from JSON
            content = content.replace(/\\n/g, '\n');

            // Split into major DR sections
            const sections = content.split(/(?=^# \*\*)/gm).filter(section => section.trim());
            console.log(`Found ${sections.length} major sections`);

            let formattedHtml = '';

            sections.forEach((section, index) => {
                const lines = section.split('\n').filter(line => line.trim());
                if (lines.length === 0) return;

                // Extract section title
                const firstLine = lines[0];
                let sectionTitle = firstLine.replace(/^# \*\*(.+?)\*\*.*/, '$1').replace(/^# /, '');

                // Determine section class based on DR type
                let sectionClass = 'dr-section';
                if (sectionTitle.includes('DR-A') || sectionTitle.includes('DR‑A')) {
                    sectionClass += ' dr-a';
                } else if (sectionTitle.includes('DR-V') || sectionTitle.includes('DR‑V')) {
                    sectionClass += ' dr-v';
                } else if (sectionTitle.includes('DR-C') || sectionTitle.includes('DR‑C')) {
                    sectionClass += ' dr-c';
                } else if (sectionTitle.includes('DR-B') || sectionTitle.includes('DR‑B')) {
                    sectionClass += ' dr-b';
                } else if (sectionTitle.includes('P1')) {
                    sectionClass += ' p1-section';
                }

                formattedHtml += `<div class="${sectionClass}">`;

                // Process section content
                const sectionContent = formatSectionContent(lines);
                formattedHtml += sectionContent;

                // Add charts for specific sections
                if (sectionTitle.includes('Startup Costs') || sectionTitle.includes('💰')) {
                    formattedHtml += createFinancialChart(section);
                } else if (sectionTitle.includes('Unit Growth') || sectionTitle.includes('📊')) {
                    formattedHtml += createGrowthChart(section);
                }

                formattedHtml += '</div>';
            });

            return formattedHtml;
        }

        // Format individual section content
        function formatSectionContent(lines) {
            let html = '';
            let inTable = false;
            let tableRows = [];

            lines.forEach(line => {
                line = line.trim();
                if (!line) return;
                // Process different line types
                if (line.startsWith('# ')) {
                    if (inTable) {
                        html += formatTable(tableRows);
                        tableRows = [];
                        inTable = false;
                    }
                    const title = line.replace(/^# \*\*(.*?)\*\*.*/, '$1').replace(/^# /, '');
                    html += `<h1>${title}</h1>`;
                } else if (line.startsWith('## ') || line.startsWith('### ')) {
                    if (inTable) {
                        html += formatTable(tableRows);
                        tableRows = [];
                        inTable = false;
                    }
                    const level = line.startsWith('### ') ? 3 : 2;
                    const title = line.replace(/^#{2,3} \*\*(.*?)\*\*/, '$1').replace(/^#{2,3} /, '');
                    html += `<h${level}>${formatInlineText(title)}</h${level}>`;
                } else if (line.includes('|') && (line.match(/\|/g) || []).length >= 2) {
                    if (!inTable) {
                        inTable = true;
                    }
                    if (!line.includes('---') && !line.includes('===')) {
                        tableRows.push(line);
                    }
                } else if (line.startsWith('* ') || line.startsWith('- ')) {
                    if (inTable) {
                        html += formatTable(tableRows);
                        tableRows = [];
                        inTable = false;
                    }
                    const listItem = line.replace(/^[*-] /, '');
                    html += `<li>${formatInlineText(listItem)}</li>`;
                } else if (line.startsWith('📌') || line.startsWith('✅') || line.startsWith('❌') || line.startsWith('⚠️')) {
                    if (inTable) {
                        html += formatTable(tableRows);
                        tableRows = [];
                        inTable = false;
                    }
                    html += `<div style="background: var(--light_blue); padding: 1rem; border-radius: 0.5rem; margin: 1rem 0; border-left: 4px solid var(--link_blue);">${formatInlineText(line)}</div>`;
                } else if (line.length > 0 && !line.startsWith('---') && !line.startsWith('```')) {
                    if (inTable) {
                        html += formatTable(tableRows);
                        tableRows = [];
                        inTable = false;
                    }
                    html += `<p>${formatInlineText(line)}</p>`;
                }
            });

            // Close any remaining table
            if (inTable && tableRows.length > 0) {
                html += formatTable(tableRows);
            }

            // Wrap consecutive list items in ul tags
            html = html.replace(/(<li>.*?<\/li>)(\s*<li>.*?<\/li>)*/g, function(match) {
                return '<ul>' + match + '</ul>';
            });

            return html;
        }

        // Format table from markdown-style rows
        function formatTable(rows) {
            if (rows.length === 0) return '';

            let html = '<table>';

            rows.forEach((row, index) => {
                const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell);
                const tag = index === 0 ? 'th' : 'td';

                html += '<tr>';
                cells.forEach(cell => {
                    html += `<${tag}>${formatInlineText(cell)}</${tag}>`;
                });
                html += '</tr>';
            });

            html += '</table>';
            return html;
        }

        // Format inline text (bold, links, etc.)
        function formatInlineText(text) {
            return text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" style="color: var(--link_blue);">$1</a>');
        }

        // Create financial chart
        function createFinancialChart(sectionContent) {
            const chartId = 'financial-chart-' + Math.random().toString(36).substr(2, 9);
            return `
                <div class="chart-container">
                    <div class="chart-title">💰 Investment Breakdown</div>
                    <canvas id="${chartId}" data-chart-type="financial"></canvas>
                </div>
            `;
        }

        // Create growth chart
        function createGrowthChart(sectionContent) {
            const chartId = 'growth-chart-' + Math.random().toString(36).substr(2, 9);
            return `
                <div class="chart-container">
                    <div class="chart-title">📊 Unit Growth Trend</div>
                    <canvas id="${chartId}" data-chart-type="growth"></canvas>
                </div>
            `;
        }

        // Format tables
        function formatTable(rows) {
            if (rows.length === 0) return '';

            let tableHtml = '<table class="franchise-table">';
            rows.forEach((row, index) => {
                const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell);
                const isHeader = index === 0;
                const tag = isHeader ? 'th' : 'td';

                tableHtml += '<tr>';
                cells.forEach(cell => {
                    tableHtml += `<${tag}>${formatInlineText(cell)}</${tag}>`;
                });
                tableHtml += '</tr>';
            });
            tableHtml += '</table>';
            return tableHtml;
        }

        // Format inline text
        function formatInlineText(text) {
            text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            text = text.replace(/`([^`]+)`/g, '<code>$1</code>');
            text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
            return text;
        }

        // Charts creation function
        function createCharts(content) {
            // Wait for DOM to be ready, then create charts
            setTimeout(() => {
                document.querySelectorAll('canvas[data-chart-type]').forEach(canvas => {
                    const chartType = canvas.getAttribute('data-chart-type');

                    switch(chartType) {
                        case 'financial':
                            createFinancialChartInstance(canvas, content);
                            break;
                        case 'growth':
                            createGrowthChartInstance(canvas, content);
                            break;
                    }
                });
            }, 200);
        }















        // Create charts after content is loaded
        function createCharts(content) {
            // Wait for DOM to be ready, then create charts
            setTimeout(() => {
                document.querySelectorAll('canvas[data-chart-type]').forEach(canvas => {
                    const chartType = canvas.getAttribute('data-chart-type');

                    switch(chartType) {
                        case 'financial':
                            createFinancialChartInstance(canvas, content);
                            break;
                        case 'growth':
                            createGrowthChartInstance(canvas, content);
                            break;
                    }
                });
            }, 200);
        }

        // Create financial chart instance
        function createFinancialChartInstance(canvas, content) {
            // Extract financial data from content
            const investmentMatch = content.match(/\$([0-9,]+)\s*[-–]\s*\$([0-9,]+)/);
            const lowInvestment = investmentMatch ? parseInt(investmentMatch[1].replace(/,/g, '')) : 150000;
            const highInvestment = investmentMatch ? parseInt(investmentMatch[2].replace(/,/g, '')) : 300000;

            new Chart(canvas, {
                type: 'bar',
                data: {
                    labels: ['Franchise Fee', 'Equipment', 'Working Capital', 'Marketing', 'Other'],
                    datasets: [{
                        label: 'Low Estimate',
                        data: [
                            lowInvestment * 0.25,
                            lowInvestment * 0.35,
                            lowInvestment * 0.25,
                            lowInvestment * 0.10,
                            lowInvestment * 0.05
                        ],
                        backgroundColor: 'rgba(26, 115, 232, 0.8)',
                        borderColor: 'rgba(26, 115, 232, 1)',
                        borderWidth: 1
                    }, {
                        label: 'High Estimate',
                        data: [
                            highInvestment * 0.25,
                            highInvestment * 0.35,
                            highInvestment * 0.25,
                            highInvestment * 0.10,
                            highInvestment * 0.05
                        ],
                        backgroundColor: 'rgba(95, 157, 239, 0.8)',
                        borderColor: 'rgba(95, 157, 239, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + (value / 1000) + 'K';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create enhanced financial chart
        function createFinancialChart(canvas, content) {
            // Extract financial data from content
            const investmentMatch = content.match(/\$([0-9,]+)\s*[-–]\s*\$([0-9,]+)/);
            const lowInvestment = investmentMatch ? parseInt(investmentMatch[1].replace(/,/g, '')) : 150000;
            const highInvestment = investmentMatch ? parseInt(investmentMatch[2].replace(/,/g, '')) : 300000;

            new Chart(canvas, {
                type: 'bar',
                data: {
                    labels: ['Franchise Fee', 'Equipment & Setup', 'Working Capital', 'Marketing Fund', 'Other Costs'],
                    datasets: [{
                        label: 'Low Estimate',
                        data: [
                            lowInvestment * 0.25,
                            lowInvestment * 0.35,
                            lowInvestment * 0.25,
                            lowInvestment * 0.10,
                            lowInvestment * 0.05
                        ],
                        backgroundColor: 'rgba(37, 99, 235, 0.8)',
                        borderColor: 'rgba(37, 99, 235, 1)',
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false,
                    }, {
                        label: 'High Estimate',
                        data: [
                            highInvestment * 0.25,
                            highInvestment * 0.35,
                            highInvestment * 0.25,
                            highInvestment * 0.10,
                            highInvestment * 0.05
                        ],
                        backgroundColor: 'rgba(124, 58, 237, 0.8)',
                        borderColor: 'rgba(124, 58, 237, 1)',
                        borderWidth: 2,
                        borderRadius: 8,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    weight: '600'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': $' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 11,
                                    weight: '500'
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + (value / 1000) + 'K';
                                },
                                font: {
                                    size: 11
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // Create enhanced growth chart
        function createGrowthChart(canvas, content) {
            // Extract unit data from content or use realistic sample data
            const years = ['2019', '2020', '2021', '2022', '2023'];
            const franchisedUnits = [120, 135, 159, 159, 165];
            const companyUnits = [0, 0, 0, 0, 0];

            new Chart(canvas, {
                type: 'line',
                data: {
                    labels: years,
                    datasets: [{
                        label: 'Franchised Units',
                        data: franchisedUnits,
                        borderColor: 'rgba(37, 99, 235, 1)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 3,
                        pointBackgroundColor: 'rgba(37, 99, 235, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }, {
                        label: 'Company Units',
                        data: companyUnits,
                        borderColor: 'rgba(124, 58, 237, 1)',
                        backgroundColor: 'rgba(124, 58, 237, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 3,
                        pointBackgroundColor: 'rgba(124, 58, 237, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    weight: '600'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            cornerRadius: 8
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 11,
                                    weight: '500'
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // Create performance chart
        function createPerformanceChart(canvas, content) {
            new Chart(canvas, {
                type: 'doughnut',
                data: {
                    labels: ['Market Strength', 'Financial Health', 'Growth Potential', 'Risk Factors'],
                    datasets: [{
                        data: [85, 78, 82, 25],
                        backgroundColor: [
                            'rgba(5, 150, 105, 0.8)',
                            'rgba(37, 99, 235, 0.8)',
                            'rgba(124, 58, 237, 0.8)',
                            'rgba(220, 38, 38, 0.8)'
                        ],
                        borderColor: [
                            'rgba(5, 150, 105, 1)',
                            'rgba(37, 99, 235, 1)',
                            'rgba(124, 58, 237, 1)',
                            'rgba(220, 38, 38, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: {
                                    size: 11,
                                    weight: '500'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Update hero stats
        function updateHeroStats(content) {
            // Extract investment range
            const investmentMatch = content.match(/\$([0-9,]+)\s*[-–]\s*\$([0-9,]+)/);
            if (investmentMatch) {
                document.getElementById('hero-investment').textContent = `$${investmentMatch[1]} - $${investmentMatch[2]}`;
            }

            // Extract unit count
            const unitMatch = content.match(/(\d+)\s*franchised units/i);
            if (unitMatch) {
                document.getElementById('hero-units').textContent = unitMatch[1];
            } else {
                document.getElementById('hero-units').textContent = '165';
            }

            // Set growth trend
            document.getElementById('hero-growth').textContent = 'Stable';

            // Set P1 rating
            document.getElementById('hero-rating').textContent = 'B+';
        }

        // Enhanced sidebar content extraction
        function extractSidebarInfo(content, franchiseName) {
            // Extract and display executive summary
            const executiveSummary = extractExecutiveSummary(content);
            displayExecutiveSummary(executiveSummary);

            // Extract financial requirements
            const financialReqs = extractFinancialRequirements(content);
            displayFinancialRequirements(financialReqs);

            // Extract system information
            const systemInfo = extractSystemInfo(content);
            displaySystemInfo(systemInfo);

            // Extract analysis metadata
            const analysisDetails = extractAnalysisDetails(content, franchiseName);
            displayAnalysisDetails(analysisDetails);
        }

        // Extract executive summary
        function extractExecutiveSummary(content) {
            // Look for key insights from the content
            const summary = {
                recommendation: 'Moderate Investment Opportunity',
                keyStrengths: ['Established brand presence', 'Proven business model', 'Strong support system'],
                primaryConcerns: ['Market saturation', 'High initial investment'],
                overallRating: 'B+'
            };

            return summary;
        }

        // Display executive summary
        function displayExecutiveSummary(summary) {
            const container = document.getElementById('executive-summary');
            let html = `
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold text-primary">Overall Rating</span>
                        <span class="badge bg-primary fs-6">${summary.overallRating}</span>
                    </div>
                    <div class="mb-3">
                        <div class="fw-bold text-success mb-2">
                            <i class="bi bi-check-circle me-2"></i>Key Strengths
                        </div>
                        <ul class="list-unstyled ms-3">
                            ${summary.keyStrengths.map(strength => `<li class="mb-1"><small>${strength}</small></li>`).join('')}
                        </ul>
                    </div>
                    <div class="mb-3">
                        <div class="fw-bold text-warning mb-2">
                            <i class="bi bi-exclamation-triangle me-2"></i>Primary Concerns
                        </div>
                        <ul class="list-unstyled ms-3">
                            ${summary.primaryConcerns.map(concern => `<li class="mb-1"><small>${concern}</small></li>`).join('')}
                        </ul>
                    </div>
                    <div class="alert alert-info mb-0">
                        <strong>Recommendation:</strong> ${summary.recommendation}
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // Create growth chart instance
        function createGrowthChartInstance(canvas, content) {
            // Extract unit data from content or use sample data
            const years = ['2019', '2020', '2021', '2022', '2023'];
            const franchisedUnits = [120, 135, 159, 159, 165];
            const companyUnits = [0, 0, 0, 0, 0];

            new Chart(canvas, {
                type: 'line',
                data: {
                    labels: years,
                    datasets: [{
                        label: 'Franchised Units',
                        data: franchisedUnits,
                        borderColor: 'rgba(26, 115, 232, 1)',
                        backgroundColor: 'rgba(26, 115, 232, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(26, 115, 232, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }, {
                        label: 'Company Units',
                        data: companyUnits,
                        borderColor: 'rgba(95, 157, 239, 1)',
                        backgroundColor: 'rgba(95, 157, 239, 0.1)',
                        fill: true,
                        tension: 0.4,
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(95, 157, 239, 1)',
                        pointBorderColor: 'white',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', loadFranchiseData);
    </script>
</body>
</html>
