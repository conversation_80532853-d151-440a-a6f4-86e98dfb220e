<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <title>Franchise Analysis Master Index</title>

    <!-- Custom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Base styles from sample template */
        * {margin: 0;padding: 0;box-sizing: border-box;}
        html, body {height: 100%;font-family: "Inter", sans-serif;font-optical-sizing: auto;color: var(--dark_gray);background-color: #FFF;font-weight: 500;line-height: 1.2;font-size: 13px;}
        body {line-height: 1.2;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;display: flex;flex-direction: column;}
        img, picture, video, canvas, svg {display: block;max-width: 100%;image-rendering: auto;transform: translateZ(0);}
        input, button, textarea, select {font: inherit;}
        a {text-decoration: none;color: inherit;}
        p {margin-bottom: 1em;}
        ul, ol {list-style: none;}
        li {padding-bottom: 1em;}
        h1, h2, h3, h4{color: var(--dark);font-weight: 700;margin-bottom: 1em;}
        h1 {font-weight: 800;font-size: 1.5rem;line-height: 1.2;}
        h2, h1.h2, strong.h2 {font-size: 1.25rem;}
        h3 {font-size: 1rem;}
        h4 {font-size: .875rem;}

        /* Color variables */
        :root {
            --dark: #282828;
            --menu_gray: #7A7A7A;
            --dark_gray: #888;
            --light_gray: #DCDCDC;
            --light: #F7F7F7;
            --light_red: #ff6d7e;
            --red: #E1253A;
            --button_red: #cc1209;
            --link_blue: #1A73E8;
            --blue: #5F9DEF;
            --light_blue: #ddeafc;
            --light_green: rgb(191 255 183 / 50%);
            --border_green: #0b9f00;
        }

        /* Layout */
        .container {width: 87.5rem;max-width: 100%;padding: 0 1.5rem;margin: 0 auto;}
        .container_medium{max-width: 75rem;width: 100%;margin: auto;}
        .container_small{max-width: 35rem;width: 100%;margin: auto;}

        /* Cards */
        .white_shell {border-radius: 1rem;overflow: hidden;background: #FFF;box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.2);position: relative;transition: all .2s ease-in;}
        .white_shell:not(article) {padding: 1rem;}
        .white_shell:hover {transform: translateY(-2px);box-shadow: 0 .5rem 2rem rgba(0, 0, 0, 0.25);}

        /* Header */
        header#top {order: 1;background-color: #FFF;height: 4rem;min-height: 4rem;display: flex;flex-direction: row;align-items: center;justify-content: space-between;padding: 0 1rem;box-sizing: border-box;position: fixed;inset: 0 0 auto;z-index: 4;}

        /* Main content */
        main {order: 2;padding-top: 4rem;}

        /* Title section */
        #title_section {padding: 2rem 0;background: linear-gradient(135deg, var(--link_blue) 0%, var(--blue) 100%);color: white;}
        #title_section h1{margin: 0;font-size: 2.5rem;}
        #title_section h1 + h2 {font-size: 1.2rem;font-weight: 500;color: rgba(255,255,255,0.9);margin: 0;}
        #title_section p {line-height: 1.4;color: rgba(255,255,255,0.8);}
        #title_section h1 + :is(p, h2) {padding-top: .5rem;}

        /* Stats section */
        .stats_section {padding: 3rem 0;background: var(--light);}
        .stats_grid {display: grid;grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));gap: 2rem;}
        .stat_card {background: white;padding: 2rem;border-radius: 1rem;text-align: center;box-shadow: 0 .25rem 1rem rgba(0, 0, 0, 0.1);}
        .stat_number {font-size: 2.5rem;font-weight: 800;color: var(--link_blue);margin-bottom: 0.5rem;}
        .stat_label {color: var(--dark_gray);font-weight: 600;}

        /* Franchise grid */
        .franchise_grid {display: grid;grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));gap: 2rem;padding: 3rem 0;}
        .franchise_card {background: white;border-radius: 1rem;padding: 2rem;box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.1);transition: all 0.3s ease;}
        .franchise_card:hover {transform: translateY(-5px);box-shadow: 0 .5rem 2rem rgba(0, 0, 0, 0.2);}
        .franchise_name {font-size: 1.25rem;font-weight: 700;color: var(--dark);margin-bottom: 1rem;}
        .franchise_description {color: var(--dark_gray);margin-bottom: 1.5rem;line-height: 1.4;}
        .franchise_buttons {display: flex;gap: 1rem;}
        .btn {padding: 0.75rem 1.5rem;border-radius: 0.5rem;font-weight: 600;text-decoration: none;display: inline-block;transition: all 0.2s ease;}
        .btn_primary {background: var(--link_blue);color: white;}
        .btn_primary:hover {background: var(--blue);}
        .btn_secondary {background: var(--light);color: var(--dark);border: 1px solid var(--light_gray);}
        .btn_secondary:hover {background: var(--light_gray);}

        /* Footer */
        footer {background: var(--dark);color: white;padding: 3rem 0;margin-top: auto;}
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .stats {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .stats h2 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .franchise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .franchise-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .franchise-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        .franchise-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        .franchise-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .franchise-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.9em;
            transition: background-color 0.2s;
            cursor: pointer;
            border: none;
        }
        .franchise-link:hover {
            background: #5a6fd8;
        }
        .download-link {
            background: #28a745;
        }
        .download-link:hover {
            background: #218838;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 95%;
            max-width: 1200px;
            height: 90%;
            position: relative;
            overflow: hidden;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .modal-header h2 {
            margin: 0;
            font-size: 1.5em;
        }
        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        .close:hover {
            opacity: 0.7;
        }
        .modal-body {
            padding: 20px;
            height: calc(100% - 80px);
            overflow-y: auto;
            background: #f8f9fa;
        }
        .content-display {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            line-height: 1.6;
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: -30px -30px 30px -30px;
            border-radius: 8px 8px 0 0;
        }
        .section-header h1 {
            margin: 0;
            font-size: 1.8em;
            font-weight: 300;
        }
        .content-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #eee;
        }
        .content-section:last-child {
            border-bottom: none;
        }
        .content-display h1 {
            color: #333;
            margin: 30px 0 20px 0;
            font-size: 1.6em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .content-display h2 {
            color: #444;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }
        .content-display h3 {
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }
        .content-display h4 {
            color: #666;
            margin: 15px 0 8px 0;
            font-size: 1.1em;
            font-weight: 600;
        }
        .franchise-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        .franchise-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.95em;
        }
        .franchise-table td {
            border: 1px solid #e0e0e0;
            padding: 12px;
            text-align: left;
            background: white;
        }
        .franchise-table tr:nth-child(even) td {
            background-color: #f8f9fa;
        }
        .franchise-table tr:hover td {
            background-color: #e3f2fd;
        }
        .content-display ul {
            margin: 15px 0;
            padding-left: 0;
        }
        .content-display li {
            list-style: none;
            margin: 8px 0;
            padding: 8px 0 8px 30px;
            position: relative;
            border-left: 3px solid #667eea;
            background: #f8f9fa;
            border-radius: 0 4px 4px 0;
        }
        .content-display li:before {
            content: "▶";
            color: #667eea;
            position: absolute;
            left: 10px;
            font-size: 0.8em;
        }
        .callout {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
            font-weight: 500;
        }
        .content-display p {
            margin: 12px 0;
            text-align: justify;
        }
        .content-display code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d63384;
            border: 1px solid #e0e0e0;
        }
        .content-display pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e0e0e0;
        }
        .content-display strong {
            color: #333;
            font-weight: 600;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header id="top">
        <div style="font-weight: 700; color: var(--link_blue);">🏢 Franchise Analysis</div>
        <div style="font-weight: 600; color: var(--dark);">P1 System v4.5.5</div>
        <div style="font-size: 0.9rem; color: var(--dark_gray);">August 22, 2025</div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Title Section -->
        <section id="title_section">
            <div class="container">
                <h1>Franchise Analysis Master Index</h1>
                <h2>P1 System v4.5.5 Analysis Platform</h2>
                <p>Comprehensive franchise evaluation and strategic insights powered by advanced AI analysis. Generated on August 22, 2025 at 05:37 PM</p>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats_section">
            <div class="container">
                <div class="stats_grid">
                    <div class="stat_card">
                        <div class="stat_number">3</div>
                        <div class="stat_label">Franchises Analyzed</div>
                    </div>
                    <div class="stat_card">
                        <div class="stat_number">100%</div>
                        <div class="stat_label">Analysis Success Rate</div>
                    </div>
                    <div class="stat_card">
                        <div class="stat_number">v4.5.5</div>
                        <div class="stat_label">P1 System Version</div>
                    </div>
                    <div class="stat_card">
                        <div class="stat_number">50+</div>
                        <div class="stat_label">Data Points per Analysis</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Franchise Grid -->
        <section>
            <div class="container">
                <div class="franchise_grid">
                    <div class="franchise_card">
                        <div class="franchise_name">101 Mobility</div>
                        <div class="franchise_description">
                            Complete P1 System analysis with financial insights, growth trends, and strategic evaluation.
                            Comprehensive franchise evaluation powered by advanced AI analysis.
                        </div>
                        <div class="franchise_buttons">
                            <a href="franchise-display.html?franchise=franchise_outputs/101_Mobility.json" class="btn btn_primary">
                                👁️ View Analysis
                            </a>
                            <a href="franchise_outputs/101_Mobility.json" class="btn btn_secondary" download>
                                📄 Download JSON
                            </a>
                        </div>
                    </div>
                    <div class="franchise_card">
                        <div class="franchise_name">BODYBAR FRANCHISING, LLC</div>
                        <div class="franchise_description">
                            Complete P1 System analysis with financial insights, growth trends, and strategic evaluation.
                            Comprehensive franchise evaluation powered by advanced AI analysis.
                        </div>
                        <div class="franchise_buttons">
                            <a href="franchise-display.html?franchise=franchise_outputs/BODYBAR_FRANCHISING,_LLC.json" class="btn btn_primary">
                                👁️ View Analysis
                            </a>
                            <a href="franchise_outputs/BODYBAR_FRANCHISING,_LLC.json" class="btn btn_secondary" download>
                                📄 Download JSON
                            </a>
                        </div>
                    </div>
                    <div class="franchise_card">
                        <div class="franchise_name">NutritionHQ Franchising, LLC</div>
                        <div class="franchise_description">
                            Complete P1 System analysis with financial insights, growth trends, and strategic evaluation.
                            Comprehensive franchise evaluation powered by advanced AI analysis.
                        </div>
                        <div class="franchise_buttons">
                            <a href="franchise-display.html?franchise=franchise_outputs/NutritionHQ_Franchising,_LLC.json" class="btn btn_primary">
                                👁️ View Analysis
                            </a>
                            <a href="franchise_outputs/NutritionHQ_Franchising,_LLC.json" class="btn btn_secondary" download>
                                📄 Download JSON
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; align-items: center;">
                <div>
                    <h3 style="color: white; margin-bottom: 1rem;">Franchise Analysis Platform</h3>
                    <p style="color: rgba(255,255,255,0.8); margin-bottom: 0.5rem;">
                        Advanced AI-powered franchise analysis using the P1 System v4.5.5 methodology.
                    </p>
                    <p style="color: rgba(255,255,255,0.6); margin-bottom: 0;">
                        Click "View Analysis" for comprehensive dashboard or "Download JSON" for raw data.
                    </p>
                </div>
                <div style="text-align: right;">
                    <div style="margin-bottom: 1rem;">
                        <span style="background: var(--link_blue); color: white; padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.8rem; margin-right: 0.5rem;">P1 System</span>
                        <span style="background: var(--border_green); color: white; padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.8rem;">v4.5.5</span>
                    </div>
                    <p style="color: rgba(255,255,255,0.6); margin-bottom: 0; font-size: 0.9rem;">
                        Generated {datetime.now().strftime('%B %d, %Y')}
                    </p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>