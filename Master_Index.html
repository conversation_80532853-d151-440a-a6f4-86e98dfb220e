<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <title>Franchise Analysis Master Index</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Corporate-grade design variables */
        :root {
            --primary-blue: #1A73E8;
            --secondary-blue: #5F9DEF;
            --light-blue: #E8F0FE;
            --dark-gray: #2D3748;
            --medium-gray: #4A5568;
            --light-gray: #E2E8F0;
            --success-green: #48BB78;
            --warning-orange: #ED8936;
            --danger-red: #F56565;
            --white: #FFFFFF;
            --background-light: #F7FAFC;
        }

        /* Base typography and layout */
        body {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            line-height: 1.6;
            color: var(--dark-gray);
            background-color: var(--background-light);
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            color: var(--dark-gray);
        }

        .text-primary-custom {
            color: var(--primary-blue) !important;
        }

        .bg-primary-custom {
            background-color: var(--primary-blue) !important;
        }

        .bg-secondary-custom {
            background-color: var(--secondary-blue) !important;
        }

        /* Corporate header styling */
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand-custom {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
        }

        /* Hero section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        /* Stats section */
        .stats-section {
            background: var(--white);
            padding: 4rem 0;
            margin-top: -2rem;
            position: relative;
            z-index: 3;
        }

        .stat-card {
            background: var(--white);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid var(--light-gray);
            transition: all 0.3s ease;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            color: var(--medium-gray);
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Franchise cards */
        .franchise-card {
            background: var(--white);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid var(--light-gray);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .franchise-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border-color: var(--primary-blue);
        }

        .franchise-name {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--dark-gray);
            margin-bottom: 1rem;
        }

        .franchise-description {
            color: var(--medium-gray);
            margin-bottom: 1.5rem;
            line-height: 1.6;
            flex-grow: 1;
        }

        .btn-custom-primary {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            font-weight: 600;
            padding: 0.6rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .btn-custom-primary:hover {
            background: var(--secondary-blue);
            border-color: var(--secondary-blue);
            transform: translateY(-1px);
        }

        .btn-custom-secondary {
            background: var(--white);
            border: 2px solid var(--light-gray);
            color: var(--medium-gray);
            font-weight: 600;
            padding: 0.6rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .btn-custom-secondary:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
            transform: translateY(-1px);
        }

        /* Ensure buttons fit side by side */
        .btn-sm {
            padding: 0.5rem 0.8rem;
            font-size: 0.85rem;
        }

        @media (max-width: 576px) {
            .btn-custom-primary, .btn-custom-secondary {
                padding: 0.5rem 0.7rem;
                font-size: 0.8rem;
            }
        }

        /* Footer */
        .footer-custom {
            background: linear-gradient(135deg, var(--dark-gray) 0%, var(--medium-gray) 100%);
            color: white;
            padding: 3rem 0;
            margin-top: 4rem;
        }

        .footer-custom h5 {
            color: white;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .footer-custom p {
            color: rgba(255,255,255,0.8);
            margin-bottom: 0.5rem;
        }

        .badge-custom {
            background: var(--primary-blue);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            margin-right: 0.5rem;
            font-weight: 600;
        }

        .badge-success-custom {
            background: var(--success-green);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hero-section {
                padding: 2rem 0;
            }

            .stats-section {
                padding: 2rem 0;
            }

            .franchise-name {
                font-size: 1.2rem;
            }
        }


    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand navbar-brand-custom" href="#">
                🏢 Franchise Analysis Platform
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <small>P1 System v4.5.5 • August 23, 2025</small>
                </span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-content">
            <div class="container">
                <div class="row justify-content-center text-center">
                    <div class="col-lg-8">
                        <h1 class="display-4 fw-bold mb-4">Franchise Analysis Master Index</h1>
                        <h2 class="h4 mb-4 opacity-75">P1 System v4.5.5 Analysis Platform</h2>
                        <p class="lead opacity-75">
                            Comprehensive franchise evaluation and strategic insights powered by advanced AI analysis.
                            Corporate-grade analytics with contextual data visualization and P1 Snapshot methodology.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row g-4" id="statsContainer">
                <!-- Stats will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Franchise Grid -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-12 text-center">
                    <h2 class="display-6 fw-bold text-primary-custom mb-3">Available Franchise Analyses</h2>
                    <p class="lead text-muted">
                        Explore comprehensive P1 System evaluations with interactive charts and strategic insights
                    </p>
                </div>
            </div>
            <div class="row g-4" id="franchiseContainer">
                <!-- Franchise cards will be dynamically generated -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-custom">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h5>Franchise Analysis Platform</h5>
                    <p class="mb-2">
                        Advanced AI-powered franchise analysis using the P1 System v4.5.5 methodology.
                        Corporate-grade analytics with contextual data visualization.
                    </p>
                    <p class="mb-0">
                        Click "View Analysis" for comprehensive dashboard or "Download JSON" for raw data.
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                    <div class="mb-3">
                        <span class="badge-custom">P1 System</span>
                        <span class="badge-custom badge-success-custom">v4.5.5</span>
                    </div>
                    <p class="mb-0">
                        <small>Generated <span id="currentDate"></span></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Dynamic Content Generation -->
    <script>
        // Set current date
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Franchise data - this will be dynamically loaded
        const franchiseFiles = [
            '101_Mobility.json',
            'BODYBAR_FRANCHISING,_LLC.json',
            'NutritionHQ_Franchising,_LLC.json'
        ];

        // Generate stats
        function generateStats() {
            const statsContainer = document.getElementById('statsContainer');
            const stats = [
                { number: franchiseFiles.length, label: 'Franchises Analyzed' },
                { number: '100%', label: 'Analysis Success Rate' },
                { number: 'v4.5.5', label: 'P1 System Version' },
                { number: '50+', label: 'Data Points per Analysis' }
            ];

            stats.forEach(stat => {
                const statCard = `
                    <div class="col-lg-3 col-md-6">
                        <div class="stat-card">
                            <div class="stat-number">${stat.number}</div>
                            <div class="stat-label">${stat.label}</div>
                        </div>
                    </div>
                `;
                statsContainer.innerHTML += statCard;
            });
        }

        // Generate franchise cards with unique descriptions
        function generateFranchiseCards() {
            const franchiseContainer = document.getElementById('franchiseContainer');

            // Define unique descriptions for each franchise
            const franchiseDescriptions = {
                '101_Mobility.json': {
                    name: '101 Mobility',
                    description: 'Specialized mobility equipment franchise serving aging populations with stairlifts, ramps, and accessibility solutions. Operates retail outlets with installation services, targeting the growing senior market with recurring revenue from maintenance contracts and equipment rentals.'
                },
                'BODYBAR_FRANCHISING,_LLC.json': {
                    name: 'BODYBAR Franchising',
                    description: 'Innovative fitness franchise combining strength training with flexibility work using portable equipment. Features low overhead studio model with group classes, personal training, and corporate wellness programs targeting health-conscious professionals and active adults.'
                },
                'NutritionHQ_Franchising,_LLC.json': {
                    name: 'NutritionHQ Franchising',
                    description: 'Comprehensive nutrition and wellness franchise offering meal planning, supplement sales, and health coaching services. Combines retail nutrition products with personalized consultation services, targeting health-conscious consumers and weight management clients.'
                }
            };

            franchiseFiles.forEach(file => {
                const franchiseInfo = franchiseDescriptions[file];
                const franchiseName = franchiseInfo ? franchiseInfo.name : file.replace('.json', '').replace(/_/g, ' ');
                const description = franchiseInfo ? franchiseInfo.description : 'Complete P1 System analysis with financial insights, growth trends, and strategic evaluation.';

                const franchiseCard = `
                    <div class="col-lg-4 col-md-6">
                        <div class="franchise-card">
                            <div class="franchise-name">${franchiseName}</div>
                            <div class="franchise-description">
                                ${description}
                            </div>
                            <div class="d-flex gap-2">
                                <a href="franchise-detail.html?franchise=${file}" class="btn btn-custom-primary btn-sm flex-fill">
                                    👁️ View Analysis
                                </a>
                                <a href="franchise_outputs/${file}" class="btn btn-custom-secondary btn-sm flex-fill" download>
                                    📄 Download JSON
                                </a>
                            </div>
                        </div>
                    </div>
                `;
                franchiseContainer.innerHTML += franchiseCard;
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            generateStats();
            generateFranchiseCards();
        });
    </script>
</body>
</html>