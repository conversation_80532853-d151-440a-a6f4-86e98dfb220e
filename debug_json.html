<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug JSON Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .content-preview { background: #f5f5f5; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>Debug JSON Loading</h1>
    
    <div class="debug-section">
        <h2>1. Test JSON File Loading</h2>
        <button onclick="testJsonLoad()">Load 101 Mobility JSON</button>
        <div id="json-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. Test Content Parsing</h2>
        <button onclick="testContentParsing()">Parse Content</button>
        <div id="parsing-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. Test Regex Patterns</h2>
        <button onclick="testRegexPatterns()">Test Patterns</button>
        <div id="regex-result"></div>
    </div>

    <script>
        let loadedContent = '';
        
        async function testJsonLoad() {
            const resultDiv = document.getElementById('json-result');
            resultDiv.innerHTML = 'Loading...';
            
            try {
                const response = await fetch('franchise_outputs/101_Mobility.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                loadedContent = data.generated_content;
                
                resultDiv.innerHTML = `
                    <h3>✅ JSON Loaded Successfully</h3>
                    <p><strong>Franchise Name:</strong> ${data.franchise_name}</p>
                    <p><strong>Content Length:</strong> ${data.generated_content.length} characters</p>
                    <p><strong>First 200 characters:</strong></p>
                    <div class="content-preview">
                        <pre>${data.generated_content.substring(0, 200)}...</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ Error Loading JSON</h3>
                    <p>${error.message}</p>
                `;
            }
        }
        
        function testContentParsing() {
            const resultDiv = document.getElementById('parsing-result');
            
            if (!loadedContent) {
                resultDiv.innerHTML = '<p>Please load JSON first</p>';
                return;
            }
            
            // Test newline replacement
            const processedContent = loadedContent.replace(/\\n/g, '\n');
            const sections = processedContent.split(/(?=^# )/gm).filter(section => section.trim());
            
            resultDiv.innerHTML = `
                <h3>Content Parsing Results</h3>
                <p><strong>Original content has \\n:</strong> ${loadedContent.includes('\\n')}</p>
                <p><strong>After replacement has newlines:</strong> ${processedContent.includes('\n')}</p>
                <p><strong>Number of sections found:</strong> ${sections.length}</p>
                <p><strong>First section preview:</strong></p>
                <div class="content-preview">
                    <pre>${sections[0] ? sections[0].substring(0, 300) : 'No sections found'}...</pre>
                </div>
            `;
        }
        
        function testRegexPatterns() {
            const resultDiv = document.getElementById('regex-result');
            
            if (!loadedContent) {
                resultDiv.innerHTML = '<p>Please load JSON first</p>';
                return;
            }
            
            const processedContent = loadedContent.replace(/\\n/g, '\n');
            
            // Test various regex patterns
            const tests = [
                {
                    name: 'Total Investment Range',
                    pattern: /\*\*Total Investment Range\*\* \| \*\*([^*]+)\*\*/,
                    content: processedContent
                },
                {
                    name: 'Initial Franchise Fee',
                    pattern: /\*\*Initial Franchise Fee\*\* \| \*\*([^*]+)\*\*/,
                    content: processedContent
                },
                {
                    name: 'Headers (# pattern)',
                    pattern: /^# /gm,
                    content: processedContent
                },
                {
                    name: 'Bold text pattern',
                    pattern: /\*\*(.*?)\*\*/g,
                    content: processedContent
                }
            ];
            
            let html = '<h3>Regex Pattern Tests</h3>';
            tests.forEach(test => {
                const matches = test.content.match(test.pattern);
                html += `
                    <p><strong>${test.name}:</strong> ${matches ? `Found ${matches.length} matches` : 'No matches'}</p>
                    ${matches ? `<div class="content-preview"><pre>${JSON.stringify(matches.slice(0, 3), null, 2)}</pre></div>` : ''}
                `;
            });
            
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
