#!/usr/bin/env python3
"""
Run the complete franchise processing system for all franchises in the CSV.

This script processes all franchises without any limit. Use this for production runs.
For testing, use test_single_franchise.py or franchise_automation.py with a limit.
"""

from franchise_automation import FranchiseProcessor
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Run the complete franchise processing workflow for all franchises."""
    try:
        logger.info("🚀 Starting FULL franchise processing workflow")
        logger.info("⚠️  This will process ALL franchises in the CSV file")
        
        # Ask for confirmation
        response = input("Do you want to proceed with processing ALL franchises? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            logger.info("Processing cancelled by user")
            return
        
        # Initialize processor
        processor = FranchiseProcessor()
        
        # Run complete process without limit
        processor.run_complete_process(limit=None)
        
        logger.info("✅ Full processing completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("❌ Processing interrupted by user")
    except Exception as e:
        logger.error(f"❌ Error in full processing: {str(e)}")
        raise

if __name__ == "__main__":
    main()
