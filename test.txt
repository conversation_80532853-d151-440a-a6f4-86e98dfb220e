Create a franchise analysis web application with the following specifications:

**Base Template & Design:**
- Use `/Users/<USER>/Desktop/FV_AI/sample_template.html` as the foundation design template for all franchise detail pages
- Implement Bootstrap CSS framework with consistent color scheme throughout the application
- Ensure corporate-grade design aesthetics that rival commercial franchise platforms

**Master Index Page:**
- Recreate `Master_Index.html` as the main landing page
- Include navigation links to all individual franchise pages
- Generate links dynamically based on JSON files found in the `franchise_outputs` folder

**Individual Franchise Pages:**
- Create dynamic HTML pages for each franchise using their respective JSON files from the `franchise_outputs` folder
- Each page must extend the `sample_template.html` base template
- Include a "Back to Home" link that navigates to `Master_Index.html`
- Populate all content dynamically from the corresponding JSON file

**Data Visualization:**
- Implement Chart.js for all data visualizations
- Ensure chart data accuracy by extracting values directly from the respective JSON files
- Place charts contextually within their relevant content sections (not grouped separately)
- Use intelligent judgment to determine when charts enhance data comprehension vs. when tabular data is more appropriate

**Content Guidelines:**
- Follow the "P1 Snapshot" guidelines from the prompting guide and output reference for content structure and presentation
- Ensure proper formatting and display of all JSON data
- Maintain consistent visual hierarchy and readability
- Use charts strategically to make complex data easily digestible

**Technical Requirements:**
- All content must be dynamically generated from JSON data sources
- Maintain responsive design principles
- Ensure cross-browser compatibility
- Implement clean, semantic HTML structure
- After completion, open the `Master_Index.html` page automatically in a browser running the local server
