Create a franchise analysis web application with the following specifications:

**Base Template & Design:**
- Use `/Users/<USER>/Desktop/FV_AI/sample_template.html` as the foundation design template for all franchise detail pages
- Implement Bootstrap CSS framework with consistent color scheme throughout the application
- Ensure corporate-grade design aesthetics that rival commercial franchise platforms

**Master Index Page:**
- Recreate `Master_Index.html` as the main landing page
- Include navigation links to all individual franchise pages
- Generate links dynamically based on JSON files found in the `franchise_outputs` folder

**Individual Franchise Pages:**
- Create dynamic HTML pages for each franchise using their respective JSON files from the `franchise_outputs` folder
- Each page must extend the `sample_template.html` base template
- Include a "Back to Home" link that navigates to `Master_Index.html`
- Populate all content dynamically from the corresponding JSON file

**Data Visualization:**
- Implement Chart.js for all data visualizations
- Ensure chart data accuracy by extracting values directly from the respective JSON files
- Place charts contextually within their relevant content sections (not grouped separately)
- Use intelligent judgment to determine when charts enhance data comprehension vs. when tabular data is more appropriate

**Content Guidelines:**
- Follow the "P1 Snapshot" guidelines from the prompting guide and output reference for content structure and presentation
- Ensure proper formatting and display of all JSON data
- Maintain consistent visual hierarchy and readability
- Use charts strategically to make complex data easily digestible

**Technical Requirements:**
- All content must be dynamically generated from JSON data sources
- Maintain responsive design principles
- Ensure cross-browser compatibility
- Implement clean, semantic HTML structure
- After completion, open the `Master_Index.html` page automatically in a browser running the local server

Please review the `franchise-base-template.html` file and ensure it maintains complete design consistency with the original `sample_template.html` foundation. Specifically:

1. **Color Scheme Alignment**: Verify that all CSS custom properties (`:root` variables) match the color palette established in `sample_template.html`, including:
   - Primary and secondary blues
   - Gray scale variations
   - Background colors
   - Text colors
   - Accent colors

2. **Typography Consistency**: Ensure the font family, weights, and sizing hierarchy match exactly:
   - Inter font family usage
   - Font weight progression (300, 400, 500, 600, 700, 800)
   - Heading size relationships (h1, h2, h3, h4)
   - Line height and letter spacing

3. **Layout Structure**: Verify that the base template follows the same structural patterns:
   - Container widths and max-widths
   - Padding and margin conventions
   - Grid system usage
   - Component spacing standards

4. **Visual Elements**: Check that design components maintain consistency:
   - Border radius values
   - Box shadow specifications
   - Transition timing and easing
   - Hover state behaviors
   - Button styling and sizing

5. **Responsive Behavior**: Ensure breakpoints and mobile adaptations align with the original template's responsive design patterns.

Compare the base template against the established design system from `sample_template.html` and identify any deviations that need correction to maintain the corporate-grade aesthetic consistency across the franchise analysis platform.