#!/usr/bin/env python3
"""
Franchise Output Generation System

This script processes franchise data from a CSV file and generates structured outputs
for each franchise using AI. The system combines franchise datapoints with formatting
guidelines to produce consistent, professional outputs.

Requirements:
- Python 3.x with pandas, requests, json, webbrowser
- Novita AI API access
- Input files: CSV datapoints, prompting guide, output reference
"""

import pandas as pd
import requests
import json
import os
import re
import webbrowser
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('franchise_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FranchiseProcessor:
    """Main class for processing franchise data and generating AI outputs."""
    
    def __init__(self):
        """Initialize the processor with API configuration and file paths."""
        # Novita AI API Configuration
        self.api_base_url = "https://api.novita.ai/openai"
        self.api_key = "sk_X6Dj_jBoPp7vqxSdD0OdjRcHkb5mn-bWupFmoMD2UX4"
        self.model = "openai/gpt-oss-20b"
        
        # File paths
        self.csv_file = "Datapoints - Actual_Datapoints.csv"
        self.prompting_guide_file = "Prompting_Guide_v4.5.5 Final.md"
        self.output_reference_file = "Output-Reference.md"
        self.output_dir = "franchise_outputs"
        self.master_index_file = "Master_Index.html"
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Load reference files
        self.prompting_guide = self._load_file(self.prompting_guide_file)
        self.output_reference = self._load_file(self.output_reference_file)
        
        logger.info("FranchiseProcessor initialized successfully")
    
    def _load_file(self, filepath: str) -> str:
        """Load content from a file."""
        try:
            with open(filepath, 'r', encoding='utf-8') as file:
                content = file.read()
            logger.info(f"Loaded file: {filepath}")
            return content
        except FileNotFoundError:
            logger.error(f"File not found: {filepath}")
            raise
        except Exception as e:
            logger.error(f"Error loading file {filepath}: {str(e)}")
            raise
    
    def _sanitize_filename(self, name: str) -> str:
        """Sanitize franchise name for use as filename."""
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)
        # Remove extra spaces and replace with underscores
        sanitized = re.sub(r'\s+', '_', sanitized)
        # Remove leading/trailing underscores
        sanitized = sanitized.strip('_')
        # Limit length
        if len(sanitized) > 100:
            sanitized = sanitized[:100]
        return sanitized

    def load_franchise_data(self) -> pd.DataFrame:
        """Load and parse the franchise datapoints CSV file."""
        try:
            logger.info(f"Loading franchise data from {self.csv_file}")

            # Load with multi-level headers
            df = pd.read_csv(self.csv_file, header=[0, 1])

            # Create unique column names by combining both levels when needed
            new_columns = []
            level0_cols = df.columns.get_level_values(0)
            level1_cols = df.columns.get_level_values(1)

            for i, (l0, l1) in enumerate(zip(level0_cols, level1_cols)):
                if pd.isna(l1) or str(l1).startswith('Unnamed'):
                    # Use level 0 if level 1 is unnamed
                    new_columns.append(str(l0) if not pd.isna(l0) else f'Column_{i}')
                else:
                    # Use level 1, but add level 0 prefix if there are duplicates
                    base_name = str(l1)
                    if level1_cols.tolist().count(l1) > 1:
                        # Add level 0 as prefix for duplicates
                        prefix = str(l0) if not pd.isna(l0) and not str(l0).startswith('Unnamed') else f'Group_{i}'
                        new_columns.append(f"{prefix}_{base_name}")
                    else:
                        new_columns.append(base_name)

            df.columns = new_columns

            logger.info(f"Loaded {len(df)} franchise records with {len(df.columns)} columns")

            # Basic data validation
            if df.empty:
                raise ValueError("CSV file is empty")

            # Find the franchise name column
            franchise_name_col = None
            for col in df.columns:
                if 'Franchise Name' in col:
                    franchise_name_col = col
                    break

            if franchise_name_col is None:
                logger.error("'Franchise Name' column not found in CSV")
                logger.info(f"Available columns: {list(df.columns[:10])}")
                raise ValueError("Required 'Franchise Name' column not found")

            logger.info(f"Using franchise name column: {franchise_name_col}")

            # Remove rows where Franchise Name is empty
            initial_count = len(df)
            df = df.dropna(subset=[franchise_name_col])
            # Filter out empty strings after converting to string and stripping
            df = df[df[franchise_name_col].astype(str).str.strip() != '']
            final_count = len(df)

            # Store the franchise name column for later use
            self.franchise_name_column = franchise_name_col

            if final_count < initial_count:
                logger.info(f"Removed {initial_count - final_count} rows with empty franchise names")

            logger.info(f"Final dataset: {final_count} valid franchise records")
            return df

        except FileNotFoundError:
            logger.error(f"CSV file not found: {self.csv_file}")
            raise
        except Exception as e:
            logger.error(f"Error loading CSV data: {str(e)}")
            raise

    def extract_franchise_datapoints(self, row: pd.Series) -> Dict[str, Any]:
        """Extract and structure datapoints for a single franchise."""
        datapoints = {}
        
        # Convert pandas Series to dictionary, handling NaN values
        for column, value in row.items():
            if pd.isna(value):
                datapoints[column] = None
            else:
                datapoints[column] = str(value)
        
        return datapoints

    def construct_ai_prompt(self, franchise_datapoints: Dict[str, Any]) -> str:
        """Construct the AI prompt using franchise data, prompting guide, and output reference."""
        
        # Format franchise datapoints for the prompt
        datapoints_text = "\n".join([
            f"**{key}:** {value}" for key, value in franchise_datapoints.items() 
            if value is not None and str(value).strip() != ""
        ])
        
        prompt = f"""
You are an expert franchise analyst tasked with creating a comprehensive P1 Snapshot following the v4.5.5 system guidelines.

## FRANCHISE DATAPOINTS:
{datapoints_text}

## FORMATTING GUIDELINES:
{self.prompting_guide}

## OUTPUT REFERENCE (Style and Structure Example):
{self.output_reference}

## INSTRUCTIONS:
Using the franchise datapoints provided above, create a complete P1 Snapshot that follows the exact structure and formatting guidelines specified in the Prompting Guide v4.5.5. 

Your output must include:
1. Complete DR-A section (data extraction)
2. Complete DR-V section (validation signals)
3. Complete DR-C section (strategic implications)
4. Complete DR-B section (smart questions)
5. Complete P1 Snapshot (13 sections)
6. Layer 5 JSON metadata

Follow the style, tone, and structure demonstrated in the Output Reference. Ensure all sections are present and properly formatted according to the guidelines.

Generate the complete analysis now:
"""
        
        return prompt

    def call_novita_ai(self, prompt: str, max_retries: int = 3) -> str:
        """Call Novita AI API to generate content."""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 8000,
            "temperature": 0.7
        }

        for attempt in range(max_retries):
            try:
                logger.info(f"Calling Novita AI API (attempt {attempt + 1}/{max_retries})")
                response = requests.post(
                    f"{self.api_base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=120
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result['choices'][0]['message']['content']
                    logger.info("AI content generated successfully")
                    return content
                else:
                    logger.error(f"API call failed with status {response.status_code}: {response.text}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    else:
                        raise Exception(f"API call failed after {max_retries} attempts")

            except requests.exceptions.Timeout:
                logger.error(f"API call timed out (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise Exception("API call timed out after multiple attempts")

            except Exception as e:
                logger.error(f"Error calling AI API: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
                else:
                    raise

    def save_franchise_output(self, franchise_name: str, content: str) -> str:
        """Save generated content to a JSON file."""
        try:
            # Sanitize filename
            safe_name = self._sanitize_filename(franchise_name)
            filename = f"{safe_name}.json"
            filepath = os.path.join(self.output_dir, filename)

            # Structure the content as JSON
            output_data = {
                "franchise_name": franchise_name,
                "generated_content": content,
                "generation_timestamp": datetime.now().isoformat(),
                "system_version": "v4.5.5"
            }

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as file:
                json.dump(output_data, file, indent=2, ensure_ascii=False)

            logger.info(f"Saved output for {franchise_name} to {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving output for {franchise_name}: {str(e)}")
            raise

    def process_single_franchise(self, row: pd.Series) -> Optional[str]:
        """Process a single franchise and generate its output."""
        try:
            # Use the dynamic franchise name column
            franchise_name = row.get(self.franchise_name_column, 'Unknown')
            logger.info(f"Processing franchise: {franchise_name}")

            # Extract datapoints
            datapoints = self.extract_franchise_datapoints(row)

            # Construct AI prompt
            prompt = self.construct_ai_prompt(datapoints)

            # Generate content using AI
            content = self.call_novita_ai(prompt)

            # Save output
            filepath = self.save_franchise_output(franchise_name, content)

            return filepath

        except Exception as e:
            franchise_name = row.get(self.franchise_name_column, 'Unknown')
            logger.error(f"Error processing franchise {franchise_name}: {str(e)}")
            return None

    def process_all_franchises(self, limit: Optional[int] = None) -> List[str]:
        """Process all franchises in the CSV file."""
        try:
            # Load data
            df = self.load_franchise_data()

            # Apply limit if specified
            if limit:
                df = df.head(limit)
                logger.info(f"Processing limited to {limit} franchises")

            processed_files = []
            total_franchises = len(df)

            logger.info(f"Starting to process {total_franchises} franchises")

            for index, row in df.iterrows():
                try:
                    logger.info(f"Processing {index + 1}/{total_franchises}")
                    filepath = self.process_single_franchise(row)
                    if filepath:
                        processed_files.append(filepath)

                    # Add small delay to avoid overwhelming the API
                    time.sleep(1)

                except Exception as e:
                    franchise_name = row.get('Franchise Name', f'Row {index}')
                    logger.error(f"Failed to process {franchise_name}: {str(e)}")
                    continue

            logger.info(f"Successfully processed {len(processed_files)} out of {total_franchises} franchises")
            return processed_files

        except Exception as e:
            logger.error(f"Error in process_all_franchises: {str(e)}")
            raise

    def generate_master_index(self, processed_files: List[str]) -> str:
        """Generate the Master_Index.html file with links to all franchise outputs."""
        try:
            logger.info("Generating Master_Index.html")

            # Extract franchise names and filenames
            franchise_links = []
            for filepath in processed_files:
                filename = os.path.basename(filepath)
                # Try to extract franchise name from the JSON file
                try:
                    with open(filepath, 'r', encoding='utf-8') as file:
                        data = json.load(file)
                        franchise_name = data.get('franchise_name', filename.replace('.json', ''))
                except:
                    franchise_name = filename.replace('.json', '').replace('_', ' ')

                franchise_links.append({
                    'name': franchise_name,
                    'filename': filename,
                    'filepath': os.path.join(self.output_dir, filename)
                })

            # Sort by franchise name
            franchise_links.sort(key=lambda x: x['name'])

            # Generate HTML content using sample template structure
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <title>Franchise Analysis Master Index</title>

    <!-- Custom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Base styles from sample template */
        * {{margin: 0;padding: 0;box-sizing: border-box;}}
        html, body {{height: 100%;font-family: "Inter", sans-serif;font-optical-sizing: auto;color: var(--dark_gray);background-color: #FFF;font-weight: 500;line-height: 1.2;font-size: 13px;}}
        body {{line-height: 1.2;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;display: flex;flex-direction: column;}}
        img, picture, video, canvas, svg {{display: block;max-width: 100%;image-rendering: auto;transform: translateZ(0);}}
        input, button, textarea, select {{font: inherit;}}
        a {{text-decoration: none;color: inherit;}}
        p {{margin-bottom: 1em;}}
        ul, ol {{list-style: none;}}
        li {{padding-bottom: 1em;}}
        h1, h2, h3, h4{{color: var(--dark);font-weight: 700;margin-bottom: 1em;}}
        h1 {{font-weight: 800;font-size: 1.5rem;line-height: 1.2;}}
        h2, h1.h2, strong.h2 {{font-size: 1.25rem;}}
        h3 {{font-size: 1rem;}}
        h4 {{font-size: .875rem;}}

        /* Color variables */
        :root {{
            --dark: #282828;
            --menu_gray: #7A7A7A;
            --dark_gray: #888;
            --light_gray: #DCDCDC;
            --light: #F7F7F7;
            --light_red: #ff6d7e;
            --red: #E1253A;
            --button_red: #cc1209;
            --link_blue: #1A73E8;
            --blue: #5F9DEF;
            --light_blue: #ddeafc;
            --light_green: rgb(191 255 183 / 50%);
            --border_green: #0b9f00;
        }}

        /* Layout */
        .container {{width: 87.5rem;max-width: 100%;padding: 0 1.5rem;margin: 0 auto;}}
        .container_medium{{max-width: 75rem;width: 100%;margin: auto;}}
        .container_small{{max-width: 35rem;width: 100%;margin: auto;}}

        /* Cards */
        .white_shell {{border-radius: 1rem;overflow: hidden;background: #FFF;box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.2);position: relative;transition: all .2s ease-in;}}
        .white_shell:not(article) {{padding: 1rem;}}
        .white_shell:hover {{transform: translateY(-2px);box-shadow: 0 .5rem 2rem rgba(0, 0, 0, 0.25);}}

        /* Header */
        header#top {{order: 1;background-color: #FFF;height: 4rem;min-height: 4rem;display: flex;flex-direction: row;align-items: center;justify-content: space-between;padding: 0 1rem;box-sizing: border-box;position: fixed;inset: 0 0 auto;z-index: 4;}}

        /* Main content */
        main {{order: 2;padding-top: 4rem;}}

        /* Title section */
        #title_section {{padding: 2rem 0;background: linear-gradient(135deg, var(--link_blue) 0%, var(--blue) 100%);color: white;}}
        #title_section h1{{margin: 0;font-size: 2.5rem;}}
        #title_section h1 + h2 {{font-size: 1.2rem;font-weight: 500;color: rgba(255,255,255,0.9);margin: 0;}}
        #title_section p {{line-height: 1.4;color: rgba(255,255,255,0.8);}}
        #title_section h1 + :is(p, h2) {{padding-top: .5rem;}}

        /* Stats section */
        .stats_section {{padding: 3rem 0;background: var(--light);}}
        .stats_grid {{display: grid;grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));gap: 2rem;}}
        .stat_card {{background: white;padding: 2rem;border-radius: 1rem;text-align: center;box-shadow: 0 .25rem 1rem rgba(0, 0, 0, 0.1);}}
        .stat_number {{font-size: 2.5rem;font-weight: 800;color: var(--link_blue);margin-bottom: 0.5rem;}}
        .stat_label {{color: var(--dark_gray);font-weight: 600;}}

        /* Franchise grid */
        .franchise_grid {{display: grid;grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));gap: 2rem;padding: 3rem 0;}}
        .franchise_card {{background: white;border-radius: 1rem;padding: 2rem;box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.1);transition: all 0.3s ease;}}
        .franchise_card:hover {{transform: translateY(-5px);box-shadow: 0 .5rem 2rem rgba(0, 0, 0, 0.2);}}
        .franchise_name {{font-size: 1.25rem;font-weight: 700;color: var(--dark);margin-bottom: 1rem;}}
        .franchise_description {{color: var(--dark_gray);margin-bottom: 1.5rem;line-height: 1.4;}}
        .franchise_buttons {{display: flex;gap: 1rem;}}
        .btn {{padding: 0.75rem 1.5rem;border-radius: 0.5rem;font-weight: 600;text-decoration: none;display: inline-block;transition: all 0.2s ease;}}
        .btn_primary {{background: var(--link_blue);color: white;}}
        .btn_primary:hover {{background: var(--blue);}}
        .btn_secondary {{background: var(--light);color: var(--dark);border: 1px solid var(--light_gray);}}
        .btn_secondary:hover {{background: var(--light_gray);}}

        /* Footer */
        footer {{background: var(--dark);color: white;padding: 3rem 0;margin-top: auto;}}
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }}
        .stats {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }}
        .stats h2 {{
            margin: 0 0 10px 0;
            color: #333;
        }}
        .franchise-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }}
        .franchise-card {{
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }}
        .franchise-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }}
        .franchise-name {{
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }}
        .franchise-buttons {{
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }}
        .franchise-link {{
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.9em;
            transition: background-color 0.2s;
            cursor: pointer;
            border: none;
        }}
        .franchise-link:hover {{
            background: #5a6fd8;
        }}
        .download-link {{
            background: #28a745;
        }}
        .download-link:hover {{
            background: #218838;
        }}
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }}
        .modal-content {{
            background-color: #fefefe;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 95%;
            max-width: 1200px;
            height: 90%;
            position: relative;
            overflow: hidden;
        }}
        .modal-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .modal-header h2 {{
            margin: 0;
            font-size: 1.5em;
        }}
        .close {{
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }}
        .close:hover {{
            opacity: 0.7;
        }}
        .modal-body {{
            padding: 20px;
            height: calc(100% - 80px);
            overflow-y: auto;
            background: #f8f9fa;
        }}
        .content-display {{
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            line-height: 1.6;
        }}
        .section-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: -30px -30px 30px -30px;
            border-radius: 8px 8px 0 0;
        }}
        .section-header h1 {{
            margin: 0;
            font-size: 1.8em;
            font-weight: 300;
        }}
        .content-section {{
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #eee;
        }}
        .content-section:last-child {{
            border-bottom: none;
        }}
        .content-display h1 {{
            color: #333;
            margin: 30px 0 20px 0;
            font-size: 1.6em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }}
        .content-display h2 {{
            color: #444;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }}
        .content-display h3 {{
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }}
        .content-display h4 {{
            color: #666;
            margin: 15px 0 8px 0;
            font-size: 1.1em;
            font-weight: 600;
        }}
        .franchise-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }}
        .franchise-table th {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.95em;
        }}
        .franchise-table td {{
            border: 1px solid #e0e0e0;
            padding: 12px;
            text-align: left;
            background: white;
        }}
        .franchise-table tr:nth-child(even) td {{
            background-color: #f8f9fa;
        }}
        .franchise-table tr:hover td {{
            background-color: #e3f2fd;
        }}
        .content-display ul {{
            margin: 15px 0;
            padding-left: 0;
        }}
        .content-display li {{
            list-style: none;
            margin: 8px 0;
            padding: 8px 0 8px 30px;
            position: relative;
            border-left: 3px solid #667eea;
            background: #f8f9fa;
            border-radius: 0 4px 4px 0;
        }}
        .content-display li:before {{
            content: "▶";
            color: #667eea;
            position: absolute;
            left: 10px;
            font-size: 0.8em;
        }}
        .callout {{
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
            font-weight: 500;
        }}
        .content-display p {{
            margin: 12px 0;
            text-align: justify;
        }}
        .content-display code {{
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d63384;
            border: 1px solid #e0e0e0;
        }}
        .content-display pre {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e0e0e0;
        }}
        .content-display strong {{
            color: #333;
            font-weight: 600;
        }}
        .loading {{
            text-align: center;
            padding: 50px;
            color: #666;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }}
    </style>
</head>
<body>
    <!-- Header -->
    <header id="top">
        <div style="font-weight: 700; color: var(--link_blue);">🏢 Franchise Analysis</div>
        <div style="font-weight: 600; color: var(--dark);">P1 System v4.5.5</div>
        <div style="font-size: 0.9rem; color: var(--dark_gray);">{datetime.now().strftime('%B %d, %Y')}</div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Title Section -->
        <section id="title_section">
            <div class="container">
                <h1>Franchise Analysis Master Index</h1>
                <h2>P1 System v4.5.5 Analysis Platform</h2>
                <p>Comprehensive franchise evaluation and strategic insights powered by advanced AI analysis. Generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</p>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="stats_section">
            <div class="container">
                <div class="stats_grid">
                    <div class="stat_card">
                        <div class="stat_number">{len(franchise_links)}</div>
                        <div class="stat_label">Franchises Analyzed</div>
                    </div>
                    <div class="stat_card">
                        <div class="stat_number">100%</div>
                        <div class="stat_label">Analysis Success Rate</div>
                    </div>
                    <div class="stat_card">
                        <div class="stat_number">v4.5.5</div>
                        <div class="stat_label">P1 System Version</div>
                    </div>
                    <div class="stat_card">
                        <div class="stat_number">50+</div>
                        <div class="stat_label">Data Points per Analysis</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Franchise Grid -->
        <section>
            <div class="container">
                <div class="franchise_grid">"""

            # Add franchise cards
            for link in franchise_links:
                # Use relative path from Master_Index.html to the JSON files
                relative_path = f"franchise_outputs/{link['filename']}"
                display_url = f"franchise-display.html?franchise={relative_path}"
                html_content += f"""
                    <div class="franchise_card">
                        <div class="franchise_name">{link['name']}</div>
                        <div class="franchise_description">
                            Complete P1 System analysis with financial insights, growth trends, and strategic evaluation.
                            Comprehensive franchise evaluation powered by advanced AI analysis.
                        </div>
                        <div class="franchise_buttons">
                            <a href="{display_url}" class="btn btn_primary">
                                👁️ View Analysis
                            </a>
                            <a href="{relative_path}" class="btn btn_secondary" download>
                                📄 Download JSON
                            </a>
                        </div>
                    </div>"""

            html_content += """
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; align-items: center;">
                <div>
                    <h3 style="color: white; margin-bottom: 1rem;">Franchise Analysis Platform</h3>
                    <p style="color: rgba(255,255,255,0.8); margin-bottom: 0.5rem;">
                        Advanced AI-powered franchise analysis using the P1 System v4.5.5 methodology.
                    </p>
                    <p style="color: rgba(255,255,255,0.6); margin-bottom: 0;">
                        Click "View Analysis" for comprehensive dashboard or "Download JSON" for raw data.
                    </p>
                </div>
                <div style="text-align: right;">
                    <div style="margin-bottom: 1rem;">
                        <span style="background: var(--link_blue); color: white; padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.8rem; margin-right: 0.5rem;">P1 System</span>
                        <span style="background: var(--border_green); color: white; padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.8rem;">v4.5.5</span>
                    </div>
                    <p style="color: rgba(255,255,255,0.6); margin-bottom: 0; font-size: 0.9rem;">
                        Generated {datetime.now().strftime('%B %d, %Y')}
                    </p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>"""

            # Save HTML file
            with open(self.master_index_file, 'w', encoding='utf-8') as file:
                file.write(html_content)

            logger.info(f"Master index generated: {self.master_index_file}")
            return self.master_index_file

        except Exception as e:
            logger.error(f"Error generating master index: {str(e)}")
            raise



    def launch_browser(self, filepath: str) -> None:
        """Launch the default browser to display the master index."""
        try:
            # Convert to absolute path
            abs_path = os.path.abspath(filepath)
            file_url = f"file://{abs_path}"

            logger.info(f"Opening browser with: {file_url}")
            webbrowser.open(file_url)
            logger.info("Browser launched successfully")

        except Exception as e:
            logger.error(f"Error launching browser: {str(e)}")
            # Don't raise - this is not critical to the main process

    def run_complete_process(self, limit: Optional[int] = None) -> None:
        """Run the complete franchise processing workflow."""
        try:
            logger.info("=== Starting Complete Franchise Processing Workflow ===")

            # Process all franchises
            processed_files = self.process_all_franchises(limit=limit)

            if not processed_files:
                logger.error("No franchises were successfully processed")
                return

            # Generate master index
            index_file = self.generate_master_index(processed_files)

            # Launch browser
            self.launch_browser(index_file)

            logger.info("=== Franchise Processing Workflow Completed Successfully ===")
            logger.info(f"Processed {len(processed_files)} franchises")
            logger.info(f"Master index: {index_file}")
            logger.info(f"Output directory: {self.output_dir}")

        except Exception as e:
            logger.error(f"Error in complete process: {str(e)}")
            raise

if __name__ == "__main__":
    processor = FranchiseProcessor()
    logger.info("Franchise automation system started")

    # Run with a limit for testing (remove limit for full processing)
    processor.run_complete_process(limit=3)  # Process first 3 franchises for testing
