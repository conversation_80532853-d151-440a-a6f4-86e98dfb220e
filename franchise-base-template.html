<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <title>{{FRANCHISE_NAME}} - Franchise Analysis</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Corporate-grade design variables */
        :root {
            --primary-blue: #1A73E8;
            --secondary-blue: #5F9DEF;
            --light-blue: #E8F0FE;
            --dark-gray: #2D3748;
            --medium-gray: #4A5568;
            --light-gray: #E2E8F0;
            --success-green: #48BB78;
            --warning-orange: #ED8936;
            --danger-red: #F56565;
            --white: #FFFFFF;
            --background-light: #F7FAFC;
        }

        /* Base typography and layout */
        body {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            line-height: 1.6;
            color: var(--dark-gray);
            background-color: var(--background-light);
            padding-top: 76px; /* Account for fixed navbar */
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            color: var(--dark-gray);
        }

        .text-primary-custom {
            color: var(--primary-blue) !important;
        }

        .bg-primary-custom {
            background-color: var(--primary-blue) !important;
        }

        .bg-secondary-custom {
            background-color: var(--secondary-blue) !important;
        }

        /* Navigation */
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand-custom {
            font-weight: 700;
            font-size: 1.3rem;
            color: white !important;
        }

        .btn-back {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-back:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-1px);
        }

        /* Content sections */
        .content-section {
            background: var(--white);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid var(--light-gray);
        }

        .section-header {
            border-bottom: 3px solid var(--primary-blue);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-gray);
            margin: 0;
        }

        /* Tables */
        .table-custom {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .table-custom thead th {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 1rem;
        }

        .table-custom tbody td {
            padding: 1rem;
            border-color: var(--light-gray);
        }

        .table-custom tbody tr:hover {
            background-color: var(--light-blue);
        }

        /* Charts */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
            background: var(--white);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* Badges and indicators */
        .badge-custom {
            background: var(--primary-blue);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge-success-custom {
            background: var(--success-green);
        }

        .badge-warning-custom {
            background: var(--warning-orange);
        }

        .badge-danger-custom {
            background: var(--danger-red);
        }

        /* Alert boxes */
        .alert-custom {
            border-radius: 0.5rem;
            border: none;
            padding: 1rem 1.5rem;
            margin: 1rem 0;
        }

        .alert-info-custom {
            background: var(--light-blue);
            color: var(--primary-blue);
            border-left: 4px solid var(--primary-blue);
        }

        .alert-warning-custom {
            background: #FFF3CD;
            color: var(--warning-orange);
            border-left: 4px solid var(--warning-orange);
        }

        .alert-danger-custom {
            background: #F8D7DA;
            color: var(--danger-red);
            border-left: 4px solid var(--danger-red);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content-section {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }
            
            .chart-container {
                height: 300px;
                padding: 0.5rem;
            }
            
            .section-title {
                font-size: 1.3rem;
            }
        }

        /* Loading states */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(26, 115, 232, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Markdown content styling */
        .markdown-content h1 {
            font-size: 1.8rem;
            margin: 2rem 0 1rem 0;
            color: var(--dark-gray);
            border-bottom: 2px solid var(--primary-blue);
            padding-bottom: 0.5rem;
        }

        .markdown-content h2 {
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
            color: var(--medium-gray);
            border-left: 4px solid var(--primary-blue);
            padding-left: 1rem;
        }

        .markdown-content h3 {
            font-size: 1.3rem;
            margin: 1.2rem 0 0.8rem 0;
            color: var(--medium-gray);
        }

        .markdown-content h4 {
            font-size: 1.1rem;
            margin: 1rem 0 0.5rem 0;
            color: var(--medium-gray);
            font-weight: 600;
        }

        .markdown-content p {
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .markdown-content ul, .markdown-content ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        .markdown-content li {
            margin-bottom: 0.5rem;
        }

        .markdown-content table {
            width: 100%;
            margin: 1.5rem 0;
            border-collapse: collapse;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .markdown-content table th {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .markdown-content table td {
            padding: 1rem;
            border-bottom: 1px solid var(--light-gray);
        }

        .markdown-content table tr:nth-child(even) {
            background-color: var(--background-light);
        }

        .markdown-content table tr:hover {
            background-color: var(--light-blue);
        }

        .markdown-content strong {
            font-weight: 700;
            color: var(--dark-gray);
        }

        .markdown-content em {
            font-style: italic;
            color: var(--medium-gray);
        }

        .markdown-content code {
            background: var(--background-light);
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: var(--danger-red);
        }

        .markdown-content pre {
            background: var(--background-light);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-content blockquote {
            border-left: 4px solid var(--primary-blue);
            padding-left: 1rem;
            margin: 1rem 0;
            color: var(--medium-gray);
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand navbar-brand-custom" href="Master_Index.html">
                🏢 Franchise Analysis
            </a>
            <div class="navbar-nav ms-auto">
                <a href="Master_Index.html" class="btn btn-back">
                    ← Back to Home
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container my-4">
        <!-- Franchise Header -->
        <div class="content-section">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold text-primary-custom mb-2" id="franchiseName">
                        Loading...
                    </h1>
                    <p class="lead text-muted mb-0" id="franchiseSubtitle">
                        P1 System Analysis • Generated <span id="generationDate"></span>
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                    <span class="badge-custom">P1 System</span>
                    <span class="badge-custom badge-success-custom" id="systemVersion">v4.5.5</span>
                </div>
            </div>
        </div>

        <!-- Content Container -->
        <div id="contentContainer">
            <div class="text-center py-5">
                <div class="loading-spinner"></div>
                <p class="mt-3 text-muted">Loading franchise analysis...</p>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Content Loading Script -->
    <script>
        // This will be extended in the actual franchise detail page
        console.log('Base template loaded');
    </script>
</body>
</html>
