<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <title>{{FRANCHISE_NAME}} - Franchise Analysis</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Original sample template color variables - exact match */
        :root {
            --dark: #282828;
            --menu_gray: #7A7A7A;
            --dark_gray: #888;
            --light_gray: #DCDCDC;
            --light: #F7F7F7;
            --light_red: #ff6d7e;
            --red: #E1253A;
            --button_red: #cc1209;
            --link_blue: #1A73E8;
            --blue: #5F9DEF;
            --light_blue: #ddeafc;
            --light_green: rgb(191 255 183 / 50%);
            --border_green: #0b9f00;
        }

        /* Base typography and layout - exact match to sample template */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            font-family: "Inter", sans-serif;
            font-optical-sizing: auto;
            color: var(--dark_gray);
            background-color: #FFF;
            font-weight: 500;
            line-height: 1.2;
            font-size: 13px;
        }

        body {
            line-height: 1.2;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            display: flex;
            flex-direction: column;
            padding-top: 4rem; /* Account for fixed navbar */
        }

        img, picture, video, canvas, svg {
            display: block;
            max-width: 100%;
            image-rendering: auto;
            transform: translateZ(0);
        }

        input, button, textarea, select {
            font: inherit;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        p {
            margin-bottom: 1em;
        }

        ul, ol {
            list-style: none;
        }

        li {
            padding-bottom: 1em;
        }

        h1, h2, h3, h4 {
            color: var(--dark);
            font-weight: 700;
            margin-bottom: 1em;
        }

        h1 {
            font-weight: 800;
            font-size: 1.5rem;
            line-height: 1.2;
        }

        h2, h1.h2, strong.h2 {
            font-size: 1.25rem;
        }

        h3 {
            font-size: 1rem;
        }

        h4 {
            font-size: .875rem;
        }

        /* Container and layout - exact match to sample template */
        .container {
            width: 87.5rem;
            max-width: 100%;
            padding: 0 1.5rem;
            margin: 0 auto;
        }

        .container_medium {
            max-width: 75rem;
            width: 100%;
            margin: auto;
        }

        .container_small {
            max-width: 35rem;
            width: 100%;
            margin: auto;
        }

        /* White shell component - exact match */
        .white_shell {
            border-radius: 1rem;
            overflow: hidden;
            background: #FFF;
            box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.2);
            position: relative;
            transition: all .2s ease-in;
        }

        .white_shell:not(article) {
            padding: 1rem;
        }

        /* Header - exact match to sample template */
        header#top {
            order: 1;
            background-color: #FFF;
            height: 4rem;
            min-height: 4rem;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 0 1rem;
            box-sizing: border-box;
            position: fixed;
            inset: 0 0 auto;
            z-index: 4;
        }

        header#top img {
            height: 1.5rem;
            width: auto;
        }

        @media only screen and (min-width: 441px) {
            header#top img {
                height: 1.75rem;
            }
        }

        /* Main content order */
        main {
            order: 2;
            padding-top: 4rem;
        }

        /* Content sections - using white_shell styling */
        .content-section {
            border-radius: 1rem;
            overflow: hidden;
            background: #FFF;
            box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.2);
            position: relative;
            transition: all .2s ease-in;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .content-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 .5rem 2rem rgba(0, 0, 0, 0.25);
        }

        .section-header {
            border-bottom: 3px solid var(--link_blue);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark);
            margin: 0;
        }

        /* Tables - exact match to sample template */
        .table-custom {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .table-custom th {
            background: linear-gradient(135deg, var(--link_blue) 0%, var(--blue) 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.95em;
        }

        .table-custom td {
            border: 1px solid #e0e0e0;
            padding: 12px;
            text-align: left;
            background: white;
        }

        .table-custom tr:nth-child(even) td {
            background-color: #f8f9fa;
        }

        .table-custom tr:hover td {
            background-color: #e3f2fd;
        }

        /* Charts */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
            background: var(--white);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* Badges and indicators - exact match to sample template */
        .badge-custom {
            background: var(--link_blue);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 0.5rem;
        }

        .badge-success-custom {
            background: var(--border_green);
        }

        .badge-warning-custom {
            background: var(--light_red);
        }

        .badge-danger-custom {
            background: var(--red);
        }

        /* Alert boxes - exact match to sample template */
        .callout {
            background: var(--light_blue);
            border-left: 4px solid var(--link_blue);
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
            font-weight: 500;
        }

        .alert-info-custom {
            background: var(--light_blue);
            color: var(--link_blue);
            border-left: 4px solid var(--link_blue);
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }

        .alert-warning-custom {
            background: var(--light_green);
            color: var(--border_green);
            border-left: 4px solid var(--border_green);
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }

        .alert-danger-custom {
            background: rgba(255, 109, 126, 0.1);
            color: var(--red);
            border-left: 4px solid var(--red);
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content-section {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }
            
            .chart-container {
                height: 300px;
                padding: 0.5rem;
            }
            
            .section-title {
                font-size: 1.3rem;
            }
        }

        /* Loading states */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(26, 115, 232, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Markdown content styling - exact match to sample template */
        .markdown-content h1 {
            color: #333;
            margin: 30px 0 20px 0;
            font-size: 1.6em;
            border-bottom: 2px solid var(--link_blue);
            padding-bottom: 10px;
        }

        .markdown-content h2 {
            color: #444;
            margin: 25px 0 15px 0;
            font-size: 1.4em;
            border-left: 4px solid var(--link_blue);
            padding-left: 15px;
        }

        .markdown-content h3 {
            color: #555;
            margin: 20px 0 10px 0;
            font-size: 1.2em;
        }

        .markdown-content h4 {
            color: #666;
            margin: 15px 0 8px 0;
            font-size: 1.1em;
            font-weight: 600;
        }

        .markdown-content p {
            margin: 12px 0;
            text-align: justify;
            line-height: 1.6;
        }

        .markdown-content ul {
            margin: 15px 0;
            padding-left: 0;
        }

        .markdown-content li {
            list-style: none;
            margin: 8px 0;
            padding: 8px 0 8px 30px;
            position: relative;
            border-left: 3px solid var(--link_blue);
            background: #f8f9fa;
            border-radius: 0 4px 4px 0;
        }

        .markdown-content li:before {
            content: "▶";
            color: var(--link_blue);
            position: absolute;
            left: 10px;
            font-size: 0.8em;
        }

        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .markdown-content table th {
            background: linear-gradient(135deg, var(--link_blue) 0%, var(--blue) 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.95em;
        }

        .markdown-content table td {
            border: 1px solid #e0e0e0;
            padding: 12px;
            text-align: left;
            background: white;
        }

        .markdown-content table tr:nth-child(even) td {
            background-color: #f8f9fa;
        }

        .markdown-content table tr:hover td {
            background-color: #e3f2fd;
        }

        .markdown-content strong {
            color: #333;
            font-weight: 600;
        }

        .markdown-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #d63384;
            border: 1px solid #e0e0e0;
        }

        .markdown-content pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e0e0e0;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <!-- Header - exact match to sample template structure -->
    <header id="top">
        <div style="font-weight: 700; color: var(--link_blue);">🏢 Franchise Analysis</div>
        <div style="font-weight: 600; color: var(--dark);">P1 System v4.5.5</div>
        <div style="font-size: 0.9rem; color: var(--dark_gray);">
            <a href="Master_Index.html" style="color: var(--link_blue); text-decoration: none;">← Back to Home</a>
        </div>
    </header>

    <!-- Main Content - exact match to sample template structure -->
    <main>
        <div class="container">
            <!-- Franchise Header -->
            <div class="white_shell" style="margin: 2rem 0;">
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; align-items: center;">
                    <div>
                        <h1 id="franchiseName" style="color: var(--dark); margin-bottom: 0.5rem;">
                            Loading...
                        </h1>
                        <p id="franchiseSubtitle" style="color: var(--dark_gray); margin-bottom: 0;">
                            P1 System Analysis • Generated <span id="generationDate"></span>
                        </p>
                    </div>
                    <div style="text-align: right;">
                        <div style="margin-bottom: 1rem;">
                            <span class="badge-custom">P1 System</span>
                            <span class="badge-custom badge-success-custom" id="systemVersion">v4.5.5</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Container -->
            <div id="contentContainer">
                <div class="white_shell" style="text-align: center; padding: 3rem;">
                    <div class="loading-spinner"></div>
                    <p style="margin-top: 1rem; color: var(--dark_gray);">Loading franchise analysis...</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Content Loading Script -->
    <script>
        // This will be extended in the actual franchise detail page
        console.log('Base template loaded');
    </script>
</body>
</html>
