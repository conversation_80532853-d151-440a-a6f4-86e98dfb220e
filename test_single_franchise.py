#!/usr/bin/env python3
"""
Test script to process a single franchise and verify the system works end-to-end.
"""

from franchise_automation import FranchiseProcessor
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_single_franchise():
    """Test processing a single franchise."""
    try:
        # Initialize processor
        processor = FranchiseProcessor()
        
        # Load data
        df = processor.load_franchise_data()
        
        # Get the first franchise
        first_row = df.iloc[0]
        franchise_name = first_row[processor.franchise_name_column]
        
        logger.info(f"Testing with franchise: {franchise_name}")
        
        # Test datapoint extraction
        datapoints = processor.extract_franchise_datapoints(first_row)
        logger.info(f"Extracted {len(datapoints)} datapoints")
        
        # Test prompt construction
        prompt = processor.construct_ai_prompt(datapoints)
        logger.info(f"Generated prompt of length: {len(prompt)}")
        
        # Test AI API call (this will make an actual API call)
        logger.info("Making AI API call...")
        content = processor.call_novita_ai(prompt)
        logger.info(f"Received AI response of length: {len(content)}")
        
        # Test saving output
        filepath = processor.save_franchise_output(franchise_name, content)
        logger.info(f"Saved output to: {filepath}")
        
        # Test master index generation
        processed_files = [filepath]
        index_file = processor.generate_master_index(processed_files)
        logger.info(f"Generated master index: {index_file}")
        
        # Test browser launch
        processor.launch_browser(index_file)
        
        logger.info("✅ Single franchise test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_single_franchise()
    if success:
        print("\n🎉 Test passed! The system is working correctly.")
    else:
        print("\n💥 Test failed! Check the logs for details.")
