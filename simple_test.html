<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Content Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .content { border: 1px solid #ccc; padding: 20px; margin: 20px 0; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Simple Content Test</h1>
    <button onclick="loadAndDisplay()">Load and Display Content</button>
    
    <div id="debug-info" class="debug"></div>
    <div id="content-display" class="content"></div>

    <script>
        async function loadAndDisplay() {
            const debugDiv = document.getElementById('debug-info');
            const contentDiv = document.getElementById('content-display');
            
            try {
                // Load JSON
                const response = await fetch('franchise_outputs/101_Mobility.json');
                const data = await response.json();
                const content = data.generated_content;
                
                debugDiv.innerHTML = `
                    <h3>Debug Info:</h3>
                    <p>Content length: ${content.length}</p>
                    <p>Has \\n: ${content.includes('\\n')}</p>
                    <p>First 100 chars: ${content.substring(0, 100)}</p>
                `;
                
                // Simple processing - just replace \\n with <br> and display
                const simpleHtml = content
                    .replace(/\\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>');
                
                contentDiv.innerHTML = simpleHtml;
                
            } catch (error) {
                debugDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
