<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">
    <title>Franchise Analysis</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Corporate-grade design variables */
        :root {
            --primary-blue: #1A73E8;
            --secondary-blue: #5F9DEF;
            --light-blue: #E8F0FE;
            --dark-gray: #2D3748;
            --medium-gray: #4A5568;
            --light-gray: #E2E8F0;
            --success-green: #48BB78;
            --warning-orange: #ED8936;
            --danger-red: #F56565;
            --white: #FFFFFF;
            --background-light: #F7FAFC;
        }

        /* Base typography and layout */
        body {
            font-family: "Inter", sans-serif;
            font-weight: 400;
            line-height: 1.6;
            color: var(--dark-gray);
            background-color: var(--background-light);
            padding-top: 76px;
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            color: var(--dark-gray);
        }

        .text-primary-custom {
            color: var(--primary-blue) !important;
        }

        .bg-primary-custom {
            background-color: var(--primary-blue) !important;
        }

        /* Navigation */
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .navbar-brand-custom {
            font-weight: 700;
            font-size: 1.3rem;
            color: white !important;
        }

        .btn-back {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
        }

        .btn-back:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-1px);
        }

        /* Content sections */
        .content-section {
            background: var(--white);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid var(--light-gray);
        }

        .section-header {
            border-bottom: 3px solid var(--primary-blue);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-gray);
            margin: 0;
        }

        /* Charts */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
            background: var(--white);
            border-radius: 0.5rem;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* Badges */
        .badge-custom {
            background: var(--primary-blue);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge-success-custom {
            background: var(--success-green);
        }

        /* Loading states */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(26, 115, 232, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-blue);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Markdown content styling */
        .markdown-content h1 {
            font-size: 1.8rem;
            margin: 2rem 0 1rem 0;
            color: var(--dark-gray);
            border-bottom: 2px solid var(--primary-blue);
            padding-bottom: 0.5rem;
        }

        .markdown-content h2 {
            font-size: 1.5rem;
            margin: 1.5rem 0 1rem 0;
            color: var(--medium-gray);
            border-left: 4px solid var(--primary-blue);
            padding-left: 1rem;
        }

        .markdown-content h3 {
            font-size: 1.3rem;
            margin: 1.2rem 0 0.8rem 0;
            color: var(--medium-gray);
        }

        .markdown-content h4 {
            font-size: 1.1rem;
            margin: 1rem 0 0.5rem 0;
            color: var(--medium-gray);
            font-weight: 600;
        }

        .markdown-content p {
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .markdown-content table {
            width: 100%;
            margin: 1.5rem 0;
            border-collapse: collapse;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .markdown-content table th {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .markdown-content table td {
            padding: 1rem;
            border-bottom: 1px solid var(--light-gray);
        }

        .markdown-content table tr:nth-child(even) {
            background-color: var(--background-light);
        }

        .markdown-content table tr:hover {
            background-color: var(--light-blue);
        }

        .markdown-content strong {
            font-weight: 700;
            color: var(--dark-gray);
        }

        .markdown-content ul, .markdown-content ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        .markdown-content li {
            margin-bottom: 0.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content-section {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }
            
            .chart-container {
                height: 300px;
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container">
            <a class="navbar-brand navbar-brand-custom" href="Master_Index.html">
                🏢 Franchise Analysis
            </a>
            <div class="navbar-nav ms-auto">
                <a href="Master_Index.html" class="btn btn-back">
                    ← Back to Home
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container my-4">
        <!-- Franchise Header -->
        <div class="content-section">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-5 fw-bold text-primary-custom mb-2" id="franchiseName">
                        Loading...
                    </h1>
                    <p class="lead text-muted mb-0" id="franchiseSubtitle">
                        P1 System Analysis • Generated <span id="generationDate"></span>
                    </p>
                </div>
                <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                    <span class="badge-custom">P1 System</span>
                    <span class="badge-custom badge-success-custom" id="systemVersion">v4.5.5</span>
                </div>
            </div>
        </div>

        <!-- Content Container -->
        <div id="contentContainer">
            <div class="text-center py-5">
                <div class="loading-spinner"></div>
                <p class="mt-3 text-muted">Loading franchise analysis...</p>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Franchise Detail Script -->
    <script>
        // Get franchise parameter from URL
        const urlParams = new URLSearchParams(window.location.search);
        const franchiseFile = urlParams.get('franchise');

        if (!franchiseFile) {
            document.getElementById('contentContainer').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <h4 class="alert-heading">Error</h4>
                    <p>No franchise specified. Please return to the <a href="Master_Index.html">Master Index</a>.</p>
                </div>
            `;
        } else {
            loadFranchiseData(franchiseFile);
        }

        async function loadFranchiseData(filename) {
            try {
                const response = await fetch(`franchise_outputs/${filename}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                displayFranchiseData(data);
            } catch (error) {
                console.error('Error loading franchise data:', error);
                document.getElementById('contentContainer').innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <h4 class="alert-heading">Error Loading Data</h4>
                        <p>Could not load franchise data. Please check the file path and try again.</p>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        function displayFranchiseData(data) {
            // Update header
            document.getElementById('franchiseName').textContent = data.franchise_name;
            document.getElementById('generationDate').textContent = new Date(data.generation_timestamp).toLocaleDateString();
            document.getElementById('systemVersion').textContent = data.system_version || 'v4.5.5';
            document.title = `${data.franchise_name} - Franchise Analysis`;

            // Process and display content
            const content = parseMarkdownContent(data.generated_content);
            document.getElementById('contentContainer').innerHTML = content;

            // Add charts after content is loaded
            setTimeout(() => {
                addChartsToContent(data);
            }, 100);
        }

        function parseMarkdownContent(markdown) {
            // Split content into sections
            const sections = markdown.split(/(?=^#{1,4}\s)/m);
            let html = '<div class="markdown-content">';

            sections.forEach((section, index) => {
                if (section.trim()) {
                    const sectionHtml = convertMarkdownSection(section.trim());
                    html += `<div class="content-section">${sectionHtml}</div>`;
                }
            });

            html += '</div>';
            return html;
        }

        function convertMarkdownSection(markdown) {
            let html = markdown;

            // Convert headers
            html = html.replace(/^#{4}\s+(.+)$/gm, '<h4>$1</h4>');
            html = html.replace(/^#{3}\s+(.+)$/gm, '<h3>$1</h3>');
            html = html.replace(/^#{2}\s+(.+)$/gm, '<h2>$1</h2>');
            html = html.replace(/^#{1}\s+(.+)$/gm, '<h1>$1</h1>');

            // Convert tables
            html = convertMarkdownTables(html);

            // Convert lists
            html = html.replace(/^\* (.+)$/gm, '<li>$1</li>');
            html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

            // Convert bold and italic
            html = html.replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>');
            html = html.replace(/\*(.+?)\*/g, '<em>$1</em>');

            // Convert line breaks
            html = html.replace(/\n\n/g, '</p><p>');
            html = html.replace(/\n/g, '<br>');

            // Wrap in paragraphs
            if (!html.includes('<h') && !html.includes('<table') && !html.includes('<ul')) {
                html = '<p>' + html + '</p>';
            }

            return html;
        }

        function convertMarkdownTables(markdown) {
            const tableRegex = /\|(.+)\|\n\|[-\s|]+\|\n((?:\|.+\|\n?)+)/g;

            return markdown.replace(tableRegex, (match, headerRow, bodyRows) => {
                const headers = headerRow.split('|').map(h => h.trim()).filter(h => h);
                const rows = bodyRows.trim().split('\n').map(row =>
                    row.split('|').map(cell => cell.trim()).filter(cell => cell)
                );

                let tableHtml = '<table class="table table-custom"><thead><tr>';
                headers.forEach(header => {
                    tableHtml += `<th>${header}</th>`;
                });
                tableHtml += '</tr></thead><tbody>';

                rows.forEach(row => {
                    tableHtml += '<tr>';
                    row.forEach(cell => {
                        tableHtml += `<td>${cell}</td>`;
                    });
                    tableHtml += '</tr>';
                });

                tableHtml += '</tbody></table>';
                return tableHtml;
            });
        }

        function addChartsToContent(data) {
            // Look for tables that could be converted to charts
            const tables = document.querySelectorAll('.table-custom');

            tables.forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
                const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr =>
                    Array.from(tr.querySelectorAll('td')).map(td => td.textContent.trim())
                );

                // Check if this table should have a chart
                if (shouldCreateChart(headers, rows)) {
                    const chartContainer = document.createElement('div');
                    chartContainer.className = 'chart-container';
                    chartContainer.innerHTML = `<canvas id="chart-${index}"></canvas>`;

                    // Insert chart before the table
                    table.parentNode.insertBefore(chartContainer, table);

                    // Create the chart
                    createChart(`chart-${index}`, headers, rows);
                }
            });
        }

        function shouldCreateChart(headers, rows) {
            // Create charts for financial data, unit growth, etc.
            const chartKeywords = ['year', 'units', 'growth', 'sales', 'revenue', 'cost', 'fee'];
            const headerText = headers.join(' ').toLowerCase();

            return chartKeywords.some(keyword => headerText.includes(keyword)) && rows.length > 1;
        }

        function createChart(canvasId, headers, rows) {
            const ctx = document.getElementById(canvasId).getContext('2d');

            // Determine chart type based on data
            const isTimeSeriesData = headers.some(h => h.toLowerCase().includes('year'));
            const hasNumericData = rows.some(row => row.some(cell => !isNaN(parseFloat(cell.replace(/[,$%]/g, '')))));

            if (isTimeSeriesData && hasNumericData) {
                createLineChart(ctx, headers, rows);
            } else if (hasNumericData) {
                createBarChart(ctx, headers, rows);
            }
        }

        function createLineChart(ctx, headers, rows) {
            const labels = rows.map(row => row[0]);
            const datasets = [];

            for (let i = 1; i < headers.length; i++) {
                const data = rows.map(row => {
                    const value = row[i];
                    return parseFloat(value.replace(/[,$%]/g, '')) || 0;
                });

                datasets.push({
                    label: headers[i],
                    data: data,
                    borderColor: `hsl(${(i-1) * 60}, 70%, 50%)`,
                    backgroundColor: `hsla(${(i-1) * 60}, 70%, 50%, 0.1)`,
                    tension: 0.4,
                    fill: false
                });
            }

            new Chart(ctx, {
                type: 'line',
                data: { labels, datasets },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: headers[0] + ' Trends'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function createBarChart(ctx, headers, rows) {
            const labels = rows.map(row => row[0]);
            const data = rows.map(row => {
                const value = row[1];
                return parseFloat(value.replace(/[,$%]/g, '')) || 0;
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: headers[1],
                        data: data,
                        backgroundColor: 'rgba(26, 115, 232, 0.8)',
                        borderColor: 'rgba(26, 115, 232, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: headers[1] + ' Analysis'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
