/* Base CSS */
html, body {
    font-size: 16px;
}

h1 {
    font-size: 2.75rem;
}

h2, h1.h2, strong.h2 {
    font-size: 2rem;
}

/* Columns and Grids */

:is(.laptop_grid2, .desktop_grid3, .desktop_grid4, .grid2, .grid3, .grid4) {
    grid-gap: 2.5rem;
}

/* h1 Area */
#title_section {
    padding: 3rem 0;
}

@media only screen and (min-width:1600px) {
    footer {
        padding-bottom: 7.6rem ;
    }
}