/* Base CSS */
html, body {
    font-size: 15px;
}

h1 {
    font-size: 2rem;
}

h2, h1.h2, strong.h2 {
    font-size: 1.5rem;
}

:is(a.button, button):hover {
    background-color: var(--blue);
    cursor: pointer;
}

button.outlined:hover {
    background-color: var(--light);
}

.link {
    position: relative;
}

.link::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -.25rem;
    width: 0;
    height:.0625rem;
    border-bottom:.0625rem dashed var(--dark_gray);
    transition: width 0.3s ease;
}

.link:hover {
    color: var(--dark);
}

.link:hover::after {
    width: 100%;
}

/* Columns and Grids */
.columns {
    display: grid;
    grid-template-columns: repeat(16, 1fr);
    gap: 1.25rem;
}

.tcol4 {
    grid-column: span 4;
}

.tcol6 {
    grid-column: span 6;
}

.tcol8 {
    grid-column: span 8;
}

.tcol10 {
    grid-column: span 10;
}

.tcol12 {
    grid-column: span 12;
}

.tcol16 {
    grid-column: span 16;
}

@media (min-width: 769px) and (max-width: 1024px) {
    .columns:has(.tblock) {
        display: block;
    }
}

/* Columns and Grids */
.columns {
    gap: 2rem;
}

.modal .columns{
    gap: 1rem;
}

.col2 {
    grid-column: span 2;
}

.col3 {
    grid-column: span 3;
}

.col4 {
    grid-column: span 4;
}

.col5 {
    grid-column: span 5;
}

.col6 {
    grid-column: span 6;
}

.col8 {
    grid-column: span 8;
}

.col10 {
    grid-column: span 10;
}

.col12 {
    grid-column: span 12;
}

:is(.grid2, .grid3, .grid4, .laptop_grid2, .desktop_grid2, .desktop_grid3, .desktop_grid4) {
    grid-gap: 1.5rem;
}

.grid3, .desktop_grid3 {
    grid-template-columns: 1fr 1fr 1fr;
}

.grid4, .desktop_grid4 {
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

.flex_bottom_right {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: flex-end;
}

.flex_center_right {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
}

/* Show/Hide, Text Alignments and Others */
.desktop_nomargin {
    margin-bottom: 0;
}

.hide_desktop {
    display: none !important;
}

/* Design Elements */
.white_shell:not(article) {
    padding: 1.5rem 2.5rem;
}

/* Headers and Footer */
header#top {
    border: none;
}

.header_left {
    display: flex;
    align-items: center;
}

.desktop_nav {
    display: flex;
    align-items: center;
}

.desktop_nav > ul {
    display: flex;
    flex-direction: row;
    width: 50vw;
    max-width: 40rem;
    justify-content: space-evenly;
}
.desktop_nav > ul > li{
    padding: 0;
    position: relative;
    height: 4rem;
    display: inline-flex;
    align-items: center;
}

.desktop_nav > ul > li:has(ul) > a span {
    display: flex;
    align-items: center;
}

.desktop_nav > ul > li:has(ul) > a span:after {
    content: '';
    background-image: url(https://franchise-ventures-general.s3.us-east-1.amazonaws.com/fcom_cdn/images/chevron.svg);
    background-repeat: no-repeat;
    background-size: .5em;
    background-position: center;
    width: 1em;
    height: 1em;
    display: inline-block;
    transform: rotate(90deg);
}

.desktop_nav > ul li.open > a span {
    font-weight: 600;
}

.desktop_nav > ul li.open > a span:after {
    transform: rotate(-90deg);
}

.desktop_nav a {
    color: var(--dark);
    font-weight: 400;
    line-height: 1;
}

.desktop_nav span {
    line-height: 1;
}

.desktop_nav > li.active > a {
    color: var(--link_blue);
}

.desktop_nav > ul li:not(.open) ul {
    display: none;
}

.desktop_nav > ul ul {
    position: absolute;
    padding: 1rem;
    top: 4rem;
    background: #FFF;
    border: .0625rem solid var(--light_gray);
    left: -1rem;
}

.desktop_nav > ul ul li{
    text-wrap: nowrap;
}

.desktop_nav > ul ul li:last-child{
    padding-bottom: 0;
}

footer {
    padding: 6rem 0 1rem;
    margin-bottom: 0;
}

footer p {
    max-width: 15rem;
}

footer .light_gray_border{
    margin-top: 1rem;
    padding-top: 3rem;
}

.social_media a:hover {
    background-color: rgba(0, 0, 0, .4);
}

/* Copy Section */
:is(.copy, .login_modal, .login_page) a:not(.no-style) {
    position: relative;
}

:is(.copy, .login_modal, .login_page) a:not(.no-style)::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -.0625rem;
    width: 0;
    height:.0625rem;
    border-bottom:.0625rem dashed var(--dark_gray);
    transition: width 0.3s ease;
}

:is(.copy, .login_modal, .login_page) a:not(.no-style):hover {
    color: var(--dark);
}

:is(.copy, .login_modal, .login_page) a:not(.no-style):hover::after {
    width: 100%;
}

.login_modal .login_screens .tcol8.login_form_block{
    padding-left: 1rem;
}

.login_page .login_display .signup_steps_block{
    top: 1.25rem !important;
    right: 1.25rem !important;
}

/* Settings */
.settings_forms .white_shell{
    min-height: 16.875rem;
}

.move:hover, .has_move:is(.white_shell):hover {
    transform: translateY(-.25rem);
}

.article_picture {
    width: 50% !important;
    float: right;
    margin-left: 2rem;
}

#request_information .buttons{
    text-align: right;
}

#request_information .buttons button{
    width: 50%;
}

/* Loading */
.loader {
    border-width: 1rem;
    border-top-width: 1rem;
    width: 10rem;
    height: 10rem;
}