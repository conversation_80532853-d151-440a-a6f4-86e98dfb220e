/* Base Reset and CSS */
h1 {
    font-size: 2.5rem;
}

h2, h1.h2, strong.h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.25rem;
}

.text_right {
    text-align: right;
}

:is(.grid2, .grid4) {
    grid-auto-columns: minmax(0, 1fr);
    grid-template-columns: 1fr 1fr;
}

.grid3 {
    grid-template-columns: 1fr 1fr;
}

/* Show/Hide and Text Alignments */
@media (min-width: 769px){
    .hide_tablet{
        display: none !important;
    }
}

/* Headers and Footer */
header#top img {
    height: 2rem;
}

header#bottom {
    height: 6rem;
}

header#bottom a {
    font-size: 1.0625rem;
}

footer {
    padding-bottom: 6.5rem;
}

.disclaimer {
    text-align: right;
}

/* h1 Area */
#title_section {
    padding: 2rem 0;
}

/* Copy Section */
.copy {
    padding: 3.5rem 0;
}

.copy :is(td, th) {
    padding: 1.5rem;
}

.copy table {
    border-radius: 1.5rem;
}

/* Modal */
.guts {
    width: 100%;
    padding: 2rem;
}

/* Side Bar */
.side_bar {
    border-top: none;
    border-left:.0625rem solid var(--light_gray);
    padding: 0 0 2rem 2rem;
    margin: 0 0 0 2rem;
}

.sidebar{
    position: sticky;
    top: 6rem;
    font-size: .875rem;
}