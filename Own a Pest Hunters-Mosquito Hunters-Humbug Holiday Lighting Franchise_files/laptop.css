/* Columns and Grids */
.columns {
    display: grid;
    grid-template-columns: repeat(16, 1fr);
    gap: 1.25rem;
}

.tcol4 {
    grid-column: span 4;
}

.tcol6 {
    grid-column: span 6;
}

.tcol8 {
    grid-column: span 8;
}

.tcol10 {
    grid-column: span 10;
}

.tcol12 {
    grid-column: span 12;
}

.tcol16 {
    grid-column: span 16;
}

.laptop_grid2 {
    grid-template-columns: 1fr 1fr;
}

@media (min-width: 769px) and (max-width: 1024px) {
    .columns:has(.tblock) {
        display: block;
    }
}