/*! api - Wed, 20 Aug 2025 17:29:50 GMT */
@keyframes omBounce{from,20%,53%,to{animation-timing-function:cubic-bezier(0.215, 0.61, 0.355, 1);transform:translate3d(0, 0, 0)}40%,43%{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);transform:translate3d(0, -30px, 0) scaleY(1.1)}70%{animation-timing-function:cubic-bezier(0.755, 0.05, 0.855, 0.06);transform:translate3d(0, -15px, 0) scaleY(1.05)}80%{transition-timing-function:cubic-bezier(0.215, 0.61, 0.355, 1);transform:translate3d(0, 0, 0) scaleY(0.95)}90%{transform:translate3d(0, -4px, 0) scaleY(1.02)}}.om-animation-bounce{animation-name:omBounce;transform-origin:center bottom}@keyframes omBounceIn{from,20%,40%,60%,80%,to{animation-timing-function:cubic-bezier(0.215, 0.61, 0.355, 1)}0%{opacity:0;transform:scale3d(0.3, 0.3, 0.3)}20%{transform:scale3d(1.1, 1.1, 1.1)}40%{transform:scale3d(0.9, 0.9, 0.9)}60%{opacity:1;transform:scale3d(1.03, 1.03, 1.03)}80%{transform:scale3d(0.97, 0.97, 0.97)}to{opacity:1;transform:scale3d(1, 1, 1)}}.om-animation-bounce-in{animation-duration:.75s;animation-name:omBounceIn}@keyframes omBounceInDown{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215, 0.61, 0.355, 1)}0%{opacity:0;transform:translate3d(0, -3000px, 0) scaleY(3)}60%{opacity:1;transform:translate3d(0, 25px, 0) scaleY(0.9)}75%{transform:translate3d(0, -10px, 0) scaleY(0.95)}90%{transform:translate3d(0, 5px, 0) scaleY(0.985)}to{transform:translate3d(0, 0, 0)}}.om-animation-bounce-in-down{animation-name:omBounceInDown}@keyframes omBounceInLeft{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215, 0.61, 0.355, 1)}0%{opacity:0;transform:translate3d(-3000px, 0, 0) scaleX(3)}60%{opacity:1;transform:translate3d(25px, 0, 0) scaleX(1)}75%{transform:translate3d(-10px, 0, 0) scaleX(0.98)}90%{transform:translate3d(5px, 0, 0) scaleX(0.995)}to{transform:translate3d(0, 0, 0)}}.om-animation-bounce-in-left{animation-name:omBounceInLeft}@keyframes omBounceInRight{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215, 0.61, 0.355, 1)}from{opacity:0;transform:translate3d(3000px, 0, 0) scaleX(3)}60%{opacity:1;transform:translate3d(-25px, 0, 0) scaleX(1)}75%{transform:translate3d(10px, 0, 0) scaleX(0.98)}90%{transform:translate3d(-5px, 0, 0) scaleX(0.995)}to{transform:translate3d(0, 0, 0)}}.om-animation-bounce-in-right{animation-name:omBounceInRight}@keyframes omBounceInUp{from,60%,75%,90%,to{animation-timing-function:cubic-bezier(0.215, 0.61, 0.355, 1)}from{opacity:0;transform:translate3d(0, 3000px, 0) scaleY(5)}60%{opacity:1;transform:translate3d(0, -20px, 0) scaleY(0.9)}75%{transform:translate3d(0, 10px, 0) scaleY(0.95)}90%{transform:translate3d(0, -5px, 0) scaleY(0.985)}to{transform:translate3d(0, 0, 0)}}.om-animation-bounce-in-up{animation-name:omBounceInUp}@keyframes omFlash{from,50%,to{opacity:1}25%,75%{opacity:0}}.om-animation-flash{animation-name:omFlash}@keyframes omFlip{from{transform:perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, -360deg);animation-timing-function:ease-out}40%{transform:perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);animation-timing-function:ease-out}50%{transform:perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);animation-timing-function:ease-in}80%{transform:perspective(400px) scale3d(0.95, 0.95, 0.95) translate3d(0, 0, 0) rotate3d(0, 1, 0, 0deg);animation-timing-function:ease-in}to{transform:perspective(400px) scale3d(1, 1, 1) translate3d(0, 0, 0) rotate3d(0, 1, 0, 0deg);animation-timing-function:ease-in}}.om-animation-flip{backface-visibility:visible;animation-name:omFlip}@keyframes omFlipInX{from{transform:perspective(400px) rotate3d(1, 0, 0, 90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(1, 0, 0, -20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(1, 0, 0, 10deg);opacity:1}80%{transform:perspective(400px) rotate3d(1, 0, 0, -5deg)}to{transform:perspective(400px)}}.om-animation-flip-down{backface-visibility:visible !important;animation-name:omFlipInX}@keyframes omFlipInY{from{transform:perspective(400px) rotate3d(0, 1, 0, 90deg);animation-timing-function:ease-in;opacity:0}40%{transform:perspective(400px) rotate3d(0, 1, 0, -20deg);animation-timing-function:ease-in}60%{transform:perspective(400px) rotate3d(0, 1, 0, 10deg);opacity:1}80%{transform:perspective(400px) rotate3d(0, 1, 0, -5deg)}to{transform:perspective(400px)}}.om-animation-flip-side{backface-visibility:visible !important;animation-name:omFlipInY}@keyframes omLightSpeedInRight{from{transform:translate3d(100%, 0, 0) skewX(-30deg);opacity:0}60%{transform:skewX(20deg);opacity:1}80%{transform:skewX(-5deg)}to{transform:translate3d(0, 0, 0)}}.om-animation-light-speed{animation-name:omLightSpeedInRight;animation-timing-function:ease-out}@keyframes omPulse{from{transform:scale3d(1, 1, 1)}50%{transform:scale3d(1.05, 1.05, 1.05)}to{transform:scale3d(1, 1, 1)}}.om-animation-pulse{animation-name:omPulse;animation-timing-function:ease-in-out}@keyframes omRollIn{from{opacity:0;transform:translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg)}to{opacity:1;transform:translate3d(0, 0, 0)}}.om-animation-roll-in{animation-name:omRollIn}@keyframes omRotateIn{from{transform:rotate3d(0, 0, 1, -200deg);opacity:0}to{transform:translate3d(0, 0, 0);opacity:1}}.om-animation-rotate{animation-name:omRotateIn;transform-origin:center}@keyframes omRotateInDownLeft{from{transform:rotate3d(0, 0, 1, -45deg);opacity:0}to{transform:translate3d(0, 0, 0);opacity:1}}.om-animation-rotate-down-left{animation-name:omRotateInDownLeft;transform-origin:left bottom}@keyframes omRotateInDownRight{from{transform:rotate3d(0, 0, 1, 45deg);opacity:0}to{transform:translate3d(0, 0, 0);opacity:1}}.om-animation-rotate-down-right{animation-name:omRotateInDownRight;transform-origin:right bottom}@keyframes omRotateInUpLeft{from{transform:rotate3d(0, 0, 1, 45deg);opacity:0}to{transform:translate3d(0, 0, 0);opacity:1}}.om-animation-rotate-up-left{animation-name:omRotateInUpLeft;transform-origin:left bottom}@keyframes omRotateInUpRight{from{transform:rotate3d(0, 0, 1, -90deg);opacity:0}to{transform:translate3d(0, 0, 0);opacity:1}}.om-animation-rotate-up-right{animation-name:omRotateInUpRight;transform-origin:right bottom}@keyframes omRubberBand{from{transform:scale3d(1, 1, 1)}30%{transform:scale3d(1.25, 0.75, 1)}40%{transform:scale3d(0.75, 1.25, 1)}50%{transform:scale3d(1.15, 0.85, 1)}65%{transform:scale3d(0.95, 1.05, 1)}75%{transform:scale3d(1.05, 0.95, 1)}to{transform:scale3d(1, 1, 1)}}.om-animation-rubber-band{animation-name:omRubberBand}@keyframes omShake{from,to{transform:translate3d(0, 0, 0)}10%,30%,50%,70%,90%{transform:translate3d(-10px, 0, 0)}20%,40%,60%,80%{transform:translate3d(10px, 0, 0)}}.om-animation-shake{animation-name:omShake}@keyframes omSlideInDown{from{transform:translate3d(0, -100%, 0);visibility:visible}to{transform:translate3d(0, 0, 0)}}.om-animation-slide-in-down{animation-name:omSlideInDown}@keyframes omSlideInLeft{from{transform:translate3d(-100%, 0, 0);visibility:visible}to{transform:translate3d(0, 0, 0)}}.om-animation-slide-in-left{animation-name:omSlideInLeft}@keyframes omSlideInRight{from{transform:translate3d(100%, 0, 0);visibility:visible}to{transform:translate3d(0, 0, 0)}}.om-animation-slide-in-right{animation-name:omSlideInRight}@keyframes omSwing{20%{transform:rotate3d(0, 0, 1, 15deg)}40%{transform:rotate3d(0, 0, 1, -10deg)}60%{transform:rotate3d(0, 0, 1, 5deg)}80%{transform:rotate3d(0, 0, 1, -5deg)}to{transform:rotate3d(0, 0, 1, 0deg)}}.om-animation-swing{transform-origin:top center;animation-name:omSwing}@keyframes omTada{from{transform:scale3d(1, 1, 1)}10%,20%{transform:scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg)}30%,50%,70%,90%{transform:scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg)}40%,60%,80%{transform:scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg)}to{transform:scale3d(1, 1, 1)}}.om-animation-tada{animation-name:omTada}@keyframes omWobble{from{transform:translate3d(0, 0, 0)}15%{transform:translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg)}30%{transform:translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg)}45%{transform:translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg)}60%{transform:translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg)}75%{transform:translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg)}to{transform:translate3d(0, 0, 0)}}.om-animation-wobble{animation-name:omWobble}.om-animated{animation-duration:1s;animation-fill-mode:both}@media print,(prefers-reduced-motion: reduce){.animated{animation-duration:1ms !important;transition-duration:1ms !important;animation-iteration-count:1 !important}.animated[class*=Out]{opacity:0}}.om-content-lock{color:rgba(0,0,0,0) !important;text-shadow:rgba(0,0,0,.5) 0px 0px 10px;-webkit-user-select:none;-moz-user-select:none;user-select:none;pointer-events:none;filter:url("data:image/svg+xml;utf9,<svg%20version=%271.1%27%20xmlns=%27http://www.w3.org/2000/svg%27><filter%20id=%27blur%27><feGaussianBlur%20stdDeviation=%2710%27%20/></filter></svg>#blur");-webkit-filter:blur(10px);-ms-filter:blur(10px);-o-filter:blur(10px);filter:blur(10px)}html.om-mobile-position{position:fixed !important}html.om-mobile-position body{position:fixed !important}html.om-position-popup body{overflow:hidden !important}html.om-position-floating-top{transition:padding-top .5s ease !important}html.om-position-floating-bottom{transition:padding-bottom .5s ease !important}html.om-reset-dimensions{height:100% !important;width:100% !important}.om-verification-confirmation{font-family:"Lato",Arial,Helvetica,sans-serif;position:fixed;border-radius:10px;bottom:20px;left:20px;padding:10px 20px;opacity:0;transition:opacity .3s ease-in;background:#85bf31;color:#fff;font-size:18px;font-weight:bold;z-index:9999}
