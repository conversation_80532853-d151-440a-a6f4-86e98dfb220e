<!DOCTYPE html>
<!-- saved from url=(0052)https://www.franchise.com/franchise/mosquito-hunters -->
<html lang="en"><script async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/clarity.js"></script><script async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/w.js"></script><script async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/e2iolzu7nc"></script><script src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/bat.js" async=""></script><script src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/194010784310142" async=""></script><script async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/fbevents.js"></script><script type="text/javascript" async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/js"></script><script type="text/javascript" async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/js(1)"></script><script type="text/javascript" src="chrome-extension://nomnoimacbncclfbnfaingniikblfbji/build/scripts/installDetector.js"></script><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
            <link rel="preconnect" href="https://fonts.googleapis.com/">
        <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
                    <link rel="preconnect" href="https://connect.facebook.net/">
            <script async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/gtm.js"></script><script>
                        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                        })(window,document,'script','dataLayer','GTM-T3J9HJS');
                    </script>                <meta http-equiv="X-UA-Compatible" content="IE=edge">
        
        <meta name="google" content="notranslate">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no">     

            <title>Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise</title>
            <meta name="description" content="Partner with Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting franchise to own your own business in the home services industry. Get cost, location and ownership details at Franchise.com now.">
            <meta property="og:title" content="Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise">
            <meta property="og:description" content="Partner with Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting franchise to own your own business in the home services industry. Get cost, location and ownership details at Franchise.com now.">
            <meta property="og:image" content="https://franchise-ventures-general.s3.amazonaws.com/concepts_cdn/254546/mosquitohunters-960x480.jpg?v=1">        <link rel="canonical" href="https://www.franchise.com/franchise/mosquito-hunters"> 
        <meta name="robots" content="index, follow">
    
            <!-- including form and login because login modal gets triggered on all pages -->
        <style>* {margin: 0;padding: 0;box-sizing: border-box;}html, body {height: 100%;font-family: "Inter", sans-serif;font-optical-sizing: auto;color: var(--dark_gray);background-color: #FFF;font-weight: 500;line-height: 1.2;font-size: 13px;}body {line-height: 1.2;-webkit-font-smoothing: antialiased;-moz-osx-font-smoothing: grayscale;display: flex;flex-direction: column;}img, picture, video, canvas, svg {display: block;max-width: 100%;image-rendering: auto;transform: translateZ(0);}input, button, textarea, select {font: inherit;}a {text-decoration: none;color: inherit;}p {margin-bottom: 1em;}ul, ol {list-style: none;}li {padding-bottom: 1em;}h1, h2, h3, h4{color: var(--dark);font-weight: 700;margin-bottom: 1em;}h1 {font-weight: 800;font-size: 1.5rem;line-height: 1.2;}h2, h1.h2, strong.h2 {font-size: 1.25rem;}h3 {font-size: 1rem;}h4 {font-size: .875rem;}.italic{font-style: italic;}.text-small{font-size: 80%;}.mcol8{grid-column: span 8;}.bold{font-weight: bold;}.text-center{text-align: center;}.hide{display: none !important;}.w-100{width: 100% !important;}.mw-100{max-width: 100% !important;}.mb-1{margin-bottom: 1rem !important;}.flexxed{display: flex;align-items: center;justify-content: space-between;}.readonly{pointer-events: none;background-color: var(--light_blue);}.noevents{pointer-events: none !important;cursor: not-allowed !important;}.d-inline-block{display: inline-block !important;}button, a.button, .content #quiz_main button {transition: background-color .2s;background-color: var(--link_blue);line-height: 1;font-size: 1rem;padding: .75em 3em;border-radius: 3em;font-weight: 500;color: #fff;border: none;}button.chevron, a.button.chevron {background-image: url(https://franchise-ventures-general.s3.us-east-1.amazonaws.com/fcom_cdn/images/chevron_white.svg);background-repeat: no-repeat;background-size: .75rem;background-position: center right .875rem;}button.big, a.button.big {font-size: 1.125rem;padding: 1.25em 3.5em;}button.disabled {background-color: var(--light_gray) !important;border-color: var(--light_gray) !important;color: var(--dark_gray) !important;cursor: default !important;}button.outlined {box-shadow: inset 0 0 0 0.0625rem var(--link_blue);background-color: #fff;background-image: url(https://franchise-ventures-general.s3.us-east-1.amazonaws.com/fcom_cdn/images/chevron_blue.svg);color: var(--dark);}.standard_btn{text-align: right;transition: background-color .2s;background-color: var(--blue);width: 100%;border-radius: 0 0 .875rem .875rem;padding-right: 1.5em;}.standard_wrap{border: .25rem solid #FFF;border-radius: 0 0 1rem 1rem;}:is(.grid2, .grid3, .grid4, .laptopgrid2, .laptop_grid3, .laptopgrid4, .desktop_grid2, .desktop_grid3, .desktop_grid4) {display: grid;grid-template-columns: 1fr;grid-gap: 1rem;}@media (max-width: 768px){.hide_mobile {display: none !important;}}.text_center {text-align: center;}.container {width: 87.5rem;max-width: 100%;padding: 0 1.5rem;margin: 0 auto;}.container_medium{max-width: 75rem;width: 100%;margin: auto;}.container_small{max-width: 35rem;width: 100%;margin: auto;}.white_shell {border-radius: 1rem;overflow: hidden;background: #FFF;box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.2);position: relative;transition: all .2s ease-in;}.white_shell:not(article) {padding: 1rem;}header#top {order: 1;background-color: #FFF;height: 4rem;min-height: 4rem;display: flex;flex-direction: row;align-items: center;justify-content: space-between;padding: 0 1rem;box-sizing: border-box;position: fixed;inset: 0 0 auto;z-index: 4;}header#top img {height: 1.5rem;width: auto;}@media only screen and (min-width: 441px) {header#top img {height: 1.75rem;}}.back {display: none;}.desktop_nav {display: none;}#header_nav {display: grid;grid-template-columns: repeat(3, 1fr);grid-template-rows: 1fr;grid-column-gap: 1rem;grid-row-gap: 0;width: 8rem;}#header_nav div {cursor: pointer;position: relative;}#header_nav i:before {content: '';display: inline-flex;height: 1.5rem;width: 1.5rem;background-color: var(--link_blue);mask-repeat: no-repeat;}#header_nav .avatar {background: var(--dark_gray);display: inline-flex;width: 1.75rem;align-items: center;justify-content: center;font-size: .75rem;font-weight: 600;border-radius: 3rem;margin-bottom: .5rem;color: #FFF;height: 1.75rem;}main {order: 2;padding-top: 4rem;}header#bottom {order: 4;width: 100%;display: flex;align-items: center;justify-content: center;height: 4rem;padding: 0;border-top:.0625rem solid var(--light_gray);background: #FFF;z-index: 2;position: fixed;inset: auto 0 0 0;}header#bottom > ul {display: grid;grid-auto-columns: minmax(0, 1fr);grid-auto-flow: column;width: 100%;}header#bottom > ul > li {padding-bottom: 0;position: relative;height: 4rem;display: flex;align-items: center;justify-content: center;}header#bottom > ul > li > a {text-decoration: none;color: var(--menu_gray);font-weight: 500;display: flex;flex-direction: column;align-items: center;font-size: .725rem;padding: .25rem;}header#bottom > ul > li.active :is(a,svg) {fill: var(--link_blue);color: var(--link_blue);}header#bottom i:before{content: '';display: block;height: 1.5rem;width: 1.5rem;background-color: var(--menu_gray);mask-repeat: no-repeat;}header#bottom > ul > li.active i:before{background-color: var(--link_blue);}header#bottom > ul > li > a span {padding-top: .25rem;}header#bottom > ul li:not(.open) ul {display: none;}header#bottom > ul ul {position: absolute;padding: 1rem;bottom: 4rem;border: .0625rem solid var(--light_gray);border-bottom: none;background-color: #fff;right: .5rem;}header#bottom > ul ul li{text-wrap: nowrap;}header#bottom > ul ul li:last-child{padding-bottom: 0;}footer {order: 3;padding: 2.5rem 0 4.5rem;}footer :is(h3, p, a, ul, div) {font-size: .875rem;}footer ul {margin-bottom: 2rem;}#apps {display: flex;width: 100%;gap: 4.1665%;max-width: 15rem;margin-bottom: 2rem;}#apps a {flex: 1 1 0;aspect-ratio: 3 / 1;object-fit: cover;display: inline-flex;}#apps img {width: 100%;height: auto;}.disclaimer, .social_media {padding-bottom: 2rem;}footer .light_gray_border{padding-top: 2rem;}.social_media {display: flex;flex-wrap: wrap;gap: .5rem;}.social_media a {height: 2.5rem;width: 2.5rem;background-color: rgba(0, 0, 0, .2);border-radius: 3rem;display: flex;align-items: center;justify-content: center;transition: background-color .2s;}.social_media img{height: 1rem;width: 1rem;opacity: .8;}#title_section {padding: 1.5rem 0;}#title_section h1{margin: 0;}#title_section h1 + h2 {font-size: 1rem;font-weight: 500;color: var(--dark_gray);margin: 0;}#title_section p {line-height: 1.4;}#title_section h1 + :is(p, h2) {padding-top: .25rem;}.copy {padding: 2.5rem 0;}.copy p{line-height: 1.4;}:is(.copy, .login_modal, .login_page) a {color: var(--link_blue);}.copy :is(strong, b) {font-weight: 800;}.copy :is(ul, ol) {list-style-type: initial;padding-left: 1rem;margin-bottom: 1rem;}:is(ul, ol) li::marker {color: var(--dark);}.copy :is(ul, ol) li:nth-last-child(1) {padding-bottom: 0;}.copy .container > *:nth-last-child(1) {margin-bottom: 0;}.copy h2{margin-bottom: 1rem;}.copy table {background-color: #FFF;box-shadow: 0 0 0 .125rem var(--blue);width: 100%;max-width: 48rem;border-radius: .5rem;border-collapse: collapse;margin-bottom: 1em;overflow: hidden;}.copy table > *:first-child, .copy table tbody > *:first-child {border-radius: 1.5rem 1.5rem 0 0;}.copy table > *:last-child, .copy table tbody > *:last-child{border-radius: 0 0 1.5rem 1.5rem;}.copy thead {background-color: var(--link_blue);color: #FFF;font-size: 1.5rem;}.copy tr:nth-child(even) {background-color: var(--light_blue);}.copy :is(td, th) {padding: .75rem;border: none;}.copy hr {border: none;border-top: .0625rem solid var(--light_gray);margin: 1rem 0;}:root {--dark: #282828;--menu_gray: #7A7A7A;--dark_gray: #888;--light_gray: #DCDCDC;--light: #F7F7F7;--light_red: #ff6d7e;--red: #E1253A;--button_red: #cc1209;--link_blue: #1A73E8;--blue: #5F9DEF;--light_blue: #ddeafc;--light_green: rgb(191 255 183 / 50%);--border_green: #0b9f00;}.dark {color: var(--dark);}.link_blue {color: var(--link_blue);}.light_bg {background-color: var(--light);}.light_blue_bg {background-color: var(--light_blue);}.light_gray_border {border-top:.0625rem solid var(--light_gray);}.gray_border{border:.0625rem solid var(--light_gray);border-radius: 1.25rem;}.tooltip {background: var(--blue);border:.0625rem solid var(--link_blue);color: #fff;border-radius: .25rem;padding: .5rem;z-index: 9;font-size: 0.8rem;}.tooltip ul{list-style: disc;padding-left: 1rem;}.tooltip strong{font-weight: bold;}.tooltip_label{position: relative;}.tooltip_label label{display: flex !important;align-items: center;gap: .25rem;}.tooltip_label label svg{cursor: pointer;}.divider{width: 100%;height:.0625rem;border-bottom:.0625rem solid var(--light_gray);}.divider.dashed{border-bottom:.0625rem dashed var(--light_gray);}.error,.error_msg{color: var(--red);font-size: .85rem;margin: .25rem .25rem 0;line-height: 1.2;font-weight: 400;}.error-outline{border-color: var(--red) !important;}.error-outline + .error{display: block;}.success_bg{background: var(--light_green);border:.0625rem solid var(--border_green);padding: 1rem;border-radius: .25rem;color: var(--dark);}#alert{display: none;left: 0;top: 0;bottom: 0;right: 0;position: fixed;align-content: center;justify-content: center;flex-direction: column;z-index: 5;background: rgba(0,0,0,.5);backdrop-filter: blur(.5rem);}#alert.show{display: flex;}.alert {display: block;background: #fff;padding: 2rem 1rem;width: 100%;max-width: 25rem;box-sizing: border-box;margin: 0 auto;position: relative;border-radius: 1.25rem;}.alert h3{margin-bottom: 0.5rem;}.alert p{margin: 1rem 0;font-size: 1rem;}.overflow{overflow: hidden;}.modal{position: fixed;top: 0;left: 0;right: 0;bottom: 0;z-index: 5;background: rgba(0,0,0,.5);display: none;transition: all .25s ease-out;backdrop-filter: blur(.5rem);}.modal.show{display: block;}.guts {background: #FFF;display: flex;justify-content: flex-start;max-height: calc(100% - 2rem);flex-direction: column;max-width: 64rem;width:calc(100% - 1.25rem);margin: 1rem auto;padding: 2rem 1rem;border-radius: 1.25rem;z-index: 1;position: relative;}.login_modal .guts{max-width: 31.25rem;}#notification_modal .guts{max-width: 40rem;}#forgot_password_modal h3{margin: 0;margin-bottom: 1em;}.modal .guts .content{overflow-y: auto;}.modal .guts .content{-ms-overflow-style: none;scrollbar-width: none;}.modal .guts .content::-webkit-scrollbar {display: none;}.modal .close_modal{width: 2rem;height: 2rem;background-color: var(--dark_gray);border-radius: .25rem;display: flex;justify-content: center;align-items: center;padding: .5rem;position: absolute;top: -.5rem;right: -.5rem;color: #fff;cursor: pointer;border:.0625rem solid var(--light_gray);}input[type="checkbox"] {width: 1.5rem;height: 1.15rem;border-radius: .25rem;background-color: var(--light);border:.0625rem solid var(--dark_gray);cursor: pointer;}.listing_favorites{position: relative;}.listing_favorites input{position: absolute;z-index: 1;opacity: 0;max-width: 100% !important;cursor: pointer;width: 100%;height: 100%;}:is(.listing_favorites,.compare_change_item) i:before{content: '';display: inline-flex;height: 1rem;width: 1rem;background-color: #fff;mask-repeat: no-repeat;}.side_bar{border-top:.0625rem solid var(--light_gray);padding-top: 1.5rem;margin-top: 1.5rem;}:is(.notifications_nav:not([data-count="0"]),.favorites_nav:not([data-count="0"])):before {content: attr(data-count);position: absolute;left: 1rem;top: -.25rem;background: var(--button_red);color: #fff;display: inline-flex;line-height: 1;font-size: .75rem;font-weight: 600;padding: .1rem .2rem;border-radius: .5rem;min-width: .5rem;align-items: center;justify-content: center;z-index: 1;}.title_with_icon button.favorites:has(input:checked){background-color: var(--button_red) !important;border-color: var(--button_red) !important;}#title_section.title_with_icon button.favorites:has(input:checked) span{color: #fff;}.title_with_icon button.favorites:has(input:checked) :is(.save,.heart){background-color: #fff;}:is(button.favorites,.listing_favorites):has(input:checked) .heart:before{-webkit-mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');}.title_with_icon button.favorites:has(input:checked) .save:before{-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiAgd2lkdGg9IjI0IiAgaGVpZ2h0PSIyNCIgIHZpZXdCb3g9IjAgMCAyNCAyNCIgIGZpbGw9ImN1cnJlbnRDb2xvciIgIGNsYXNzPSJpY29uIGljb24tdGFibGVyIGljb25zLXRhYmxlci1maWxsZWQgaWNvbi10YWJsZXItYm9va21hcmsiPjxwYXRoIHN0cm9rZT0ibm9uZSIgZD0iTTAgMGgyNHYyNEgweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xNCAyYTUgNSAwIDAgMSA1IDV2MTRhMSAxIDAgMCAxIC0xLjU1NSAuODMybC01LjQ0NSAtMy42M2wtNS40NDQgMy42M2ExIDEgMCAwIDEgLTEuNTUgLS43MmwtLjAwNiAtLjExMnYtMTRhNSA1IDAgMCAxIDUgLTVoNHoiIC8+PC9zdmc+');mask: url('data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiAgd2lkdGg9IjI0IiAgaGVpZ2h0PSIyNCIgIHZpZXdCb3g9IjAgMCAyNCAyNCIgIGZpbGw9ImN1cnJlbnRDb2xvciIgIGNsYXNzPSJpY29uIGljb24tdGFibGVyIGljb25zLXRhYmxlci1maWxsZWQgaWNvbi10YWJsZXItYm9va21hcmsiPjxwYXRoIHN0cm9rZT0ibm9uZSIgZD0iTTAgMGgyNHYyNEgweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xNCAyYTUgNSAwIDAgMSA1IDV2MTRhMSAxIDAgMCAxIC0xLjU1NSAuODMybC01LjQ0NSAtMy42M2wtNS40NDQgMy42M2ExIDEgMCAwIDEgLTEuNTUgLS43MmwtLjAwNiAtLjExMnYtMTRhNSA1IDAgMCAxIDUgLTVoNHoiIC8+PC9zdmc+');}:is(button.favorites,.listing_favorites):has(input:checked) :is(.save,.heart):before{background-color: var(--button_red) !important;}.article_icon:has(input:checked) .save:before{-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiAgd2lkdGg9IjE1IiAgaGVpZ2h0PSIxNSIgIHZpZXdCb3g9IjAgMCAyNCAyNCIgIGZpbGw9ImN1cnJlbnRDb2xvciIgIGNsYXNzPSJpY29uIGljb24tdGFibGVyIGljb25zLXRhYmxlci1maWxsZWQgaWNvbi10YWJsZXItYm9va21hcmsiPjxwYXRoIHN0cm9rZT0ibm9uZSIgZD0iTTAgMGgyNHYyNEgweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xNCAyYTUgNSAwIDAgMSA1IDV2MTRhMSAxIDAgMCAxIC0xLjU1NSAuODMybC01LjQ0NSAtMy42M2wtNS40NDQgMy42M2ExIDEgMCAwIDEgLTEuNTUgLS43MmwtLjAwNiAtLjExMnYtMTRhNSA1IDAgMCAxIDUgLTVoNHoiIC8+PC9zdmc+');mask: url('data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiAgd2lkdGg9IjE1IiAgaGVpZ2h0PSIxNSIgIHZpZXdCb3g9IjAgMCAyNCAyNCIgIGZpbGw9ImN1cnJlbnRDb2xvciIgIGNsYXNzPSJpY29uIGljb24tdGFibGVyIGljb25zLXRhYmxlci1maWxsZWQgaWNvbi10YWJsZXItYm9va21hcmsiPjxwYXRoIHN0cm9rZT0ibm9uZSIgZD0iTTAgMGgyNHYyNEgweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xNCAyYTUgNSAwIDAgMSA1IDV2MTRhMSAxIDAgMCAxIC0xLjU1NSAuODMybC01LjQ0NSAtMy42M2wtNS40NDQgMy42M2ExIDEgMCAwIDEgLTEuNTUgLS43MmwtLjAwNiAtLjExMnYtMTRhNSA1IDAgMCAxIDUgLTVoNHoiIC8+PC9zdmc+');}.article_icon .save {height: 1.25rem;display: flex;justify-content: center;align-items: center;}.article_icon .save:before {width: 1rem;height: 1rem;}.skeleton-item {list-style-type: none;}.skeleton-image {width: 100%;height: 14rem;background-color: #ddd;border-radius: .5rem;animation: pulse 1.5s infinite ease-in-out;}.skeletion-heading{height: 1.5rem;background-color: #ddd;border-radius: .25rem;animation: pulse 1.5s infinite ease-in-out;margin: .5rem 0;}.skeleton-text {height: 1rem;background-color: #ddd;border-radius: .25rem;animation: pulse 1.5s infinite ease-in-out;margin: .5rem 0;}.skeleton-text:nth-last-child(2) {width: 90%;}.skeleton-text:last-child {width: 70%;}.skeleton-image,.skeletion-heading,.skeleton-text {position: relative;overflow: hidden;background-color: #eee;}.skeleton-image::after,.skeletion-heading::after,.skeleton-text::after {content: "";position: absolute;top: 0;left: -150%;width: 100%;height: 100%;background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);animation: shimmer 1.5s infinite;}.skeleton-item .details{padding:1rem}@keyframes shimmer {0% {left: -150%;}50% {left: 100%;}100% {left: 100%;}}.overlay,.filter_overlay{background: rgba(0,0,0,0);position: fixed;top: 0;left: 0;right: 100%;bottom: 0;z-index: 100;opacity: 0;pointer-events: none;transition: background .2s ease, opacity 0.2s ease;backdrop-filter: blur(.5rem);}.overlay.show,.filter_overlay.show{right: 0;opacity: 1;pointer-events: auto;background: rgba(0,0,0,.5);}.overlay:before,.filter_overlay:before{content: '\00D7';position: fixed;top: 1rem;right: 18.15rem;color: var(--white);font-size: 2rem;line-height: 1;opacity: 0;transform: translateX(20px);background: rgba(255, 255, 255, .25);display: inline-flex;width: 1.5rem;height: 1.5rem;align-items: center;justify-content: center;border-radius: .25rem;cursor: pointer;transition: opacity 0.8s ease, transform 0.4s ease;}.overlay.show:before,.filter_overlay.show:before{opacity: 1;transform: translateX(0);transition-delay: 0.1s;}#menu_modal {transition: all .5s ease;position: fixed;background: #FFF;top: 0;bottom: 0;right: -18rem;width: 18rem;z-index: 100;border-radius: 1rem 0 0 1rem;}#menu_modal.show {right: 0 !important;box-shadow: 0 0.5rem 2rem -0.5rem rgb(28 75 102 / 60%);}.menu_header{padding: 1rem;color: var(--dark);text-align: center;}.menu_header .avatar {background: var(--dark_gray);display: inline-flex;width: 3rem;height: 3rem;align-items: center;justify-content: center;font-size: 1.5rem;font-weight: 600;border-radius: 3rem;margin-bottom: .5rem;color: #FFF;}.menu_header .name{font-weight: 400;}.menu_header .email {color: var(--dark_gray);font-size: .85rem;word-break: break-all;}#menu_modal ul{padding: 1rem;}#menu_modal ul:first-child{margin-top: 1rem;}#menu_modal li {padding: 0 0 .5rem;}#menu_modal li a {color: var(--dark);text-decoration: none;font-weight: 500;display: flex;font-size: 1rem;line-height: 1;padding: .75rem 0;align-items: center;}#menu_modal li svg {height: 1rem;width: 1rem;vertical-align: middle;fill: var(--dark);margin-right: .75rem;}#login_button{margin: 0 1rem 1rem;}.current a{color: var(--light-gray);}#loading {position: fixed;inset: 0;z-index: 99999;background: rgba(0, 0, 0, .5);transition: all .25s ease-out;display: flex;align-items: center;justify-content: center;}#loading:not(.show) {display: none;}.loader {border: .5rem solid var(--dark_gray);border-top: .5rem solid var(--link_blue);border-radius: 50%;width: 5rem;height: 5rem;animation: loading 1s linear infinite;}@keyframes loading {0% {transform: rotate(0deg);}100% {transform: rotate(360deg);}}.content #quiz_header{display: none !important;}.content #quiz_main{margin-top: 1rem;}.content #quiz_main .quiz_btns{display: flex;gap: 1rem;}.content #quiz_main button.skip{background-color: var(--dark_gray);border-color: var(--dark_gray);}.content #progress span{background-color: var(--button_red);}.content .checker input:checked + label {border: solid 1px var(--link_blue);color: var(--link_blue);}li:is(.dashboard,.franchise,.resources,.compare-results,.net-worth-calculator), .back {display: none;}i.bell:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTUgMjFjMCAxLjU5OC0xLjM5MiAzLTIuOTcxIDNzLTMuMDI5LTEuNDAyLTMuMDI5LTNoNnptLjEzNy0xNy4wNTVjLS42NDQtLjM3NC0xLjA0Mi0xLjA3LTEuMDQxLTEuODJ2LS4wMDNjLjAwMS0xLjE3Mi0uOTM4LTIuMTIyLTIuMDk2LTIuMTIycy0yLjA5Ny45NS0yLjA5NyAyLjEyMnYuMDAzYy4wMDEuNzUxLS4zOTYgMS40NDYtMS4wNDEgMS44Mi00LjY2OCAyLjcwOS0xLjk4NSAxMS43MTUtNi44NjIgMTMuMzA2djEuNzQ5aDIwdi0xLjc0OWMtNC44NzctMS41OTEtMi4xOTMtMTAuNTk4LTYuODYzLTEzLjMwNnptLTMuMTM3LTIuOTQ1Yy41NTIgMCAxIC40NDkgMSAxIDAgLjU1Mi0uNDQ4IDEtMSAxcy0xLS40NDgtMS0xYzAtLjU1MS40NDgtMSAxLTF6bS02LjQ1MSAxNmMxLjE4OS0xLjY2NyAxLjYwNS0zLjg5MSAxLjk2NC01LjgxNS40NDctMi4zOS44NjktNC42NDggMi4zNTQtNS41MDkgMS4zOC0uODAxIDIuOTU2LS43NiA0LjI2NyAwIDEuNDg1Ljg2MSAxLjkwNyAzLjExOSAyLjM1NCA1LjUwOS4zNTkgMS45MjQuNzc1IDQuMTQ4IDEuOTY0IDUuODE1aC0xMi45MDN6Ii8+PC9zdmc+');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTUgMjFjMCAxLjU5OC0xLjM5MiAzLTIuOTcxIDNzLTMuMDI5LTEuNDAyLTMuMDI5LTNoNnptLjEzNy0xNy4wNTVjLS42NDQtLjM3NC0xLjA0Mi0xLjA3LTEuMDQxLTEuODJ2LS4wMDNjLjAwMS0xLjE3Mi0uOTM4LTIuMTIyLTIuMDk2LTIuMTIycy0yLjA5Ny45NS0yLjA5NyAyLjEyMnYuMDAzYy4wMDEuNzUxLS4zOTYgMS40NDYtMS4wNDEgMS44Mi00LjY2OCAyLjcwOS0xLjk4NSAxMS43MTUtNi44NjIgMTMuMzA2djEuNzQ5aDIwdi0xLjc0OWMtNC44NzctMS41OTEtMi4xOTMtMTAuNTk4LTYuODYzLTEzLjMwNnptLTMuMTM3LTIuOTQ1Yy41NTIgMCAxIC40NDkgMSAxIDAgLjU1Mi0uNDQ4IDEtMSAxcy0xLS40NDgtMS0xYzAtLjU1MS40NDgtMSAxLTF6bS02LjQ1MSAxNmMxLjE4OS0xLjY2NyAxLjYwNS0zLjg5MSAxLjk2NC01LjgxNS40NDctMi4zOS44NjktNC42NDggMi4zNTQtNS41MDkgMS4zOC0uODAxIDIuOTU2LS43NiA0LjI2NyAwIDEuNDg1Ljg2MSAxLjkwNyAzLjExOSAyLjM1NCA1LjUwOS4zNTkgMS45MjQuNzc1IDQuMTQ4IDEuOTY0IDUuODE1aC0xMi45MDN6Ii8+PC9zdmc+');-webkit-mask-size: contain;mask-size: contain;}i.heart:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNNi4yOCAzYzMuMjM2LjAwMSA0Ljk3MyAzLjQ5MSA1LjcyIDUuMDMxLjc1LTEuNTQ3IDIuNDY5LTUuMDIxIDUuNzI2LTUuMDIxIDIuMDU4IDAgNC4yNzQgMS4zMDkgNC4yNzQgNC4xODIgMCAzLjQ0Mi00Ljc0NCA3Ljg1MS0xMCAxMy01LjI1OC01LjE1MS0xMC05LjU1OS0xMC0xMyAwLTIuNjc2IDEuOTY1LTQuMTkzIDQuMjgtNC4xOTJ6bS4wMDEtMmMtMy4xODMgMC02LjI4MSAyLjE4Ny02LjI4MSA2LjE5MiAwIDQuNjYxIDUuNTcgOS40MjcgMTIgMTUuODA4IDYuNDMtNi4zODEgMTItMTEuMTQ3IDEyLTE1LjgwOCAwLTQuMDExLTMuMDk3LTYuMTgyLTYuMjc0LTYuMTgyLTIuMjA0IDAtNC40NDYgMS4wNDItNS43MjYgMy4yMzgtMS4yODUtMi4yMDYtMy41MjItMy4yNDgtNS43MTktMy4yNDh6Ii8+PC9zdmc+');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNNi4yOCAzYzMuMjM2LjAwMSA0Ljk3MyAzLjQ5MSA1LjcyIDUuMDMxLjc1LTEuNTQ3IDIuNDY5LTUuMDIxIDUuNzI2LTUuMDIxIDIuMDU4IDAgNC4yNzQgMS4zMDkgNC4yNzQgNC4xODIgMCAzLjQ0Mi00Ljc0NCA3Ljg1MS0xMCAxMy01LjI1OC01LjE1MS0xMC05LjU1OS0xMC0xMyAwLTIuNjc2IDEuOTY1LTQuMTkzIDQuMjgtNC4xOTJ6bS4wMDEtMmMtMy4xODMgMC02LjI4MSAyLjE4Ny02LjI4MSA2LjE5MiAwIDQuNjYxIDUuNTcgOS40MjcgMTIgMTUuODA4IDYuNDMtNi4zODEgMTItMTEuMTQ3IDEyLTE1LjgwOCAwLTQuMDExLTMuMDk3LTYuMTgyLTYuMjc0LTYuMTgyLTIuMjA0IDAtNC40NDYgMS4wNDItNS43MjYgMy4yMzgtMS4yODUtMi4yMDYtMy41MjItMy4yNDgtNS43MTktMy4yNDh6Ii8+PC9zdmc+');-webkit-mask-size: contain;mask-size: contain;}i.menu:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNCAyNCI+CiAgPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI5LjUuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDIuMS4wIEJ1aWxkIDEzNykgIC0tPgogIDxwYXRoIGQ9Ik0yNCw1SDB2LTJoMjR2MlpNMjQsMTFIMHYyaDI0di0yWk0yNCwxOEgwdjJoMjR2LTJaIi8+Cjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAyNCAyNCI+CiAgPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI5LjUuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDIuMS4wIEJ1aWxkIDEzNykgIC0tPgogIDxwYXRoIGQ9Ik0yNCw1SDB2LTJoMjR2MlpNMjQsMTFIMHYyaDI0di0yWk0yNCwxOEgwdjJoMjR2LTJaIi8+Cjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.dashboard:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTIgMmMtNi42MjcgMC0xMiA1LjM3My0xMiAxMiAwIDIuNTgzLjU3NSA1LjM0NCAyLjU5OSA4aDE4Ljc1M2MyLjAyMy0yLjY1NiAyLjY0OC01LjQxNyAyLjY0OC04IDAtNi42MjctNS4zNzMtMTItMTItMTJ6bS0uNzU4IDIuMTRjLjI1Ni0uMDE5LjUxLS4wMjkuNzU4LS4wMjlzLjUwMi4wMS43NTguMDI5djMuMTE1Yy0uMjUyLS4wMjgtLjUwNi0uMDQyLS43NTgtLjA0MnMtLjUwNi4wMTQtLjc1OC4wNDJ2LTMuMTE1em0tNS43NjQgNy45NzhsLTIuODgtMS4xOTNjLjE1OC0uNDc5LjM1Mi0uOTQ4LjU4Mi0xLjM5OWwyLjg3OSAxLjE5MmMtLjI0Ny40NDQtLjQ0MS45MTMtLjU4MSAxLjR6bTEuMjE3LTIuMzUxbC0yLjIwMy0yLjIwM2MuMzI5LS4zODMuNjg4LS43NDMgMS4wNzEtMS4wNzFsMi4yMDMgMi4yMDNjLS4zOTUuMzE2LS43NTQuNjc1LTEuMDcxIDEuMDcxem0uNzkzLTQuNTY5Yy40NDktLjIzMS45MTktLjQyOCAxLjM5Ny0uNTg2bDEuMjA1IDIuODc0Yy0uNDg2LjE0Mi0uOTU0LjMzOS0xLjM5Ny41ODZsLTEuMjA1LTIuODc0em0xLjQwNyAxMy44MDJjLjAxOS0xLjE1MS42NTgtMi4xNSAxLjYwMy0yLjY3MmwxLjUwMi03LjA0MSAxLjUwMiA3LjA0MWMuOTQzLjUyMiAxLjU4NCAxLjUyMSAxLjYwMiAyLjY3MmgtNi4yMDl6bTQuOTg5LTExLjUyMmwxLjE5My0yLjg3OGMuNDc5LjE1Ni45NDguMzUyIDEuNC41ODFsLTEuMTkzIDIuODc4Yy0uNDQ0LS4yNDYtLjkxNC0uNDQtMS40LS41ODF6bTIuMzQ5IDEuMjE4bDIuMjAzLTIuMjAzYy4zODMuMzI5Ljc0Mi42ODggMS4wNzEgMS4wNzFsLTIuMjAzIDIuMjAzYy0uMzE2LS4zOTYtLjY3NS0uNzU1LTEuMDcxLTEuMDcxem0yLjI1OSAzLjMyYy0uMTQ3LS40ODMtLjM1LS45NS0uNjAzLTEuMzlsMi44Ni0xLjIzOGMuMjM1LjQ0NS40MzcuOTEyLjYwMiAxLjM5bC0yLjg1OSAxLjIzOHoiLz48L3N2Zz4=');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTIgMmMtNi42MjcgMC0xMiA1LjM3My0xMiAxMiAwIDIuNTgzLjU3NSA1LjM0NCAyLjU5OSA4aDE4Ljc1M2MyLjAyMy0yLjY1NiAyLjY0OC01LjQxNyAyLjY0OC04IDAtNi42MjctNS4zNzMtMTItMTItMTJ6bS0uNzU4IDIuMTRjLjI1Ni0uMDE5LjUxLS4wMjkuNzU4LS4wMjlzLjUwMi4wMS43NTguMDI5djMuMTE1Yy0uMjUyLS4wMjgtLjUwNi0uMDQyLS43NTgtLjA0MnMtLjUwNi4wMTQtLjc1OC4wNDJ2LTMuMTE1em0tNS43NjQgNy45NzhsLTIuODgtMS4xOTNjLjE1OC0uNDc5LjM1Mi0uOTQ4LjU4Mi0xLjM5OWwyLjg3OSAxLjE5MmMtLjI0Ny40NDQtLjQ0MS45MTMtLjU4MSAxLjR6bTEuMjE3LTIuMzUxbC0yLjIwMy0yLjIwM2MuMzI5LS4zODMuNjg4LS43NDMgMS4wNzEtMS4wNzFsMi4yMDMgMi4yMDNjLS4zOTUuMzE2LS43NTQuNjc1LTEuMDcxIDEuMDcxem0uNzkzLTQuNTY5Yy40NDktLjIzMS45MTktLjQyOCAxLjM5Ny0uNTg2bDEuMjA1IDIuODc0Yy0uNDg2LjE0Mi0uOTU0LjMzOS0xLjM5Ny41ODZsLTEuMjA1LTIuODc0em0xLjQwNyAxMy44MDJjLjAxOS0xLjE1MS42NTgtMi4xNSAxLjYwMy0yLjY3MmwxLjUwMi03LjA0MSAxLjUwMiA3LjA0MWMuOTQzLjUyMiAxLjU4NCAxLjUyMSAxLjYwMiAyLjY3MmgtNi4yMDl6bTQuOTg5LTExLjUyMmwxLjE5My0yLjg3OGMuNDc5LjE1Ni45NDguMzUyIDEuNC41ODFsLTEuMTkzIDIuODc4Yy0uNDQ0LS4yNDYtLjkxNC0uNDQtMS40LS41ODF6bTIuMzQ5IDEuMjE4bDIuMjAzLTIuMjAzYy4zODMuMzI5Ljc0Mi42ODggMS4wNzEgMS4wNzFsLTIuMjAzIDIuMjAzYy0uMzE2LS4zOTYtLjY3NS0uNzU1LTEuMDcxLTEuMDcxem0yLjI1OSAzLjMyYy0uMTQ3LS40ODMtLjM1LS45NS0uNjAzLTEuMzlsMi44Ni0xLjIzOGMuMjM1LjQ0NS40MzcuOTEyLjYwMiAxLjM5bC0yLjg1OSAxLjIzOHoiLz48L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.home:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iSG9tZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj4KICA8ZGVmcz4KICAgIDxzdHlsZT4KICAgICAgLmNscy0xIHsKICAgICAgICBmaWxsOiAjMGMwMGU5OwogICAgICAgIHN0cm9rZS13aWR0aDogMHB4OwogICAgICB9CiAgICA8L3N0eWxlPgogIDwvZGVmcz4KICA8cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik04LjA5LDQ5LjE0Yy0uNzgsMC0xLjU2LS4zLTIuMTQtLjktMS4xNi0xLjE4LTEuMTQtMy4wOC4wNS00LjI0TDQ0LjUyLDYuMjhjLjY4LS44MywzLjEtMy40NCw2LjU4LTMuNDRzNi4zNiwyLjk0LDYuOTYsMy42MmwzNy4xNiwzNy41N2MxLjE3LDEuMTgsMS4xNiwzLjA4LS4wMiw0LjI0LTEuMTgsMS4xNy0zLjA4LDEuMTYtNC4yNC0uMDJMNTMuNzIsMTAuNjFjLS4wNS0uMDYtLjExLS4xMS0uMTYtLjE3LS41Ny0uNjYtMS44LTEuNi0yLjQ3LTEuNi0uNzEsMC0xLjY4LjktMS45NSwxLjI1LS4wOS4xMi0uMTkuMjQtLjMuMzRMMTAuMTksNDguMjhjLS41OC41Ny0xLjM0Ljg2LTIuMS44NloiLz4KICA8cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik03NS45LDE5Ljg1bDkuNzIsOS40MVY5Ljgzcy0uMS0zLjE3LTMuNjgtMy4xN2gtMi41NnMtMy40OC0uMzEtMy40OCwyLjk3djEwLjIzWiIvPgogIDxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSI1MC44NiAxMi45IDE0LjQzIDQ5LjAyIDE0LjQzIDk0Ljg4IDM2LjkxIDk0Ljg4IDM2LjkxIDUyLjE0IDYzLjA5IDUyLjE0IDYzLjA5IDk0Ljg4IDg3Ljc2IDk0Ljg4IDg3Ljc2IDQ5LjAyIDUwLjg2IDEyLjkiLz4KPC9zdmc+');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iSG9tZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2ZXJzaW9uPSIxLjEiIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj4KICA8ZGVmcz4KICAgIDxzdHlsZT4KICAgICAgLmNscy0xIHsKICAgICAgICBmaWxsOiAjMGMwMGU5OwogICAgICAgIHN0cm9rZS13aWR0aDogMHB4OwogICAgICB9CiAgICA8L3N0eWxlPgogIDwvZGVmcz4KICA8cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik04LjA5LDQ5LjE0Yy0uNzgsMC0xLjU2LS4zLTIuMTQtLjktMS4xNi0xLjE4LTEuMTQtMy4wOC4wNS00LjI0TDQ0LjUyLDYuMjhjLjY4LS44MywzLjEtMy40NCw2LjU4LTMuNDRzNi4zNiwyLjk0LDYuOTYsMy42MmwzNy4xNiwzNy41N2MxLjE3LDEuMTgsMS4xNiwzLjA4LS4wMiw0LjI0LTEuMTgsMS4xNy0zLjA4LDEuMTYtNC4yNC0uMDJMNTMuNzIsMTAuNjFjLS4wNS0uMDYtLjExLS4xMS0uMTYtLjE3LS41Ny0uNjYtMS44LTEuNi0yLjQ3LTEuNi0uNzEsMC0xLjY4LjktMS45NSwxLjI1LS4wOS4xMi0uMTkuMjQtLjMuMzRMMTAuMTksNDguMjhjLS41OC41Ny0xLjM0Ljg2LTIuMS44NloiLz4KICA8cGF0aCBjbGFzcz0iY2xzLTEiIGQ9Ik03NS45LDE5Ljg1bDkuNzIsOS40MVY5Ljgzcy0uMS0zLjE3LTMuNjgtMy4xN2gtMi41NnMtMy40OC0uMzEtMy40OCwyLjk3djEwLjIzWiIvPgogIDxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSI1MC44NiAxMi45IDE0LjQzIDQ5LjAyIDE0LjQzIDk0Ljg4IDM2LjkxIDk0Ljg4IDM2LjkxIDUyLjE0IDYzLjA5IDUyLjE0IDYzLjA5IDk0Ljg4IDg3Ljc2IDk0Ljg4IDg3Ljc2IDQ5LjAyIDUwLjg2IDEyLjkiLz4KPC9zdmc+');-webkit-mask-size: contain;mask-size: contain;}i.franchise:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iZnJhbmNoaXNlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDEwMCAxMDAiPgogIDxkZWZzPgogICAgPHN0eWxlPgogICAgICAuY2xzLTEgewogICAgICAgIGZpbGw6IGJsdWU7CiAgICAgICAgc3Ryb2tlLXdpZHRoOiAwcHg7CiAgICAgIH0KICAgIDwvc3R5bGU+CiAgPC9kZWZzPgogIDxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTE1LjAzLDE1LjM2aDY5LjQ5YzIuOTEsMCw1LjI4LTIuNDYsNS4yOC01LjVzLTIuMzYtNS41LTUuMjgtNS41SDE1LjAzYy0yLjkyLDAtNS4yOCwyLjQ2LTUuMjgsNS41czIuMzYsNS41LDUuMjgsNS41WiIvPgogIDxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTk1LjE2LDQ0LjA5bC01Ljg2LTE3LjQ1Yy4zMi0uNzEuNS0xLjQ5LjUtMi4zMiwwLTMuMDQtMi4zNi01LjUtNS4yOC01LjVIMTUuMDNjLTIuOTIsMC01LjI4LDIuNDYtNS4yOCw1LjUsMCwuOTQuMjMsMS44Mi42MiwyLjU5bC01LjU1LDE3LjIyYy0uNTEsMS41OC4zNiwzLjI3LDEuOTQsMy43OC4zMS4xLjYyLjE1LjkyLjE1Ljc4LDAsMS41Mi0uMzEsMi4wNy0uODN2NDguNDJoNTAuNTl2LTM2LjY4aDE4LjU1djM2LjY4aDEwLjkxdi00OC45N2MuNTUuODYsMS41LDEuMzgsMi41MSwxLjM4LjMyLDAsLjY0LS4wNS45NS0uMTYsMS41Ny0uNTMsMi40Mi0yLjIzLDEuODktMy44Wk0zMC45MSwyOS44Mmg4LjE4Yy0uNDUsNC44NC0xLjM3LDE0LjcxLTEuMywxNS4yMywwLDIuNzQtMi4yMiw0Ljk1LTQuOTUsNC45NXMtNC45NS0yLjIyLTQuOTUtNC45NWwzLjAzLTE1LjIzWk0xNS43Niw1MGMtMi43MSwwLTQuOS0yLjE3LTQuOTUtNC44N2w0Ljk0LTE1LjMxaDguM2wtMy4zMywxNS4yM2MwLDIuNzQtMi4yMiw0Ljk1LTQuOTUsNC45NVpNNDkuNjksODQuMzJoLTI4Ljg5di0yNS4zNmgyOC44OXYyNS4zNlpNNDkuODgsNTBjLTIuNjEsMC00LjczLTIuMDMtNC45Mi00LjU5bC4yMS0xNS41OWg4LjlsLjc2LDE1LjIzYzAsMi43NC0yLjIyLDQuOTUtNC45NSw0Ljk1Wk02Ny4yLDUwYy0yLjc0LDAtNC45NS0yLjIyLTQuOTUtNC45NWwtMi4wOC0xNS4yM2g5LjIybDIuNzcsMTUuMjNjMCwyLjc0LTIuMjIsNC45NS00Ljk1LDQuOTVaTTg0LjIxLDUwYy0yLjc0LDAtNC45NS0yLjIyLTQuOTUtNC45NWwtMy43Ny0xNS4yM2g4LjU1bDUuMTIsMTUuMjdjLS4wMiwyLjcyLTIuMjMsNC45MS00Ljk1LDQuOTFaIi8+Cjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyBpZD0iZnJhbmNoaXNlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgdmlld0JveD0iMCAwIDEwMCAxMDAiPgogIDxkZWZzPgogICAgPHN0eWxlPgogICAgICAuY2xzLTEgewogICAgICAgIGZpbGw6IGJsdWU7CiAgICAgICAgc3Ryb2tlLXdpZHRoOiAwcHg7CiAgICAgIH0KICAgIDwvc3R5bGU+CiAgPC9kZWZzPgogIDxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTE1LjAzLDE1LjM2aDY5LjQ5YzIuOTEsMCw1LjI4LTIuNDYsNS4yOC01LjVzLTIuMzYtNS41LTUuMjgtNS41SDE1LjAzYy0yLjkyLDAtNS4yOCwyLjQ2LTUuMjgsNS41czIuMzYsNS41LDUuMjgsNS41WiIvPgogIDxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTTk1LjE2LDQ0LjA5bC01Ljg2LTE3LjQ1Yy4zMi0uNzEuNS0xLjQ5LjUtMi4zMiwwLTMuMDQtMi4zNi01LjUtNS4yOC01LjVIMTUuMDNjLTIuOTIsMC01LjI4LDIuNDYtNS4yOCw1LjUsMCwuOTQuMjMsMS44Mi42MiwyLjU5bC01LjU1LDE3LjIyYy0uNTEsMS41OC4zNiwzLjI3LDEuOTQsMy43OC4zMS4xLjYyLjE1LjkyLjE1Ljc4LDAsMS41Mi0uMzEsMi4wNy0uODN2NDguNDJoNTAuNTl2LTM2LjY4aDE4LjU1djM2LjY4aDEwLjkxdi00OC45N2MuNTUuODYsMS41LDEuMzgsMi41MSwxLjM4LjMyLDAsLjY0LS4wNS45NS0uMTYsMS41Ny0uNTMsMi40Mi0yLjIzLDEuODktMy44Wk0zMC45MSwyOS44Mmg4LjE4Yy0uNDUsNC44NC0xLjM3LDE0LjcxLTEuMywxNS4yMywwLDIuNzQtMi4yMiw0Ljk1LTQuOTUsNC45NXMtNC45NS0yLjIyLTQuOTUtNC45NWwzLjAzLTE1LjIzWk0xNS43Niw1MGMtMi43MSwwLTQuOS0yLjE3LTQuOTUtNC44N2w0Ljk0LTE1LjMxaDguM2wtMy4zMywxNS4yM2MwLDIuNzQtMi4yMiw0Ljk1LTQuOTUsNC45NVpNNDkuNjksODQuMzJoLTI4Ljg5di0yNS4zNmgyOC44OXYyNS4zNlpNNDkuODgsNTBjLTIuNjEsMC00LjczLTIuMDMtNC45Mi00LjU5bC4yMS0xNS41OWg4LjlsLjc2LDE1LjIzYzAsMi43NC0yLjIyLDQuOTUtNC45NSw0Ljk1Wk02Ny4yLDUwYy0yLjc0LDAtNC45NS0yLjIyLTQuOTUtNC45NWwtMi4wOC0xNS4yM2g5LjIybDIuNzcsMTUuMjNjMCwyLjc0LTIuMjIsNC45NS00Ljk1LDQuOTVaTTg0LjIxLDUwYy0yLjc0LDAtNC45NS0yLjIyLTQuOTUtNC45NWwtMy43Ny0xNS4yM2g4LjU1bDUuMTIsMTUuMjdjLS4wMiwyLjcyLTIuMjMsNC45MS00Ljk1LDQuOTFaIi8+Cjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.resources:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMgNXYxMy44ODNsLTEgLjExN3YtMTZjLTMuODk1LjExOS03LjUwNS43NjItMTAuMDAyIDIuMzE2LTIuNDk2LTEuNTU0LTYuMTAyLTIuMTk3LTkuOTk4LTIuMzE2djE2bC0xLS4xMTd2LTEzLjg4M2gtMXYxNWg5LjA1N2MxLjQ3OSAwIDEuNjQxIDEgMi45NDEgMSAxLjMwNCAwIDEuNDYxLTEgMi45NDItMWg5LjA2di0xNWgtMXptLTEyIDEzLjY0NWMtMS45NDYtLjc3Mi00LjEzNy0xLjI2OS03LTEuNDg0di0xMi4wNTFjMi4zNTIuMTk3IDQuOTk2LjY3NSA3IDEuOTIydjExLjYxM3ptOS0xLjQ4NGMtMi44NjMuMjE1LTUuMDU0LjcxMi03IDEuNDg0di0xMS42MTNjMi4wMDQtMS4yNDcgNC42NDgtMS43MjUgNy0xLjkyMnYxMi4wNTF6Ii8+PC9zdmc+');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMgNXYxMy44ODNsLTEgLjExN3YtMTZjLTMuODk1LjExOS03LjUwNS43NjItMTAuMDAyIDIuMzE2LTIuNDk2LTEuNTU0LTYuMTAyLTIuMTk3LTkuOTk4LTIuMzE2djE2bC0xLS4xMTd2LTEzLjg4M2gtMXYxNWg5LjA1N2MxLjQ3OSAwIDEuNjQxIDEgMi45NDEgMSAxLjMwNCAwIDEuNDYxLTEgMi45NDItMWg5LjA2di0xNWgtMXptLTEyIDEzLjY0NWMtMS45NDYtLjc3Mi00LjEzNy0xLjI2OS03LTEuNDg0di0xMi4wNTFjMi4zNTIuMTk3IDQuOTk2LjY3NSA3IDEuOTIydjExLjYxM3ptOS0xLjQ4NGMtMi44NjMuMjE1LTUuMDU0LjcxMi03IDEuNDg0di0xMS42MTNjMi4wMDQtMS4yNDcgNC42NDgtMS43MjUgNy0xLjkyMnYxMi4wNTF6Ii8+PC9zdmc+');-webkit-mask-size: contain;mask-size: contain;}i.tools:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMuMjcgMTkuNzQzbC0xMS45NDYtMTEuOTQ1Yy0uNTU3LS41NTctLjg0Mi0xLjMzMS0uNzgzLTIuMTE1LjExNS0xLjQ4NS0uMzk1LTMuMDA5LTEuNTI5LTQuMTQ2LTEuMDMtMS4wMjgtMi4zNzUtMS41MzctMy43MjMtMS41MzctLjUwNyAwLTEuMDE1LjA3Mi0xLjUwNi4yMTZsMy4xNyAzLjE3Yy4zNDQgMS41ODktMS45NTkgMy45MTgtMy41NjYgMy41NjdsLTMuMTctMy4xN2MtLjE0NS40OTItLjIxNyAxLS4yMTcgMS41MDkgMCAxLjM0Ny41MSAyLjY5MSAxLjUzNyAzLjcyMSAxLjEzNSAxLjEzNiAyLjY2IDEuNjQ2IDQuMTQ2IDEuNTMuNzgzLS4wNiAxLjU1Ny4yMjYgMi4xMTMuNzgzbDExLjk0NyAxMS45NDRjLjQ2OC40NjggMS4xMDMuNzMgMS43NjMuNzMgMS4zNjggMCAyLjQ5NC0xLjEwOCAyLjQ5NC0yLjQ5NCAwLS42MzgtLjI0NC0xLjI3Ni0uNzMtMS43NjN6bS0xLjc3IDIuNzU3Yy0uNTUzIDAtMS0uNDQ4LTEtMXMuNDQ3LTEgMS0xIDEgLjQ0OCAxIDEtLjQ0NyAxLTEgMXptLTguMzc1LTE1Ljc1M2w2LjcyMy02Ljc0NyA0LjE1MiA0LjEyOC02LjcyMiA2Ljc3MS0xLjAxMi0xLjAxMiA1LjQ4OC01LjUzM2MuMTY1LS4xNjUuMTY1LS40MzUtLjAwMS0uNjAyLS4xNjYtLjE2NS0uNDM2LS4xNjUtLjYwMSAwbC01LjQ4OSA1LjUzMy0uOTM1LS45MzYgNS40OTUtNS41MzljLjE2Ni0uMTY2LjE2Ni0uNDM3IDAtLjYwMy0uMTY4LS4xNjYtLjQzNi0uMTY2LS42MDMuMDAxbC01LjQ5NCA1LjUzOS0xLjAwMS0xem0tMy4xODcgOS41MjFsLTUuMzA4IDUuMzVjLS4xNjYuMTY2LS40MzcuMTY2LS42MDMgMC0uMTY1LS4xNjYtLjE2Ni0uNDM2IDAtLjYwMmw1LjMwOC01LjM1MS0uOTM2LS45MzUtNS4zMDEgNS4zNDNjLS4xNjUuMTY4LS40MzUuMTY3LS42MDEuMDAxLS4xNjYtLjE2Ny0uMTY2LS40MzYgMC0uNjAybDUuMy01LjM0My0xLjAwNC0xLjAwNC01Ljc0NSA1Ljc4Ny0xLjA0OCA1LjA4OCA1LjIwMy0uOTM3IDUuNzQzLTUuNzg2LTEuMDA4LTEuMDA5eiIvPjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjMuMjcgMTkuNzQzbC0xMS45NDYtMTEuOTQ1Yy0uNTU3LS41NTctLjg0Mi0xLjMzMS0uNzgzLTIuMTE1LjExNS0xLjQ4NS0uMzk1LTMuMDA5LTEuNTI5LTQuMTQ2LTEuMDMtMS4wMjgtMi4zNzUtMS41MzctMy43MjMtMS41MzctLjUwNyAwLTEuMDE1LjA3Mi0xLjUwNi4yMTZsMy4xNyAzLjE3Yy4zNDQgMS41ODktMS45NTkgMy45MTgtMy41NjYgMy41NjdsLTMuMTctMy4xN2MtLjE0NS40OTItLjIxNyAxLS4yMTcgMS41MDkgMCAxLjM0Ny41MSAyLjY5MSAxLjUzNyAzLjcyMSAxLjEzNSAxLjEzNiAyLjY2IDEuNjQ2IDQuMTQ2IDEuNTMuNzgzLS4wNiAxLjU1Ny4yMjYgMi4xMTMuNzgzbDExLjk0NyAxMS45NDRjLjQ2OC40NjggMS4xMDMuNzMgMS43NjMuNzMgMS4zNjggMCAyLjQ5NC0xLjEwOCAyLjQ5NC0yLjQ5NCAwLS42MzgtLjI0NC0xLjI3Ni0uNzMtMS43NjN6bS0xLjc3IDIuNzU3Yy0uNTUzIDAtMS0uNDQ4LTEtMXMuNDQ3LTEgMS0xIDEgLjQ0OCAxIDEtLjQ0NyAxLTEgMXptLTguMzc1LTE1Ljc1M2w2LjcyMy02Ljc0NyA0LjE1MiA0LjEyOC02LjcyMiA2Ljc3MS0xLjAxMi0xLjAxMiA1LjQ4OC01LjUzM2MuMTY1LS4xNjUuMTY1LS40MzUtLjAwMS0uNjAyLS4xNjYtLjE2NS0uNDM2LS4xNjUtLjYwMSAwbC01LjQ4OSA1LjUzMy0uOTM1LS45MzYgNS40OTUtNS41MzljLjE2Ni0uMTY2LjE2Ni0uNDM3IDAtLjYwMy0uMTY4LS4xNjYtLjQzNi0uMTY2LS42MDMuMDAxbC01LjQ5NCA1LjUzOS0xLjAwMS0xem0tMy4xODcgOS41MjFsLTUuMzA4IDUuMzVjLS4xNjYuMTY2LS40MzcuMTY2LS42MDMgMC0uMTY1LS4xNjYtLjE2Ni0uNDM2IDAtLjYwMmw1LjMwOC01LjM1MS0uOTM2LS45MzUtNS4zMDEgNS4zNDNjLS4xNjUuMTY4LS40MzUuMTY3LS42MDEuMDAxLS4xNjYtLjE2Ny0uMTY2LS40MzYgMC0uNjAybDUuMy01LjM0My0xLjAwNC0xLjAwNC01Ljc0NSA1Ljc4Ny0xLjA0OCA1LjA4OCA1LjIwMy0uOTM3IDUuNzQzLTUuNzg2LTEuMDA4LTEuMDA5eiIvPjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.eyeopen:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHRpdGxlPmlvbmljb25zLXY1LWk8L3RpdGxlPjxwYXRoIGQ9Ik0yNTUuNjYsMTEyYy03Ny45NCwwLTE1Ny44OSw0NS4xMS0yMjAuODMsMTM1LjMzYTE2LDE2LDAsMCwwLS4yNywxNy43N0M4Mi45MiwzNDAuOCwxNjEuOCw0MDAsMjU1LjY2LDQwMCwzNDguNSw0MDAsNDI5LDM0MC42Miw0NzcuNDUsMjY0Ljc1YTE2LjE0LDE2LjE0LDAsMCwwLDAtMTcuNDdDNDI4Ljg5LDE3Mi4yOCwzNDcuOCwxMTIsMjU1LjY2LDExMloiIHN0eWxlPSJmaWxsOm5vbmU7c3Ryb2tlOiMwMDAwMDA7c3Ryb2tlLWxpbmVjYXA6cm91bmQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS13aWR0aDozMnB4Ii8+PGNpcmNsZSBjeD0iMjU2IiBjeT0iMjU2IiByPSI4MCIgc3R5bGU9ImZpbGw6bm9uZTtzdHJva2U6IzAwMDAwMDtzdHJva2UtbWl0ZXJsaW1pdDoxMDtzdHJva2Utd2lkdGg6MzJweCIvPjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHRpdGxlPmlvbmljb25zLXY1LWk8L3RpdGxlPjxwYXRoIGQ9Ik0yNTUuNjYsMTEyYy03Ny45NCwwLTE1Ny44OSw0NS4xMS0yMjAuODMsMTM1LjMzYTE2LDE2LDAsMCwwLS4yNywxNy43N0M4Mi45MiwzNDAuOCwxNjEuOCw0MDAsMjU1LjY2LDQwMCwzNDguNSw0MDAsNDI5LDM0MC42Miw0NzcuNDUsMjY0Ljc1YTE2LjE0LDE2LjE0LDAsMCwwLDAtMTcuNDdDNDI4Ljg5LDE3Mi4yOCwzNDcuOCwxMTIsMjU1LjY2LDExMloiIHN0eWxlPSJmaWxsOm5vbmU7c3Ryb2tlOiMwMDAwMDA7c3Ryb2tlLWxpbmVjYXA6cm91bmQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS13aWR0aDozMnB4Ii8+PGNpcmNsZSBjeD0iMjU2IiBjeT0iMjU2IiByPSI4MCIgc3R5bGU9ImZpbGw6bm9uZTtzdHJva2U6IzAwMDAwMDtzdHJva2UtbWl0ZXJsaW1pdDoxMDtzdHJva2Utd2lkdGg6MzJweCIvPjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.eyeclose:before {-webkit-mask: url('data:image/svg+xml;base64,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');mask: url('data:image/svg+xml;base64,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');-webkit-mask-size: contain;mask-size: contain;}i.eyeclose.open:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHRpdGxlPmlvbmljb25zLXY1LWk8L3RpdGxlPjxwYXRoIGQ9Ik0yNTUuNjYsMTEyYy03Ny45NCwwLTE1Ny44OSw0NS4xMS0yMjAuODMsMTM1LjMzYTE2LDE2LDAsMCwwLS4yNywxNy43N0M4Mi45MiwzNDAuOCwxNjEuOCw0MDAsMjU1LjY2LDQwMCwzNDguNSw0MDAsNDI5LDM0MC42Miw0NzcuNDUsMjY0Ljc1YTE2LjE0LDE2LjE0LDAsMCwwLDAtMTcuNDdDNDI4Ljg5LDE3Mi4yOCwzNDcuOCwxMTIsMjU1LjY2LDExMloiIHN0eWxlPSJmaWxsOm5vbmU7c3Ryb2tlOiMwMDAwMDA7c3Ryb2tlLWxpbmVjYXA6cm91bmQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS13aWR0aDozMnB4Ii8+PGNpcmNsZSBjeD0iMjU2IiBjeT0iMjU2IiByPSI4MCIgc3R5bGU9ImZpbGw6bm9uZTtzdHJva2U6IzAwMDAwMDtzdHJva2UtbWl0ZXJsaW1pdDoxMDtzdHJva2Utd2lkdGg6MzJweCIvPjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDUxMiA1MTIiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHRpdGxlPmlvbmljb25zLXY1LWk8L3RpdGxlPjxwYXRoIGQ9Ik0yNTUuNjYsMTEyYy03Ny45NCwwLTE1Ny44OSw0NS4xMS0yMjAuODMsMTM1LjMzYTE2LDE2LDAsMCwwLS4yNywxNy43N0M4Mi45MiwzNDAuOCwxNjEuOCw0MDAsMjU1LjY2LDQwMCwzNDguNSw0MDAsNDI5LDM0MC42Miw0NzcuNDUsMjY0Ljc1YTE2LjE0LDE2LjE0LDAsMCwwLDAtMTcuNDdDNDI4Ljg5LDE3Mi4yOCwzNDcuOCwxMTIsMjU1LjY2LDExMloiIHN0eWxlPSJmaWxsOm5vbmU7c3Ryb2tlOiMwMDAwMDA7c3Ryb2tlLWxpbmVjYXA6cm91bmQ7c3Ryb2tlLWxpbmVqb2luOnJvdW5kO3N0cm9rZS13aWR0aDozMnB4Ii8+PGNpcmNsZSBjeD0iMjU2IiBjeT0iMjU2IiByPSI4MCIgc3R5bGU9ImZpbGw6bm9uZTtzdHJva2U6IzAwMDAwMDtzdHJva2UtbWl0ZXJsaW1pdDoxMDtzdHJva2Utd2lkdGg6MzJweCIvPjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.search:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGQ9Ik0xNC45NTM2IDE0Ljk0NThMMjEgMjFNMTcgMTBDMTcgMTMuODY2IDEzLjg2NiAxNyAxMCAxN0M2LjEzNDAxIDE3IDMgMTMuODY2IDMgMTBDMyA2LjEzNDAxIDYuMTM0MDEgMyAxMCAzQzEzLjg2NiAzIDE3IDYuMTM0MDEgMTcgMTBaIiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGQ9Ik0xNC45NTM2IDE0Ljk0NThMMjEgMjFNMTcgMTBDMTcgMTMuODY2IDEzLjg2NiAxNyAxMCAxN0M2LjEzNDAxIDE3IDMgMTMuODY2IDMgMTBDMyA2LjEzNDAxIDYuMTM0MDEgMyAxMCAzQzEzLjg2NiAzIDE3IDYuMTM0MDEgMTcgMTBaIiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.aichat:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iIzAwMDAwMCIgZD0iTTE2MCA4MjYuODggMjczLjUzNiA3MzZIODAwYTY0IDY0IDAgMCAwIDY0LTY0VjI1NmE2NCA2NCAwIDAgMC02NC02NEgyMjRhNjQgNjQgMCAwIDAtNjQgNjR2NTcwLjg4ek0yOTYgODAwIDE0Ny45NjggOTE4LjRBMzIgMzIgMCAwIDEgOTYgODkzLjQ0VjI1NmExMjggMTI4IDAgMCAxIDEyOC0xMjhoNTc2YTEyOCAxMjggMCAwIDEgMTI4IDEyOHY0MTZhMTI4IDEyOCAwIDAgMS0xMjggMTI4SDI5NnoiLz48cGF0aCBmaWxsPSIjMDAwMDAwIiBkPSJNMzUyIDUxMmgzMjBxMzIgMCAzMiAzMnQtMzIgMzJIMzUycS0zMiAwLTMyLTMydDMyLTMyem0wLTE5MmgzMjBxMzIgMCAzMiAzMnQtMzIgMzJIMzUycS0zMiAwLTMyLTMydDMyLTMyeiIvPjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0iIzAwMDAwMCIgZD0iTTE2MCA4MjYuODggMjczLjUzNiA3MzZIODAwYTY0IDY0IDAgMCAwIDY0LTY0VjI1NmE2NCA2NCAwIDAgMC02NC02NEgyMjRhNjQgNjQgMCAwIDAtNjQgNjR2NTcwLjg4ek0yOTYgODAwIDE0Ny45NjggOTE4LjRBMzIgMzIgMCAwIDEgOTYgODkzLjQ0VjI1NmExMjggMTI4IDAgMCAxIDEyOC0xMjhoNTc2YTEyOCAxMjggMCAwIDEgMTI4IDEyOHY0MTZhMTI4IDEyOCAwIDAgMS0xMjggMTI4SDI5NnoiLz48cGF0aCBmaWxsPSIjMDAwMDAwIiBkPSJNMzUyIDUxMmgzMjBxMzIgMCAzMiAzMnQtMzIgMzJIMzUycS0zMiAwLTMyLTMydDMyLTMyem0wLTE5MmgzMjBxMzIgMCAzMiAzMnQtMzIgMzJIMzUycS0zMiAwLTMyLTMydDMyLTMyeiIvPjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.networth:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KPHBhdGggZD0iTTMgMTFMMyAyMU0xNSAxMUwxNSAyMU05IDNMOSAyMU0yMSAzVjIxIiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KPHBhdGggZD0iTTMgMTFMMyAyMU0xNSAxMUwxNSAyMU05IDNMOSAyMU0yMSAzVjIxIiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.checkM:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIGZpbGw9IiMwMDAwMDAiIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTkuOTEwMDc0MjEsMTcuNDk1ODYwNyBMMy43MDcxMDY3OCwxMS4yOTI4OTMyIEMzLjMxNjU4MjQ5LDEwLjkwMjM2ODkgMi42ODM0MTc1MSwxMC45MDIzNjg5IDIuMjkyODkzMjIsMTEuMjkyODkzMiBDMS45MDIzNjg5MywxMS42ODM0MTc1IDEuOTAyMzY4OTMsMTIuMzE2NTgyNSAyLjI5Mjg5MzIyLDEyLjcwNzEwNjggTDkuMjkyODkzMjIsMTkuNzA3MTA2OCBDOS43MTY4MTk5MiwyMC4xMzEwMzM1IDEwLjQxNTkyMDIsMjAuMDg5MjM3NCAxMC43ODYzMTgzLDE5LjYxNzgyMTYgTDIxLjc4NjMxODMsNS42MTc4MjE1NSBDMjIuMTI3NTMxOCw1LjE4MzU0OTkyIDIyLjA1MjA5MzIsNC41NTQ4OTUwOCAyMS42MTc4MjE2LDQuMjEzNjgxNjYgQzIxLjE4MzU0OTksMy44NzI0NjgyNCAyMC41NTQ4OTUxLDMuOTQ3OTA2ODIgMjAuMjEzNjgxNyw0LjM4MjE3ODQ1IEw5LjkxMDA3NDIxLDE3LjQ5NTg2MDcgWiIvPgo8L3N2Zz4=');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIGZpbGw9IiMwMDAwMDAiIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgZD0iTTkuOTEwMDc0MjEsMTcuNDk1ODYwNyBMMy43MDcxMDY3OCwxMS4yOTI4OTMyIEMzLjMxNjU4MjQ5LDEwLjkwMjM2ODkgMi42ODM0MTc1MSwxMC45MDIzNjg5IDIuMjkyODkzMjIsMTEuMjkyODkzMiBDMS45MDIzNjg5MywxMS42ODM0MTc1IDEuOTAyMzY4OTMsMTIuMzE2NTgyNSAyLjI5Mjg5MzIyLDEyLjcwNzEwNjggTDkuMjkyODkzMjIsMTkuNzA3MTA2OCBDOS43MTY4MTk5MiwyMC4xMzEwMzM1IDEwLjQxNTkyMDIsMjAuMDg5MjM3NCAxMC43ODYzMTgzLDE5LjYxNzgyMTYgTDIxLjc4NjMxODMsNS42MTc4MjE1NSBDMjIuMTI3NTMxOCw1LjE4MzU0OTkyIDIyLjA1MjA5MzIsNC41NTQ4OTUwOCAyMS42MTc4MjE2LDQuMjEzNjgxNjYgQzIxLjE4MzU0OTksMy44NzI0NjgyNCAyMC41NTQ4OTUxLDMuOTQ3OTA2ODIgMjAuMjEzNjgxNyw0LjM4MjE3ODQ1IEw5LjkxMDA3NDIxLDE3LjQ5NTg2MDcgWiIvPgo8L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.checkcircle:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEwIiBzdHJva2U9IiMxQzI3NEMiIHN0cm9rZS13aWR0aD0iMS41Ii8+DQo8cGF0aCBkPSJNOC41IDEyLjVMMTAuNSAxNC41TDE1LjUgOS41IiBzdHJva2U9IiMxQzI3NEMiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjEwIiBzdHJva2U9IiMxQzI3NEMiIHN0cm9rZS13aWR0aD0iMS41Ii8+DQo8cGF0aCBkPSJNOC41IDEyLjVMMTAuNSAxNC41TDE1LjUgOS41IiBzdHJva2U9IiMxQzI3NEMiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.newsletter:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBVcGxvYWRlZCB0bzogU1ZHIFJlcG8sIHd3dy5zdmdyZXBvLmNvbSwgR2VuZXJhdG9yOiBTVkcgUmVwbyBNaXhlciBUb29scyAtLT4KPHN2ZyBmaWxsPSIjMDAwMDAwIiBoZWlnaHQ9IjgwMHB4IiB3aWR0aD0iODAwcHgiIHZlcnNpb249IjEuMiIgYmFzZVByb2ZpbGU9InRpbnkiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIgoJICB2aWV3Qm94PSIwIDAgMjU2IDIzNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxwYXRoIGQ9Ik0yNTAsMTA3LjZ2LTAuM2wtMTkuNC0zNC4xdjQ3LjFsLTEyLjIsOFY3LjJIMzcuMVYxMjhsLTEyLjItOFY3My4yTDUuNSwxMDcuM3YxMjIuNUgxMjhoMTIyLjVWMTA3LjNMMjUwLDEwNy42eiBNNDguOCwxOC45CgloMTU3Ljl2MTE3LjFMMTI4LDE4Ny43bC03OS4yLTUyVjE4Ljl6IE05NC43LDY1SDY4LjhWMzkuMWgyNS45VjY1eiBNMTg3LjMsNjVoLTc5LjhWNDcuNmg3OS44VjY1eiBNMTg3LjMsOTcuNEg2OC44VjgwaDExOC41Vjk3LjR6CgkgTTE4Ny4zLDEyOS45SDY4Ljh2LTE3LjRoMTE4LjVWMTI5Ljl6Ii8+Cjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBVcGxvYWRlZCB0bzogU1ZHIFJlcG8sIHd3dy5zdmdyZXBvLmNvbSwgR2VuZXJhdG9yOiBTVkcgUmVwbyBNaXhlciBUb29scyAtLT4KPHN2ZyBmaWxsPSIjMDAwMDAwIiBoZWlnaHQ9IjgwMHB4IiB3aWR0aD0iODAwcHgiIHZlcnNpb249IjEuMiIgYmFzZVByb2ZpbGU9InRpbnkiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIgoJICB2aWV3Qm94PSIwIDAgMjU2IDIzNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxwYXRoIGQ9Ik0yNTAsMTA3LjZ2LTAuM2wtMTkuNC0zNC4xdjQ3LjFsLTEyLjIsOFY3LjJIMzcuMVYxMjhsLTEyLjItOFY3My4yTDUuNSwxMDcuM3YxMjIuNUgxMjhoMTIyLjVWMTA3LjNMMjUwLDEwNy42eiBNNDguOCwxOC45CgloMTU3Ljl2MTE3LjFMMTI4LDE4Ny43bC03OS4yLTUyVjE4Ljl6IE05NC43LDY1SDY4LjhWMzkuMWgyNS45VjY1eiBNMTg3LjMsNjVoLTc5LjhWNDcuNmg3OS44VjY1eiBNMTg3LjMsOTcuNEg2OC44VjgwaDExOC41Vjk3LjR6CgkgTTE4Ny4zLDEyOS45SDY4Ljh2LTE3LjRoMTE4LjVWMTI5Ljl6Ii8+Cjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.visit:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KPHBhdGggZD0iTTIxIDlMMjEgM00yMSAzSDE1TTIxIDNMMTMgMTFNMTAgNUg3LjhDNi4xMTk4NCA1IDUuMjc5NzYgNSA0LjYzODAzIDUuMzI2OThDNC4wNzM1NCA1LjYxNDYgMy42MTQ2IDYuMDczNTQgMy4zMjY5OCA2LjYzODAzQzMgNy4yNzk3NiAzIDguMTE5ODQgMyA5LjhWMTYuMkMzIDE3Ljg4MDIgMyAxOC43MjAyIDMuMzI2OTggMTkuMzYyQzMuNjE0NiAxOS45MjY1IDQuMDczNTQgMjAuMzg1NCA0LjYzODAzIDIwLjY3M0M1LjI3OTc2IDIxIDYuMTE5ODQgMjEgNy44IDIxSDE0LjJDMTUuODgwMiAyMSAxNi43MjAyIDIxIDE3LjM2MiAyMC42NzNDMTcuOTI2NSAyMC4zODU0IDE4LjM4NTQgMTkuOTI2NSAxOC42NzMgMTkuMzYyQzE5IDE4LjcyMDIgMTkgMTcuODgwMiAxOSAxNi4yVjE0IiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KPHBhdGggZD0iTTIxIDlMMjEgM00yMSAzSDE1TTIxIDNMMTMgMTFNMTAgNUg3LjhDNi4xMTk4NCA1IDUuMjc5NzYgNSA0LjYzODAzIDUuMzI2OThDNC4wNzM1NCA1LjYxNDYgMy42MTQ2IDYuMDczNTQgMy4zMjY5OCA2LjYzODAzQzMgNy4yNzk3NiAzIDguMTE5ODQgMyA5LjhWMTYuMkMzIDE3Ljg4MDIgMyAxOC43MjAyIDMuMzI2OTggMTkuMzYyQzMuNjE0NiAxOS45MjY1IDQuMDczNTQgMjAuMzg1NCA0LjYzODAzIDIwLjY3M0M1LjI3OTc2IDIxIDYuMTE5ODQgMjEgNy44IDIxSDE0LjJDMTUuODgwMiAyMSAxNi43MjAyIDIxIDE3LjM2MiAyMC42NzNDMTcuOTI2NSAyMC4zODU0IDE4LjM4NTQgMTkuOTI2NSAxOC42NzMgMTkuMzYyQzE5IDE4LjcyMDIgMTkgMTcuODgwMiAxOSAxNi4yVjE0IiBzdHJva2U9IiMwMDAwMDAiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.feedback:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KPHBhdGggZD0iTTguMjQ5OTkgMThMNS4yNDk5OSAyMC4yNVYxNS43NUgyLjI1QzEuODUyMTcgMTUuNzUgMS40NzA2NCAxNS41OTE5IDEuMTg5MzQgMTUuMzEwNkMwLjkwODAzNCAxNS4wMjkzIDAuNzQ5OTk5IDE0LjY0NzggMC43NDk5OTkgMTQuMjVWMi4yNUMwLjc0OTk5OSAxLjg1MjE3IDAuOTA4MDM0IDEuNDcwNjQgMS4xODkzNCAxLjE4OTM0QzEuNDcwNjQgMC45MDgwMzQgMS44NTIxNyAwLjc0OTk5OSAyLjI1IDAuNzQ5OTk5SDE4Ljc1QzE5LjE0NzggMC43NDk5OTkgMTkuNTI5MyAwLjkwODAzNCAxOS44MTA2IDEuMTg5MzRDMjAuMDkxOSAxLjQ3MDY0IDIwLjI1IDEuODUyMTcgMjAuMjUgMi4yNVY2LjcxNDg0IiBzdHJva2U9IiM3MTcxN0EiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjxwYXRoIGQ9Ik01LjI0OTk5IDUuMjQ5OTlIMTUuNzUiIHN0cm9rZT0iIzcxNzE3QSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPg0KPHBhdGggZD0iTTUuMjQ5OTkgOS43NDk5OUg4LjI0OTk5IiBzdHJva2U9IiM3MTcxN0EiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjxwYXRoIGQ9Ik0yMy4yNSAxOC43NUgyMC4yNVYyMy4yNUwxNS43NSAxOC43NUgxMS4yNVY5Ljc0OTk5SDIzLjI1VjE4Ljc1WiIgc3Ryb2tlPSIjNzE3MTdBIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8cGF0aCBkPSJNMTkuNSAxNUgxNSIgc3Ryb2tlPSIjNzE3MTdBIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KPHBhdGggZD0iTTguMjQ5OTkgMThMNS4yNDk5OSAyMC4yNVYxNS43NUgyLjI1QzEuODUyMTcgMTUuNzUgMS40NzA2NCAxNS41OTE5IDEuMTg5MzQgMTUuMzEwNkMwLjkwODAzNCAxNS4wMjkzIDAuNzQ5OTk5IDE0LjY0NzggMC43NDk5OTkgMTQuMjVWMi4yNUMwLjc0OTk5OSAxLjg1MjE3IDAuOTA4MDM0IDEuNDcwNjQgMS4xODkzNCAxLjE4OTM0QzEuNDcwNjQgMC45MDgwMzQgMS44NTIxNyAwLjc0OTk5OSAyLjI1IDAuNzQ5OTk5SDE4Ljc1QzE5LjE0NzggMC43NDk5OTkgMTkuNTI5MyAwLjkwODAzNCAxOS44MTA2IDEuMTg5MzRDMjAuMDkxOSAxLjQ3MDY0IDIwLjI1IDEuODUyMTcgMjAuMjUgMi4yNVY2LjcxNDg0IiBzdHJva2U9IiM3MTcxN0EiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjxwYXRoIGQ9Ik01LjI0OTk5IDUuMjQ5OTlIMTUuNzUiIHN0cm9rZT0iIzcxNzE3QSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPg0KPHBhdGggZD0iTTUuMjQ5OTkgOS43NDk5OUg4LjI0OTk5IiBzdHJva2U9IiM3MTcxN0EiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4NCjxwYXRoIGQ9Ik0yMy4yNSAxOC43NUgyMC4yNVYyMy4yNUwxNS43NSAxOC43NUgxMS4yNVY5Ljc0OTk5SDIzLjI1VjE4Ljc1WiIgc3Ryb2tlPSIjNzE3MTdBIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8cGF0aCBkPSJNMTkuNSAxNUgxNSIgc3Ryb2tlPSIjNzE3MTdBIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+DQo8L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.assets:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMiA0LjVDMiAzLjExOTI5IDMuMTE5MjkgMiA0LjUgMkgxOS41QzIwLjg4MDcgMiAyMiAzLjExOTI5IDIyIDQuNVYxOS41QzIyIDIwLjg4MDcgMjAuODgwNyAyMiAxOS41IDIySDQuNUMzLjExOTI5IDIyIDIgMjAuODgwNyAyIDE5LjVWNC41Wk0xMi41IDUuNUMxMy4wNTIzIDUuNSAxMy41IDUuOTQ3NzIgMTMuNSA2LjVWMTAuNUgxNy41QzE4LjA1MjMgMTAuNSAxOC41IDEwLjk0NzcgMTguNSAxMS41VjEyLjVDMTguNSAxMy4wNTIzIDE4LjA1MjMgMTMuNSAxNy41IDEzLjVIMTMuNVYxNy41QzEzLjUgMTguMDUyMyAxMy4wNTIzIDE4LjUgMTIuNSAxOC41SDExLjVDMTAuOTQ3NyAxOC41IDEwLjUgMTguMDUyMyAxMC41IDE3LjVWMTMuNUg2LjVDNS45NDc3MiAxMy41IDUuNSAxMy4wNTIzIDUuNSAxMi41VjExLjVDNS41IDEwLjk0NzcgNS45NDc3MiAxMC41IDYuNSAxMC41SDEwLjVWNi41QzEwLjUgNS45NDc3MiAxMC45NDc3IDUuNSAxMS41IDUuNUgxMi41WiIgZmlsbD0iIzAwMDAwMCIvPg0KPC9zdmc+');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMiA0LjVDMiAzLjExOTI5IDMuMTE5MjkgMiA0LjUgMkgxOS41QzIwLjg4MDcgMiAyMiAzLjExOTI5IDIyIDQuNVYxOS41QzIyIDIwLjg4MDcgMjAuODgwNyAyMiAxOS41IDIySDQuNUMzLjExOTI5IDIyIDIgMjAuODgwNyAyIDE5LjVWNC41Wk0xMi41IDUuNUMxMy4wNTIzIDUuNSAxMy41IDUuOTQ3NzIgMTMuNSA2LjVWMTAuNUgxNy41QzE4LjA1MjMgMTAuNSAxOC41IDEwLjk0NzcgMTguNSAxMS41VjEyLjVDMTguNSAxMy4wNTIzIDE4LjA1MjMgMTMuNSAxNy41IDEzLjVIMTMuNVYxNy41QzEzLjUgMTguMDUyMyAxMy4wNTIzIDE4LjUgMTIuNSAxOC41SDExLjVDMTAuOTQ3NyAxOC41IDEwLjUgMTguMDUyMyAxMC41IDE3LjVWMTMuNUg2LjVDNS45NDc3MiAxMy41IDUuNSAxMy4wNTIzIDUuNSAxMi41VjExLjVDNS41IDEwLjk0NzcgNS45NDc3MiAxMC41IDYuNSAxMC41SDEwLjVWNi41QzEwLjUgNS45NDc3MiAxMC45NDc3IDUuNSAxMS41IDUuNUgxMi41WiIgZmlsbD0iIzAwMDAwMCIvPg0KPC9zdmc+');-webkit-mask-size: contain;mask-size: contain;}i.liabilities:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMiA0LjVDMiAzLjExOTI5IDMuMTE5MjkgMiA0LjUgMkgxOS41QzIwLjg4MDcgMiAyMiAzLjExOTI5IDIyIDQuNVYxOS41QzIyIDIwLjg4MDcgMjAuODgwNyAyMiAxOS41IDIySDQuNUMzLjExOTI5IDIyIDIgMjAuODgwNyAyIDE5LjVWNC41Wk02LjUgMTAuNUM1Ljk0NzcyIDEwLjUgNS41IDEwLjk0NzcgNS41IDExLjVWMTIuNUM1LjUgMTMuMDUyMyA1Ljk0NzcyIDEzLjUgNi41IDEzLjVIMTcuNUMxOC4wNTIzIDEzLjUgMTguNSAxMy4wNTIzIDE4LjUgMTIuNVYxMS41QzE4LjUgMTAuOTQ3NyAxOC4wNTIzIDEwLjUgMTcuNSAxMC41SDYuNVoiIGZpbGw9IiMwMDAwMDAiLz4NCjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPg0KPHN2ZyB3aWR0aD0iODAwcHgiIGhlaWdodD0iODAwcHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMiA0LjVDMiAzLjExOTI5IDMuMTE5MjkgMiA0LjUgMkgxOS41QzIwLjg4MDcgMiAyMiAzLjExOTI5IDIyIDQuNVYxOS41QzIyIDIwLjg4MDcgMjAuODgwNyAyMiAxOS41IDIySDQuNUMzLjExOTI5IDIyIDIgMjAuODgwNyAyIDE5LjVWNC41Wk02LjUgMTAuNUM1Ljk0NzcyIDEwLjUgNS41IDEwLjk0NzcgNS41IDExLjVWMTIuNUM1LjUgMTMuMDUyMyA1Ljk0NzcyIDEzLjUgNi41IDEzLjVIMTcuNUMxOC4wNTIzIDEzLjUgMTguNSAxMy4wNTIzIDE4LjUgMTIuNVYxMS41QzE4LjUgMTAuOTQ3NyAxOC4wNTIzIDEwLjUgMTcuNSAxMC41SDYuNVoiIGZpbGw9IiMwMDAwMDAiLz4NCjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.cash:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIGZpbGw9IiMwMDAwMDAiIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAtNjQgNjQwIDY0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNjA4IDY0SDMyQzE0LjMzIDY0IDAgNzguMzMgMCA5NnYzMjBjMCAxNy42NyAxNC4zMyAzMiAzMiAzMmg1NzZjMTcuNjcgMCAzMi0xNC4zMyAzMi0zMlY5NmMwLTE3LjY3LTE0LjMzLTMyLTMyLTMyek00OCA0MDB2LTY0YzM1LjM1IDAgNjQgMjguNjUgNjQgNjRINDh6bTAtMjI0di02NGg2NGMwIDM1LjM1LTI4LjY1IDY0LTY0IDY0em0yNzIgMTc2Yy00NC4xOSAwLTgwLTQyLjk5LTgwLTk2IDAtNTMuMDIgMzUuODItOTYgODAtOTZzODAgNDIuOTggODAgOTZjMCA1My4wMy0zNS44MyA5Ni04MCA5NnptMjcyIDQ4aC02NGMwLTM1LjM1IDI4LjY1LTY0IDY0LTY0djY0em0wLTIyNGMtMzUuMzUgMC02NC0yOC42NS02NC02NGg2NHY2NHoiLz48L3N2Zz4');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIGZpbGw9IiMwMDAwMDAiIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAtNjQgNjQwIDY0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNjA4IDY0SDMyQzE0LjMzIDY0IDAgNzguMzMgMCA5NnYzMjBjMCAxNy42NyAxNC4zMyAzMiAzMiAzMmg1NzZjMTcuNjcgMCAzMi0xNC4zMyAzMi0zMlY5NmMwLTE3LjY3LTE0LjMzLTMyLTMyLTMyek00OCA0MDB2LTY0YzM1LjM1IDAgNjQgMjguNjUgNjQgNjRINDh6bTAtMjI0di02NGg2NGMwIDM1LjM1LTI4LjY1IDY0LTY0IDY0em0yNzIgMTc2Yy00NC4xOSAwLTgwLTQyLjk5LTgwLTk2IDAtNTMuMDIgMzUuODItOTYgODAtOTZzODAgNDIuOTggODAgOTZjMCA1My4wMy0zNS44MyA5Ni04MCA5NnptMjcyIDQ4aC02NGMwLTM1LjM1IDI4LjY1LTY0IDY0LTY0djY0em0wLTIyNGMtMzUuMzUgMC02NC0yOC42NS02NC02NGg2NHY2NHoiLz48L3N2Zz4');-webkit-mask-size: contain;mask-size: contain;}i.change:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDIwMDEwOTA0Ly9FTiIKICJodHRwOi8vd3d3LnczLm9yZy9UUi8yMDAxL1JFQy1TVkctMjAwMTA5MDQvRFREL3N2ZzEwLmR0ZCI+CjxzdmcgdmVyc2lvbj0iMS4wIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCA0NC4wMDAwMDAgNDQuMDAwMDAwIgogcHJlc2VydmVBc3BlY3RSYXRpbz0ieE1pZFlNaWQgbWVldCI+CjxtZXRhZGF0YT4KQ3JlYXRlZCBieSBwb3RyYWNlIDEuMTYsIHdyaXR0ZW4gYnkgUGV0ZXIgU2VsaW5nZXIgMjAwMS0yMDE5CjwvbWV0YWRhdGE+CjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMDAwMDAwLDQ0LjAwMDAwMCkgc2NhbGUoMC4xMDAwMDAsLTAuMTAwMDAwKSIKZmlsbD0iIzFhNzNlOCIgc3Ryb2tlPSJub25lIj4KPHBhdGggZD0iTTU3IDM4MiBjLTI2IC0yNiAtNDcgLTUyIC00NyAtNTggMCAtMjMgODQgLTEwNCAxMDcgLTEwNCAzMCAwIDMxIDMwCjIgNTYgLTIxIDE5IC0xOSAxOSAxNDIgMjQgMTU2IDUgMTY0IDYgMTY0IDI1IDAgMTkgLTggMjAgLTE2NCAyMyBsLTE2NCAzIDIzCjI0IGMyMyAyNSAyMiA1NSAtMyA1NSAtNyAwIC0zNCAtMjIgLTYwIC00OHoiLz4KPHBhdGggZD0iTTMwMCAyMDIgYzAgLTExIDkgLTI3IDIxIC0zOCAyMSAtMTkgMTkgLTE5IC0xNDIgLTI0IC0xNTYgLTUgLTE2NAotNiAtMTY0IC0yNSAwIC0xOSA4IC0yMCAxNjQgLTI1IDE2MSAtNSAxNjMgLTUgMTQyIC0yNCAtMjQgLTIyIC0yOCAtNTEgLTcKLTU5IDE0IC02IDExNiA5MCAxMTYgMTA5IDAgNiAtMTkgMzIgLTQyIDU3IC00NCA1MCAtODggNjQgLTg4IDI5eiIvPgo8L2c+Cjwvc3ZnPgo=');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDIwMDEwOTA0Ly9FTiIKICJodHRwOi8vd3d3LnczLm9yZy9UUi8yMDAxL1JFQy1TVkctMjAwMTA5MDQvRFREL3N2ZzEwLmR0ZCI+CjxzdmcgdmVyc2lvbj0iMS4wIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCA0NC4wMDAwMDAgNDQuMDAwMDAwIgogcHJlc2VydmVBc3BlY3RSYXRpbz0ieE1pZFlNaWQgbWVldCI+CjxtZXRhZGF0YT4KQ3JlYXRlZCBieSBwb3RyYWNlIDEuMTYsIHdyaXR0ZW4gYnkgUGV0ZXIgU2VsaW5nZXIgMjAwMS0yMDE5CjwvbWV0YWRhdGE+CjxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAuMDAwMDAwLDQ0LjAwMDAwMCkgc2NhbGUoMC4xMDAwMDAsLTAuMTAwMDAwKSIKZmlsbD0iIzFhNzNlOCIgc3Ryb2tlPSJub25lIj4KPHBhdGggZD0iTTU3IDM4MiBjLTI2IC0yNiAtNDcgLTUyIC00NyAtNTggMCAtMjMgODQgLTEwNCAxMDcgLTEwNCAzMCAwIDMxIDMwCjIgNTYgLTIxIDE5IC0xOSAxOSAxNDIgMjQgMTU2IDUgMTY0IDYgMTY0IDI1IDAgMTkgLTggMjAgLTE2NCAyMyBsLTE2NCAzIDIzCjI0IGMyMyAyNSAyMiA1NSAtMyA1NSAtNyAwIC0zNCAtMjIgLTYwIC00OHoiLz4KPHBhdGggZD0iTTMwMCAyMDIgYzAgLTExIDkgLTI3IDIxIC0zOCAyMSAtMTkgMTkgLTE5IC0xNDIgLTI0IC0xNTYgLTUgLTE2NAotNiAtMTY0IC0yNSAwIC0xOSA4IC0yMCAxNjQgLTI1IDE2MSAtNSAxNjMgLTUgMTQyIC0yNCAtMjQgLTIyIC0yOCAtNTEgLTcKLTU5IDE0IC02IDExNiA5MCAxMTYgMTA5IDAgNiAtMTkgMzIgLTQyIDU3IC00NCA1MCAtODggNjQgLTg4IDI5eiIvPgo8L2c+Cjwvc3ZnPgo=');-webkit-mask-size: contain;mask-size: contain;}i.trash:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pg0KPCEtLSBVcGxvYWRlZCB0bzogU1ZHIFJlcG8sIHd3dy5zdmdyZXBvLmNvbSwgR2VuZXJhdG9yOiBTVkcgUmVwbyBNaXhlciBUb29scyAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIGZpbGw9IiMwMDAwMDAiIHZlcnNpb249IjEuMSIgaWQ9IkNhcGFfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgDQoJIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDQxLjMzNiA0MS4zMzYiDQoJIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPGc+DQoJPHBhdGggZD0iTTM2LjMzNSw1LjY2OGgtOC4xNjdWMS41YzAtMC44MjgtMC42NzItMS41LTEuNS0xLjVoLTEyYy0wLjgyOCwwLTEuNSwwLjY3Mi0xLjUsMS41djQuMTY4SDUuMDAxYy0xLjEwNCwwLTIsMC44OTYtMiwyDQoJCXMwLjg5NiwyLDIsMmgyLjAwMXYyOS4xNjhjMCwxLjM4MSwxLjExOSwyLjUsMi41LDIuNWgyMi4zMzJjMS4zODEsMCwyLjUtMS4xMTksMi41LTIuNVY5LjY2OGgyLjAwMWMxLjEwNCwwLDItMC44OTYsMi0yDQoJCVMzNy40MzgsNS42NjgsMzYuMzM1LDUuNjY4eiBNMTQuMTY4LDM1LjY3YzAsMC44MjgtMC42NzIsMS41LTEuNSwxLjVzLTEuNS0wLjY3Mi0xLjUtMS41di0yMWMwLTAuODI4LDAuNjcyLTEuNSwxLjUtMS41DQoJCXMxLjUsMC42NzIsMS41LDEuNVYzNS42N3ogTTIyLjE2OCwzNS42N2MwLDAuODI4LTAuNjcyLDEuNS0xLjUsMS41cy0xLjUtMC42NzItMS41LTEuNXYtMjFjMC0wLjgyOCwwLjY3Mi0xLjUsMS41LTEuNQ0KCQlzMS41LDAuNjcyLDEuNSwxLjVWMzUuNjd6IE0yNS4xNjgsNS42NjhoLTlWM2g5VjUuNjY4eiBNMzAuMTY4LDM1LjY3YzAsMC44MjgtMC42NzIsMS41LTEuNSwxLjVzLTEuNS0wLjY3Mi0xLjUtMS41di0yMQ0KCQljMC0wLjgyOCwwLjY3Mi0xLjUsMS41LTEuNXMxLjUsMC42NzIsMS41LDEuNVYzNS42N3oiLz4NCjwvZz4NCjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pg0KPCEtLSBVcGxvYWRlZCB0bzogU1ZHIFJlcG8sIHd3dy5zdmdyZXBvLmNvbSwgR2VuZXJhdG9yOiBTVkcgUmVwbyBNaXhlciBUb29scyAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIGZpbGw9IiMwMDAwMDAiIHZlcnNpb249IjEuMSIgaWQ9IkNhcGFfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgDQoJIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDQxLjMzNiA0MS4zMzYiDQoJIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPGc+DQoJPHBhdGggZD0iTTM2LjMzNSw1LjY2OGgtOC4xNjdWMS41YzAtMC44MjgtMC42NzItMS41LTEuNS0xLjVoLTEyYy0wLjgyOCwwLTEuNSwwLjY3Mi0xLjUsMS41djQuMTY4SDUuMDAxYy0xLjEwNCwwLTIsMC44OTYtMiwyDQoJCXMwLjg5NiwyLDIsMmgyLjAwMXYyOS4xNjhjMCwxLjM4MSwxLjExOSwyLjUsMi41LDIuNWgyMi4zMzJjMS4zODEsMCwyLjUtMS4xMTksMi41LTIuNVY5LjY2OGgyLjAwMWMxLjEwNCwwLDItMC44OTYsMi0yDQoJCVMzNy40MzgsNS42NjgsMzYuMzM1LDUuNjY4eiBNMTQuMTY4LDM1LjY3YzAsMC44MjgtMC42NzIsMS41LTEuNSwxLjVzLTEuNS0wLjY3Mi0xLjUtMS41di0yMWMwLTAuODI4LDAuNjcyLTEuNSwxLjUtMS41DQoJCXMxLjUsMC42NzIsMS41LDEuNVYzNS42N3ogTTIyLjE2OCwzNS42N2MwLDAuODI4LTAuNjcyLDEuNS0xLjUsMS41cy0xLjUtMC42NzItMS41LTEuNXYtMjFjMC0wLjgyOCwwLjY3Mi0xLjUsMS41LTEuNQ0KCQlzMS41LDAuNjcyLDEuNSwxLjVWMzUuNjd6IE0yNS4xNjgsNS42NjhoLTlWM2g5VjUuNjY4eiBNMzAuMTY4LDM1LjY3YzAsMC44MjgtMC42NzIsMS41LTEuNSwxLjVzLTEuNS0wLjY3Mi0xLjUtMS41di0yMQ0KCQljMC0wLjgyOCwwLjY3Mi0xLjUsMS41LTEuNXMxLjUsMC42NzIsMS41LDEuNVYzNS42N3oiLz4NCjwvZz4NCjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.profile:before {-webkit-mask: url('data:image/svg+xml;base64,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');mask: url('data:image/svg+xml;base64,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');-webkit-mask-size: contain;mask-size: contain;}i.key:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIGZpbGw9IiMwMDAwMDAiIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDMyIDMyIiBpZD0iaWNvbiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxzdHlsZT4KICAgICAgLmNscy0xIHsKICAgICAgICBmaWxsOiBub25lOwogICAgICB9CiAgICA8L3N0eWxlPgogIDwvZGVmcz4KICA8cGF0aCBkPSJNMjEsMmE4Ljk5NzcsOC45OTc3LDAsMCwwLTguNjExOSwxMS42MTE4TDIsMjR2Nkg4TDE4LjM4ODEsMTkuNjExOEE5LDksMCwxLDAsMjEsMlptMCwxNmE3LjAxMjUsNy4wMTI1LDAsMCwxLTIuMDMyMi0uMzAyMkwxNy44MjEsMTcuMzVsLS44NDcyLjg0NzItMy4xODExLDMuMTgxMkwxMi40MTQxLDIwLDExLDIxLjQxNDFsMS4zNzg3LDEuMzc4Ni0xLjU4NTksMS41ODZMOS40MTQxLDIzLDgsMjQuNDE0MWwxLjM3ODcsMS4zNzg2TDcuMTcxNiwyOEg0VjI0LjgyODRsOS44MDIzLTkuODAyMy44NDcyLS44NDc0LS4zNDczLTEuMTQ2N0E3LDcsMCwxLDEsMjEsMThaIi8+CiAgPGNpcmNsZSBjeD0iMjIiIGN5PSIxMCIgcj0iMiIvPgogIDxyZWN0IGlkPSJfVHJhbnNwYXJlbnRfUmVjdGFuZ2xlXyIgZGF0YS1uYW1lPSImbHQ7VHJhbnNwYXJlbnQgUmVjdGFuZ2xlJmd0OyIgY2xhc3M9ImNscy0xIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiLz4KPC9zdmc+');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IS0tIFVwbG9hZGVkIHRvOiBTVkcgUmVwbywgd3d3LnN2Z3JlcG8uY29tLCBHZW5lcmF0b3I6IFNWRyBSZXBvIE1peGVyIFRvb2xzIC0tPgo8c3ZnIGZpbGw9IiMwMDAwMDAiIHdpZHRoPSI4MDBweCIgaGVpZ2h0PSI4MDBweCIgdmlld0JveD0iMCAwIDMyIDMyIiBpZD0iaWNvbiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxzdHlsZT4KICAgICAgLmNscy0xIHsKICAgICAgICBmaWxsOiBub25lOwogICAgICB9CiAgICA8L3N0eWxlPgogIDwvZGVmcz4KICA8cGF0aCBkPSJNMjEsMmE4Ljk5NzcsOC45OTc3LDAsMCwwLTguNjExOSwxMS42MTE4TDIsMjR2Nkg4TDE4LjM4ODEsMTkuNjExOEE5LDksMCwxLDAsMjEsMlptMCwxNmE3LjAxMjUsNy4wMTI1LDAsMCwxLTIuMDMyMi0uMzAyMkwxNy44MjEsMTcuMzVsLS44NDcyLjg0NzItMy4xODExLDMuMTgxMkwxMi40MTQxLDIwLDExLDIxLjQxNDFsMS4zNzg3LDEuMzc4Ni0xLjU4NTksMS41ODZMOS40MTQxLDIzLDgsMjQuNDE0MWwxLjM3ODcsMS4zNzg2TDcuMTcxNiwyOEg0VjI0LjgyODRsOS44MDIzLTkuODAyMy44NDcyLS44NDc0LS4zNDczLTEuMTQ2N0E3LDcsMCwxLDEsMjEsMThaIi8+CiAgPGNpcmNsZSBjeD0iMjIiIGN5PSIxMCIgcj0iMiIvPgogIDxyZWN0IGlkPSJfVHJhbnNwYXJlbnRfUmVjdGFuZ2xlXyIgZGF0YS1uYW1lPSImbHQ7VHJhbnNwYXJlbnQgUmVjdGFuZ2xlJmd0OyIgY2xhc3M9ImNscy0xIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiLz4KPC9zdmc+');-webkit-mask-size: contain;mask-size: contain;}i.rightarrow:before {-webkit-mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pg0KPCEtLSBVcGxvYWRlZCB0bzogU1ZHIFJlcG8sIHd3dy5zdmdyZXBvLmNvbSwgR2VuZXJhdG9yOiBTVkcgUmVwbyBNaXhlciBUb29scyAtLT4NCjxzdmcgZmlsbD0iIzAwMDAwMCIgaGVpZ2h0PSI4MDBweCIgd2lkdGg9IjgwMHB4IiB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiANCgkgdmlld0JveD0iMCAwIDUxMi4wMDUgNTEyLjAwNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+DQo8Zz4NCgk8Zz4NCgkJPHBhdGggZD0iTTM4OC40MTgsMjQwLjkyM0wxNTMuNzUxLDYuMjU2Yy04LjM0MS04LjM0MS0yMS44MjQtOC4zNDEtMzAuMTY1LDBzLTguMzQxLDIxLjgyNCwwLDMwLjE2NUwzNDMuMTcsMjU2LjAwNQ0KCQkJTDEyMy41ODYsNDc1LjU4OWMtOC4zNDEsOC4zNDEtOC4zNDEsMjEuODI0LDAsMzAuMTY1YzQuMTYsNC4xNiw5LjYyMSw2LjI1MSwxNS4wODMsNi4yNTFjNS40NjEsMCwxMC45MjMtMi4wOTEsMTUuMDgzLTYuMjUxDQoJCQlsMjM0LjY2Ny0yMzQuNjY3QzM5Ni43NTksMjYyLjc0NywzOTYuNzU5LDI0OS4yNjQsMzg4LjQxOCwyNDAuOTIzeiIvPg0KCTwvZz4NCjwvZz4NCjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pg0KPCEtLSBVcGxvYWRlZCB0bzogU1ZHIFJlcG8sIHd3dy5zdmdyZXBvLmNvbSwgR2VuZXJhdG9yOiBTVkcgUmVwbyBNaXhlciBUb29scyAtLT4NCjxzdmcgZmlsbD0iIzAwMDAwMCIgaGVpZ2h0PSI4MDBweCIgd2lkdGg9IjgwMHB4IiB2ZXJzaW9uPSIxLjEiIGlkPSJMYXllcl8xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiANCgkgdmlld0JveD0iMCAwIDUxMi4wMDUgNTEyLjAwNSIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+DQo8Zz4NCgk8Zz4NCgkJPHBhdGggZD0iTTM4OC40MTgsMjQwLjkyM0wxNTMuNzUxLDYuMjU2Yy04LjM0MS04LjM0MS0yMS44MjQtOC4zNDEtMzAuMTY1LDBzLTguMzQxLDIxLjgyNCwwLDMwLjE2NUwzNDMuMTcsMjU2LjAwNQ0KCQkJTDEyMy41ODYsNDc1LjU4OWMtOC4zNDEsOC4zNDEtOC4zNDEsMjEuODI0LDAsMzAuMTY1YzQuMTYsNC4xNiw5LjYyMSw2LjI1MSwxNS4wODMsNi4yNTFjNS40NjEsMCwxMC45MjMtMi4wOTEsMTUuMDgzLTYuMjUxDQoJCQlsMjM0LjY2Ny0yMzQuNjY3QzM5Ni43NTksMjYyLjc0NywzOTYuNzU5LDI0OS4yNjQsMzg4LjQxOCwyNDAuOTIzeiIvPg0KCTwvZz4NCjwvZz4NCjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}i.save:before{-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiAgd2lkdGg9IjE1IiAgaGVpZ2h0PSIxNSIgIHZpZXdCb3g9IjAgMCAyNCAyNCIgIGZpbGw9Im5vbmUiICBzdHJva2U9ImN1cnJlbnRDb2xvciIgIHN0cm9rZS13aWR0aD0iMSIgIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgIHN0cm9rZS1saW5lam9pbj0icm91bmQiICBjbGFzcz0iaWNvbiBpY29uLXRhYmxlciBpY29ucy10YWJsZXItb3V0bGluZSBpY29uLXRhYmxlci1ib29rbWFyayI+PHBhdGggc3Ryb2tlPSJub25lIiBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTE4IDd2MTRsLTYgLTRsLTYgNHYtMTRhNCA0IDAgMCAxIDQgLTRoNGE0IDQgMCAwIDEgNCA0eiIgLz48L3N2Zz4=');mask: url('data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiAgd2lkdGg9IjE1IiAgaGVpZ2h0PSIxNSIgIHZpZXdCb3g9IjAgMCAyNCAyNCIgIGZpbGw9Im5vbmUiICBzdHJva2U9ImN1cnJlbnRDb2xvciIgIHN0cm9rZS13aWR0aD0iMSIgIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgIHN0cm9rZS1saW5lam9pbj0icm91bmQiICBjbGFzcz0iaWNvbiBpY29uLXRhYmxlciBpY29ucy10YWJsZXItb3V0bGluZSBpY29uLXRhYmxlci1ib29rbWFyayI+PHBhdGggc3Ryb2tlPSJub25lIiBkPSJNMCAwaDI0djI0SDB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTE4IDd2MTRsLTYgLTRsLTYgNHYtMTRhNCA0IDAgMCAxIDQgLTRoNGE0IDQgMCAwIDEgNCA0eiIgLz48L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.rightarrow:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggdHJhbnNmb3JtPSJyb3RhdGUoMTgwLDEyLDEyKSIgZD0iTTE2LjY3IDBsMi44MyAyLjgyOS05LjMzOSA5LjE3NSA5LjMzOSA5LjE2Ny0yLjgzIDIuODI5LTEyLjE3LTExLjk5NnoiLz48L3N2Zz4=');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggdHJhbnNmb3JtPSJyb3RhdGUoMTgwLDEyLDEyKSIgZD0iTTE2LjY3IDBsMi44MyAyLjgyOS05LjMzOSA5LjE3NSA5LjMzOSA5LjE2Ny0yLjgzIDIuODI5LTEyLjE3LTExLjk5NnoiLz48L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.handshake:before {-webkit-mask: url('data:image/svg+xml;base64,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');mask: url('data:image/svg+xml;base64,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');-webkit-mask-size: contain;mask-size: contain;}#errors {display: none;}.mb-2{margin-bottom: 2rem;}.mb-25{margin-bottom: 2.5rem;}.input_wrapper, .select_wrapper, .textarea_wrapper, .span_wrapper {position: relative;margin-bottom: 1.5rem;}.span_wrapper {margin-bottom: 0;}.range_wrapper, .checkboxes {margin-bottom: 1rem;}:is(.input_wrapper, .select_wrapper, .textarea_wrapper, .range_wrapper, .span_wrapper) label {display: block;font-family: sans-serif;color: var(--dark);font-weight: 600;font-size: .875rem;}:is(.input_wrapper, .select_wrapper, .textarea_wrapper, .span_wrapper) label:not(input[type="checkbox"] + label) {padding: 0 .25rem;inset: calc(-0.875rem + 0.125rem) auto auto .75rem;position: absolute;display: inline-block;line-height: 1;background-color: #FFF;z-index: 1;}.range_wrapper label {padding-left: 1.5rem;margin-bottom: .5rem;}:is(.input_wrapper, .select_wrapper, .textarea_wrapper, .range_wrapper) label span {color: var(--red);font-size: .75em;}.range_wrapper label input {background-color: transparent;border: none;border-radius: .25rem;user-select: none;pointer-events: none;cursor: default;font-size: .75rem;padding: .5rem;display: block;margin-top: .5rem;color: var(--dark_gray);line-height: 1;}.input_wrapper > input, .login_password_block input {border:.0625rem solid var(--blue);border-radius: .5rem;width: 100%;padding: .75rem;}.input_wrapper + .range_wrapper, .select_wrapper + .range_wrapper{margin-top: -1rem;}.select_wrapper select {border:.0625rem solid var(--blue);border-radius: .5rem;width: 100%;padding: .75rem;-webkit-appearance: none;-moz-appearance: none;appearance: none;background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMCA3LjMzbDIuODI5LTIuODMgOS4xNzUgOS4zMzkgOS4xNjctOS4zMzkgMi44MjkgMi44My0xMS45OTYgMTIuMTd6Ii8+PC9zdmc+) calc(100% - 1rem) 50% no-repeat #fff;background-size: .5rem;padding-left: .85rem;transition: all .1s ease;text-transform: none;word-wrap: normal;color: var(--dark);}.textarea_wrapper textarea {border:.0625rem solid var(--blue);border-radius: .5rem;width: 100%;padding: .75rem;}.checkboxes {display: grid;grid-template-columns: 1fr 1fr;grid-auto-columns: minmax(0, 1fr);}.checkbox_button {display: flex;}.checkbox_button label {display: flex;flex-direction: row;align-items: center;color: var(--dark);}.checkbox_button input {margin-right: .5rem;}.checkbox_button input[type="checkbox"] {-webkit-appearance: none;appearance: none;width: 1.5rem;height: 1.5rem;min-width: 1.5rem;border-radius: .25rem;background-color: #FFF;border: 0.0625rem solid var(--link_blue);cursor: pointer;position: relative;}.checkbox_button input[type="checkbox"]:checked::after {content: "";position: absolute;top: 0.2rem;left: 0.45rem;width: 0.3rem;height: 0.6rem;border: solid var(--link_blue);border-width: 0 0.125rem 0.125rem 0;transform: rotate(45deg);}.checkbox_button label span{font-size: 0.875rem;}.switch {position: relative;display: inline-block;width: 5rem;height: 2.75rem;cursor: pointer;}.switch input {opacity: 0;width: 0;height: 0;}.switch span {position: absolute;cursor: pointer;inset: 0;background-color: var(--dark_gray);transition: .2s;border-radius: 2rem;}.switch input:not(.noevents):checked + span, .switch.search_compare > span {background-color: var(--link_blue);}.switch span::before {position: absolute;content: "";height: 2.25rem;width: 2.25rem;left: .25rem;bottom: .25rem;background-color: white;-webkit-transition: .4s;transition: .4s;border-radius: 50%;}.switch input:not(.noevents):checked + span::before, .switch.search_compare > span::before {-webkit-transform: translateX(2.25rem);-ms-transform: translateX(2.25rem);transform: translateX(2.25rem);}@media (min-width: 1025px) {.input_wrapper, .select_wrapper, .textarea_wrapper {margin-bottom: 2rem;}.input_wrapper input:hover, .select_wrapper select:hover, .textarea_wrapper textarea:hover, .span_wrapper span:hover, .checkbox_button input[type="checkbox"]:hover {background-color: var(--light);box-shadow: inset 0 0 0 0.0625rem var(--link_blue);cursor: pointer;}.range_wrapper, .checkboxes {margin-bottom: 1.5rem;}.range_wrapper label {display: flex;justify-content: space-between;align-items: center;line-height: 1;margin-bottom: .5rem;}.range_wrapper label input {margin-top: 0;}.white_shell :is(input, select, textarea) {max-width: calc(100% - 3rem);}}.calc_intro:nth-last-child(0) {padding-bottom: 0;}#calculator_form .white_shell{position: relative;}#calculator_form .white_shell input{max-width: 100% !important;}#calculator_form .white_shell .tcol8 .input_wrapper:first-of-type{margin-top: 1.5rem;}#calculator_form .calc_heading{font-size: 1.1rem;font-weight: bold;color: var(--dark);margin-bottom: 2.5rem;border-bottom:.0625rem solid var(--light_gray);padding-bottom: 0.75rem;}#calculator_form .calc_totals{display: flex;justify-content: space-between;align-items: center;}#calculator_form .calc_totals p{color: var(--dark);}#calculator_form .calc_totals .net_total{color: var(--link_blue);font-weight: bold;}.calc_total_container .input_wrapper input{background-color: var(--light);border-color: var(--dark_gray);pointer-events: none;}:is(#subscribe_form,#feedback_form) i:before {content: '';display: inline-flex;height: 4rem;width: 4rem;background-color: var(--link_blue);mask-repeat: no-repeat;margin-bottom: 1.5rem;}#calculator_form i{line-height: 1;}#calculator_form i:before {content: '';display: inline-flex;height: 1.25rem;width: 1.25rem;background-color: var(--dark);mask-repeat: no-repeat;}.success_bg{margin-bottom: 1rem;}.success_bg:empty {margin: 0;}.login_screens:not(.login_modal .login_screens){padding: 2.5rem 1rem;max-width: 32rem;margin: auto;}.login_screens .tcol8.login_content_block{display: flex;}.login_password_block{position: relative;}.login_password_block i{position: absolute;right: .75rem;top: calc(50% - .75rem);display: flex;}.login_screens i:before {content: '';display: inline-flex;height: 1.5rem;width: 1.5rem;background-color: var(--link_blue);mask-repeat: no-repeat;}.login_content{background: var(--light_blue);width: 100%;border:.0625rem;border-radius: 1.25rem;align-content: center;padding: 2rem;}.login_btns{display: flex;gap: 1rem;}#signup_step2 .login_btns{justify-content: right;}.login_forms .btn{width: 50%;}.login_forms .btn.google_btn{width: 100%;margin-left: 12px;}.login_forms .login_password_block i{cursor: pointer;}.nsm7Bb-HzV7m-LgbsSe{width: 100% !important;max-width: 100% !important;border-radius: 3em !important;}.login_forms .button.light{background-color: transparent;border:.0625rem solid var(--link_blue);color: var(--link_blue);}.login_forms .login_link{font-size: 85%;}.login_forms .login_switch_links{color: var(--dark);margin-top: 2rem;}.login_forms .signup_btn{position: relative;}.login_forms .signup_btn span{position: absolute;top: -1rem;left: .5rem;font-size: 0.8rem;}.login_content_signup > p{display: flex;align-items: center;gap: .5rem;}.login_content_signup div p{margin-top: 2rem;font-size: 1.2rem;color: var(--dark);}.login_forms .accept_box{display: flex;align-items: center;gap: .5rem;}.login_forms .accept_box input{width: 1rem;height: 1rem;}.login_forms .accept_box label{font-size: 0.8rem;margin-bottom: 0;}#appleid-signin > div{width: 100% !important;}#appleid-signin svg{transform: none !important;}@media only screen and (max-width: 768px) {.social-login.login_btns{display: block;}.social-login.login_btns .btn{width: 100%;margin-left: 0px;}.login_forms .btn{padding: .75em 0;}.login_content{margin-bottom: 2rem;}.login_screens{padding-top: 0;}}.signup_steps{display: flex;align-items: center;margin-bottom: 1.5rem;}.stepper{width: 2.5rem;height: 2.5rem;border:.0625rem solid var(--dark_gray);border-radius: 50%;padding: .5rem;font-size: 0.75rem;display: flex;justify-content: center;align-items: center;}.stepper_line{width: 6rem;height: .25rem;background-color: var(--light_gray);}.stepper.active, .stepper_line.active{background-color: var(--link_blue);border: none;color: #fff;}.login_screens .success_bg i:before {background-color: var(--border_green);}:is(#signup_modal, .login_page) .login_display{position: relative;}#signup_modal .login_display .logo a{display: inline-block;}.login_display h3{margin: 1rem 0 3rem;text-align: center;}.login_display h3.submission{margin-bottom: 0;}.login_display h3 + p {margin-top: 1rem;}:is(#signup_modal, .login_page) .login_display .signup_steps_block{position: absolute;top: 0;right: 0;z-index: 1;}.login_page .login_display{border:.0625rem solid var(--blue);border-radius: 1.25rem;padding: 1.5rem;}.login_skeleton{width: 100%;height: calc(100vh - 2rem);background: var(--light_gray);border-radius: 1.25rem;animation: hintloading 2s ease-in-out 0s infinite reverse;-webkit-animation: hintloading 2s ease-in-out 0s infinite reverse;}.login_modal .input_wrapper .login_skeleton{border-radius: 2rem;width: 100%;padding: .75rem;height: 2.75rem;}.login_modal .skeleton_form{align-content: center;padding: 1rem;border:.0625rem solid var(--light_gray);border-radius: 1.25rem;}.login_modal .skeleton_form .input_wrapper{margin-bottom: 3rem;}:is(.login_btns,.login_or_div){margin-bottom: 1.5rem;}.login_or{display: flex;}.login_or::before{color: var(--light_gray);content: '';flex: 1;border-bottom: solid.0625rem;margin: auto 0.5rem auto 0;}.login_or::after{color: var(--light_gray);content: '';flex: 1;border-bottom: solid.0625rem;margin: auto 0 auto 0.5rem;}.google_btn, .apple_btn{cursor: pointer;}.apple_btn rect{ry: 20px !important;}@keyframes hintloading{0% {opacity: 0.5;}50%{opacity: 1;}100% {opacity: 0.5;}}@-webkit-keyframes hintloading{0% {opacity: 0.5;}50%{opacity: 1;}100% {opacity: 0.5;}}@media only screen and (max-width: 768px) {:is(#signup_modal, .login_page) .login_display .signup_steps_block{position: unset;}}</style>        <link rel="stylesheet" href="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/tablet.css" media="screen and (min-width: 481px)">
        <link rel="stylesheet" href="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/laptop.css" media="screen and (min-width: 769px)">
        <link rel="stylesheet" href="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/desktop.css" media="screen and (min-width: 1201px)">
        <link rel="stylesheet" href="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/large.css" media="screen and (min-width: 1441px)">
                    <style>.listings {}.listings article {height: 100%;display: flex;flex-direction: column;padding: 0;cursor: pointer;}.listings picture {border-bottom:.0625rem solid var(--light_gray);flex-shrink: 0;border-radius: 0;}.listings li {padding-bottom: 0;}.listings .wrap {border: .25rem solid #FFF;border-radius: 0 0 1rem 1rem;flex-grow: 1;display:flex;flex-direction: column;justify-content: space-between;}.listings .details {padding: 1rem;}.listings img {width: 100%;height: auto;aspect-ratio: 2 / 1;object-fit: cover;}.listings h3 {margin-bottom: 1rem;}.listings p {font-size: 1rem;line-height: 1.2;margin-bottom: 1rem;}.listings .tags {display: flex;flex-wrap: wrap;gap: .5rem;}.listings .tags span {background-color: var(--light);color: var(--dark);font-size: .875rem;font-weight: 500;display: inline-flex;padding: .25rem .5rem;border-radius: .25rem;}.listings button {text-align: right;transition: background-color .2s;background-color: var(--link_blue);width: 100%;border-radius: 0 0 .875rem .875rem;padding-right: 1.5em;}.listings article:hover button {cursor: pointer;background-color: var(--blue);}.listing_icons{display: flex;align-items: center;justify-content: center;position: absolute;top: .5rem;left: .5rem;gap: .5rem;z-index: 1;}.listings :is(.favorite, .view) {height: 2rem;width: 2rem;background-color: rgba(0, 0, 0, .15);border-radius: 1rem;display: flex;align-items: center;justify-content: center;transition: background-color .2s;}.listings :is(.favorite, .view):hover {background-color: rgba(0, 0, 0, 1);cursor: pointer;}.listings :is(.heart, .eye) {height: 1.25rem;display: flex;justify-content: center;align-items: center;}.listings :is(.heart, .eye):before {content: '';display: inline-flex;height: 1.25rem;width: 1.25rem;background-color: #FFF;mask-repeat: no-repeat;line-height: 1;}.listings .heart:before {width: 1rem;height: 1rem;}.results_button {margin-top: 1.5rem;}#results:empty + .results_button {display: none;}i.eye:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGwtcnVsZT0iZXZlbm9kZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLW1pdGVybGltaXQ9IjIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtMTEuOTk4IDVjLTQuMDc4IDAtNy43NDIgMy4wOTMtOS44NTMgNi40ODMtLjA5Ni4xNTktLjE0NS4zMzgtLjE0NS41MTdzLjA0OC4zNTguMTQ0LjUxN2MyLjExMiAzLjM5IDUuNzc2IDYuNDgzIDkuODU0IDYuNDgzIDQuMTQzIDAgNy43OTYtMy4wOSA5Ljg2NC02LjQ5My4wOTItLjE1Ni4xMzgtLjMzMi4xMzgtLjUwN3MtLjA0Ni0uMzUxLS4xMzgtLjUwN2MtMi4wNjgtMy40MDMtNS43MjEtNi40OTMtOS44NjQtNi40OTN6bTguNDEzIDdjLTEuODM3IDIuODc4LTQuODk3IDUuNS04LjQxMyA1LjUtMy40NjUgMC02LjUzMi0yLjYzMi04LjQwNC01LjUgMS44NzEtMi44NjggNC45MzktNS41IDguNDA0LTUuNSAzLjUxOCAwIDYuNTc5IDIuNjI0IDguNDEzIDUuNXptLTguNDExLTRjMi4yMDggMCA0IDEuNzkyIDQgNHMtMS43OTIgNC00IDQtNC0xLjc5Mi00LTQgMS43OTItNCA0LTR6bTAgMS41Yy0xLjM4IDAtMi41IDEuMTItMi41IDIuNXMxLjEyIDIuNSAyLjUgMi41IDIuNS0xLjEyIDIuNS0yLjUtMS4xMi0yLjUtMi41LTIuNXoiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PHN2ZyBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGwtcnVsZT0iZXZlbm9kZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLW1pdGVybGltaXQ9IjIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtMTEuOTk4IDVjLTQuMDc4IDAtNy43NDIgMy4wOTMtOS44NTMgNi40ODMtLjA5Ni4xNTktLjE0NS4zMzgtLjE0NS41MTdzLjA0OC4zNTguMTQ0LjUxN2MyLjExMiAzLjM5IDUuNzc2IDYuNDgzIDkuODU0IDYuNDgzIDQuMTQzIDAgNy43OTYtMy4wOSA5Ljg2NC02LjQ5My4wOTItLjE1Ni4xMzgtLjMzMi4xMzgtLjUwN3MtLjA0Ni0uMzUxLS4xMzgtLjUwN2MtMi4wNjgtMy40MDMtNS43MjEtNi40OTMtOS44NjQtNi40OTN6bTguNDEzIDdjLTEuODM3IDIuODc4LTQuODk3IDUuNS04LjQxMyA1LjUtMy40NjUgMC02LjUzMi0yLjYzMi04LjQwNC01LjUgMS44NzEtMi44NjggNC45MzktNS41IDguNDA0LTUuNSAzLjUxOCAwIDYuNTc5IDIuNjI0IDguNDEzIDUuNXptLTguNDExLTRjMi4yMDggMCA0IDEuNzkyIDQgNHMtMS43OTIgNC00IDQtNC0xLjc5Mi00LTQgMS43OTItNCA0LTR6bTAgMS41Yy0xLjM4IDAtMi41IDEuMTItMi41IDIuNXMxLjEyIDIuNSAyLjUgMi41IDIuNS0xLjEyIDIuNS0yLjUtMS4xMi0yLjUtMi41LTIuNXoiIGZpbGwtcnVsZT0ibm9uemVybyIvPjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}@media (min-width: 1441px) {.listings .details {padding: 1rem 1.5rem;}.results_button {margin-top: 2.5rem;}}.icon_round_bg {border-radius: 50%;box-sizing: border-box;display: flex;height: 2rem;width: 2rem;justify-content: center;align-items: center;border:.0625rem solid var(--light_gray);}.results_tile_icon {background-color: #fff;position: absolute;margin-top: -1.375rem;right: 0.5rem;height: 2.25rem;width: 2.25rem;cursor: pointer;z-index: 1;}.more_modal {width: 12.5rem;position: absolute;background: #fff;right: .625rem;margin-top: 1rem;padding: 1rem;border-radius: 1.25rem;box-shadow: 0 .375rem 1.5rem rgba(0, 0, 0, 0.2);}.more_modal ul li{display: flex;align-items: center;gap: 0.25rem;color: var(--dark);cursor: pointer;}.more_modal ul li:first-child{margin-bottom: 1rem;}.results_tile_icon svg{width: 100% !important;height: 100% !important;padding: .5rem;}.compare_section{width: 100%;padding-bottom: 1.5rem;}.compare-flex{display: flex;justify-content: center;align-items: center;gap: 1rem;}.compare_container .compare_thumbnail{width: 7rem;background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4IiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDUwIDUwIiBmaWxsPSIjN0Y5QUM3Ij4KPHBhdGggZD0iTSAyNSAyIEMgMTIuMzA5NTM0IDIgMiAxMi4zMDk1MzQgMiAyNSBDIDIgMzcuNjkwNDY2IDEyLjMwOTUzNCA0OCAyNSA0OCBDIDM3LjY5MDQ2NiA0OCA0OCAzNy42OTA0NjYgNDggMjUgQyA0OCAxMi4zMDk1MzQgMzcuNjkwNDY2IDIgMjUgMiB6IE0gMjUgNCBDIDM2LjYwOTUzNCA0IDQ2IDEzLjM5MDQ2NiA0NiAyNSBDIDQ2IDM2LjYwOTUzNCAzNi42MDk1MzQgNDYgMjUgNDYgQyAxMy4zOTA0NjYgNDYgNCAzNi42MDk1MzQgNCAyNSBDIDQgMTMuMzkwNDY2IDEzLjM5MDQ2NiA0IDI1IDQgeiBNIDM0Ljk4ODI4MSAxNC45ODgyODEgQSAxLjAwMDEgMS4wMDAxIDAgMCAwIDM0LjE3MTg3NSAxNS40Mzk0NTMgTCAyMy45NzA3MDMgMzAuNDc2NTYyIEwgMTYuNjc5Njg4IDIzLjcxMDkzOCBBIDEuMDAwMSAxLjAwMDEgMCAxIDAgMTUuMzIwMzEyIDI1LjE3NzczNCBMIDI0LjMxNjQwNiAzMy41MjUzOTEgTCAzNS44MjgxMjUgMTYuNTYwNTQ3IEEgMS4wMDAxIDEuMDAwMSAwIDAgMCAzNC45ODgyODEgMTQuOTg4MjgxIHoiPjwvcGF0aD4KPC9zdmc+");background-origin: content-box;background-position: center;background-repeat: no-repeat;background-size: 2rem;height: 4rem;border: .125rem dashed var(--link_blue);border-radius: .5rem;}.compare_container .compare_thumbnail.has_image{background-color: #fff;background-size: cover;border: none;position: relative;}.compare_container .compare_thumbnail.has_image svg{position: absolute;right: -0.5rem;top: -0.5rem;width: 1.25rem;fill: #be0000;cursor: pointer;}.compare_section .btn_compare{background-color: var(--dark_gray);border: none;color: #fff;pointer-events: none;}#btn_compare.active{background-color: var(--link_blue);pointer-events: all;}.icon_round_bg{border-radius: 50%;box-sizing: border-box;display: flex;height: 2.25rem;width: 2.25rem;justify-content: center;align-items: center;border:.0625rem solid var(--blue);}.compare_thumbnail .compare-icon {color: var(--blue);opacity: 0.5;border-width: 0.25rem;}.compare_select{width: 100% !important;height: 100% !important;position: absolute;z-index: 1;max-width: 100% !important;opacity: 0;}.compare_select:checked + svg{fill: #fff;}.results_tile_icon:has(.compare_select:checked) {background-color: var(--blue);}.sitepage_content table{table-layout: auto !important;width: 100% !important;}@media only screen and (max-width: 1024px) {.compare_section.compare-flex {display: block;margin-top: 2rem;}.compare_section .compare_button {text-align: center;margin-top: 0.75rem;}}@media only screen and (max-width:768px) {.compare_container {overflow-x: scroll;justify-content: flex-start;padding-bottom: 0.5rem;}.compare_container .compare_thumbnail {flex: 0 0 6.5rem;}.compare_container .compare_thumbnail.has_image svg{right: 0;top: 0;z-index: 1;width: 1rem;}#listing_title{position: relative;}#listing_title .compare_by{position: absolute;top: 0;right: 0;}#listing_title .compare_by .switch{width: 4rem;height: 2rem;}.switch span::before{height: 1.625rem;width: 1.625rem;left: 0.1875rem;bottom: 0.1875rem;}.switch input:not(.noevents):checked + span::before, .switch.search_compare > span::before{-webkit-transform: translateX(2rem);-ms-transform: translateX(2.25rem);transform: translateX(2rem);}}p {line-height: 1.5;}#title_section h1 + h2{font-size: 1rem;font-weight: 500;}#intro, #outro {padding: 2.5rem 0;}:is(.states, .long_description).light_gray_border{padding-top: 1rem;}picture {width: 100%;height: auto;aspect-ratio: 2 / 1;background: #FFF;border-radius: 1rem;display: flex;align-items: center;justify-content: center;overflow: hidden;}picture.new img{width: 100%;height: auto;}#details li {padding-bottom: 0;}#details h3 {margin-bottom: .5rem;}#details table {width: 100%;}#details.grid1 table {width: 25rem;max-width: 100%;}#details tr:not(:has(.subtitle)) {display: grid;grid-gap: 1rem;grid-auto-columns: minmax(0, 1fr);grid-template-columns: 1fr 1fr;border-bottom: 1px solid var(--light);margin-bottom: 0.5rem;}#details td {padding-bottom: 1em;font-size: 1rem;}#details td:nth-last-child(1){padding-bottom: 0;}#details td.subtitle {display: block;margin-bottom: 1rem;padding-bottom: 1rem;border-bottom:.0625rem solid var(--light_gray);}#details td.value {font-weight: 800;color: var(--dark);}.copy h4{font-size: 1rem;}#title_section button {display: inline-flex;overflow: hidden;margin: 1rem 0 0;padding: .5rem;border-radius: .5rem;align-items: center;cursor: pointer;}#title_section button + button {margin-top: 1rem;}#title_section button span {height: 1.5rem;line-height: 1.5rem;padding: 0 .5rem 0 0;}button.favorites {max-width: 100%;justify-content: space-between;background-color: #fff !important;position: relative;}button.favorites input {opacity: 0;position: absolute;inset: 0;cursor: pointer;width: 100%;height: 100%;z-index: 1;}#title_section button.favorites span {color: var(--dark_gray);}#small_button{margin-left: .5rem;}button.favorites .heart {height: 1.5rem;width: 1.5rem;display: inline-flex;align-items: center;justify-content: center;}button.favorites .heart:before {content: '';display: inline-flex;height: 1.5rem;width: 1.5rem;background-color: var(--dark_gray);mask-repeat: no-repeat;}button.favorites:has(input:checked) .heart:before {background-color: var(--red);-webkit-mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');-webkit-mask-size: contain;mask-size: contain;}.title_with_icon button.favorites:has(input:checked){background-color: #fff !important;border-color: transparent !important;}#title_section.title_with_icon button.favorites:has(input:checked) span{color: var(--dark_gray);}.title_with_icon button.favorites:has(input:checked) :is(.save,.heart){background-color: #fff;}#outro :is(h2, p) {margin-bottom: 1.5rem;}#request_information .guts {max-width: 31.25rem;}#request_information h3 {margin-bottom: .5em;}.checkbox_button {display: flex;margin-bottom: 1em;}p {margin-bottom: 2em;}#request_information .checkbox_button label{align-items: flex-start;}#request_information .checkbox_button {margin-bottom: 1em;}#request_information .grid2 {grid-gap: 1rem;}#request_information .grid2:not(.buttons) {grid-gap: 0;}.buttons button {width: 100%;}button.cancel {color: var(--dark);background-color: #FFF !important;box-shadow: inset 0 0 0 0.0625rem var(--link_blue);}body:has(#request_information.show) {overflow-y: hidden;}.request_info.error-input {border-color: var(--red) !important;}label:has(input.error-input) span {color: var(--red) !important;}.relative {position: relative;}.profile_icons{display: flex;align-items: center;justify-content: center;position: absolute;top: .5rem;left: .5rem;gap: .5rem;z-index: 1;}.profile_icons .favorite {height: 2rem;width: 2rem;background-color: rgba(0, 0, 0, .15);border-radius: 1rem;display: flex;align-items: center;justify-content: center;transition: background-color .2s;}.profile_icons .favorite:hover {background-color: rgba(0, 0, 0, 1);cursor: pointer;}.heart {height: 1.25rem;display: flex;justify-content: center;align-items: center;}.heart:before {content: '';display: inline-flex;height: 1.25rem;width: 1.25rem;background-color: #FFF;mask-repeat: no-repeat;line-height: 1;}.heart:before {width: 1rem;height: 1rem;}#connect {position: fixed;inset: auto 0 0;text-align: center;background: #FFF;padding: .75rem;border-top: .0625rem solid var(--light_gray);z-index: 3;}#connect .container {padding: 0 .75rem;}i.arrow:before {content: '';display: block;height: 2.5rem;width: 2.5rem;background-color: var(--link_blue);mask-repeat: no-repeat;-webkit-mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');mask: url('data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');-webkit-mask-size: contain;mask-size: contain;}#connect button {font-size: 1.25rem;margin: 0;width: 100%;padding: .75em .5em;font-weight: 600;}:is(#menu_modal, #top .back) i:before {content: '';display: block;height: 1rem;width: 1rem;background-color: var(--dark);mask-repeat: no-repeat;margin-right: .75rem;}i.back:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGwtcnVsZT0iZXZlbm9kZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLW1pdGVybGltaXQ9IjIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtOS40NzQgNS4yMDlzLTQuNTAxIDQuNTA1LTYuMjU0IDYuMjU5Yy0uMTQ3LjE0Ni0uMjIuMzM4LS4yMi41M3MuMDczLjM4NC4yMi41M2MxLjc1MiAxLjc1NCA2LjI1MiA2LjI1NyA2LjI1MiA2LjI1Ny4xNDUuMTQ1LjMzNi4yMTcuNTI3LjIxNy4xOTEtLjAwMS4zODMtLjA3NC41My0uMjIxLjI5My0uMjkzLjI5NC0uNzY2LjAwNC0xLjA1N2wtNC45NzYtNC45NzZoMTQuNjkyYy40MTQgMCAuNzUtLjMzNi43NS0uNzVzLS4zMzYtLjc1LS43NS0uNzVoLTE0LjY5Mmw0Ljk3OC00Ljk3OWMuMjg5LS4yODkuMjg3LS43NjEtLjAwNi0xLjA1NC0uMTQ3LS4xNDctLjMzOS0uMjIxLS41My0uMjIxLS4xOTEtLjAwMS0uMzguMDcxLS41MjUuMjE1eiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+');mask: url('data:image/svg+xml;base64,PHN2ZyBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGZpbGwtcnVsZT0iZXZlbm9kZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLW1pdGVybGltaXQ9IjIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtOS40NzQgNS4yMDlzLTQuNTAxIDQuNTA1LTYuMjU0IDYuMjU5Yy0uMTQ3LjE0Ni0uMjIuMzM4LS4yMi41M3MuMDczLjM4NC4yMi41M2MxLjc1MiAxLjc1NCA2LjI1MiA2LjI1NyA2LjI1MiA2LjI1Ny4xNDUuMTQ1LjMzNi4yMTcuNTI3LjIxNy4xOTEtLjAwMS4zODMtLjA3NC41My0uMjIxLjI5My0uMjkzLjI5NC0uNzY2LjAwNC0xLjA1N2wtNC45NzYtNC45NzZoMTQuNjkyYy40MTQgMCAuNzUtLjMzNi43NS0uNzVzLS4zMzYtLjc1LS43NS0uNzVoLTE0LjY5Mmw0Ljk3OC00Ljk3OWMuMjg5LS4yODkuMjg3LS43NjEtLjAwNi0xLjA1NC0uMTQ3LS4xNDctLjMzOS0uMjIxLS41My0uMjIxLS4xOTEtLjAwMS0uMzguMDcxLS41MjUuMjE1eiIgZmlsbC1ydWxlPSJub256ZXJvIi8+PC9zdmc+');-webkit-mask-size: contain;mask-size: contain;}i.compare-results:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNNCA0djIwaDIwdi0yMGgtMjB6bTE4IDE4aC0xNnYtMTNoMTZ2MTN6bS0zLTNoLTEwdi0xaDEwdjF6bTAtM2gtMTB2LTFoMTB2MXptMC0zaC0xMHYtMWgxMHYxem0yLTExaC0xOXYxOWgtMnYtMjFoMjF2MnoiLz48L3N2Zz4=');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNNCA0djIwaDIwdi0yMGgtMjB6bTE4IDE4aC0xNnYtMTNoMTZ2MTN6bS0zLTNoLTEwdi0xaDEwdjF6bTAtM2gtMTB2LTFoMTB2MXptMC0zaC0xMHYtMWgxMHYxem0yLTExaC0xOXYxOWgtMnYtMjFoMjF2MnoiLz48L3N2Zz4=');-webkit-mask-size: contain;mask-size: contain;}i.net-worth-calculator:before {-webkit-mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgMGgtMTRjLTIuNzYxIDAtNSAyLjIzOS01IDV2MTRjMCAyLjc2MSAyLjIzOSA1IDUgNWgxNGMyLjc2MiAwIDUtMi4yMzkgNS01di0xNGMwLTIuNzYxLTIuMjM4LTUtNS01em0tMTcgNWMwLTEuNjU0IDEuMzQ2LTMgMy0zaDZ2OWgtOXYtNnptMCAxNHYtNmg5djloLTZjLTEuNjU0IDAtMy0xLjM0Ni0zLTN6bTIwIDBjMCAxLjY1NC0xLjM0NiAzLTMgM2gtNnYtOWg5djZ6bTAtOGgtOXYtOWg2YzEuNjU0IDAgMyAxLjM0NiAzIDN2NnptLTIgNmgtNXYtMWg1djF6bS01LTExaDV2MWgtNXYtMXptMCAxM3YtMWg1djFoLTV6bS02LTJ2MWgtMnYyaC0xdi0yaC0ydi0xaDJ2LTJoMXYyaDJ6bS0xLjc5My0xMC41bDEuNDE0IDEuNDE0LS43MDcuNzA3LTEuNDE0LTEuNDE0LTEuNDE0IDEuNDE0LS43MDgtLjcwNyAxLjQxNC0xLjQxNC0xLjQxNC0xLjQxNC43MDctLjcwNyAxLjQxNSAxLjQxNCAxLjQxNS0xLjQxNS43MDguNzA4LTEuNDE2IDEuNDE0em05Ljc5My0yYzAtLjI3Ni4yMjQtLjUuNS0uNXMuNS4yMjQuNS41LS4yMjQuNS0uNS41LS41LS4yMjQtLjUtLjV6bTEgNGMwIC4yNzYtLjIyNC41LS41LjVzLS41LS4yMjQtLjUtLjUuMjI0LS41LjUtLjUuNS4yMjQuNS41eiIvPjwvc3ZnPg==');mask: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMTkgMGgtMTRjLTIuNzYxIDAtNSAyLjIzOS01IDV2MTRjMCAyLjc2MSAyLjIzOSA1IDUgNWgxNGMyLjc2MiAwIDUtMi4yMzkgNS01di0xNGMwLTIuNzYxLTIuMjM4LTUtNS01em0tMTcgNWMwLTEuNjU0IDEuMzQ2LTMgMy0zaDZ2OWgtOXYtNnptMCAxNHYtNmg5djloLTZjLTEuNjU0IDAtMy0xLjM0Ni0zLTN6bTIwIDBjMCAxLjY1NC0xLjM0NiAzLTMgM2gtNnYtOWg5djZ6bTAtOGgtOXYtOWg2YzEuNjU0IDAgMyAxLjM0NiAzIDN2NnptLTIgNmgtNXYtMWg1djF6bS01LTExaDV2MWgtNXYtMXptMCAxM3YtMWg1djFoLTV6bS02LTJ2MWgtMnYyaC0xdi0yaC0ydi0xaDJ2LTJoMXYyaDJ6bS0xLjc5My0xMC41bDEuNDE0IDEuNDE0LS43MDcuNzA3LTEuNDE0LTEuNDE0LTEuNDE0IDEuNDE0LS43MDgtLjcwNyAxLjQxNC0xLjQxNC0xLjQxNC0xLjQxNC43MDctLjcwNyAxLjQxNSAxLjQxNCAxLjQxNS0xLjQxNS43MDguNzA4LTEuNDE2IDEuNDE0em05Ljc5My0yYzAtLjI3Ni4yMjQtLjUuNS0uNXMuNS4yMjQuNS41LS4yMjQuNS0uNS41LS41LS4yMjQtLjUtLjV6bTEgNGMwIC4yNzYtLjIyNC41LS41LjVzLS41LS4yMjQtLjUtLjUuMjI0LS41LjUtLjUuNS4yMjQuNS41eiIvPjwvc3ZnPg==');-webkit-mask-size: contain;mask-size: contain;}#outro:has(#results:empty) {display: none;}@media (max-width: 768px) {#title_section button {flex-direction: row-reverse;margin-left: -.5rem;}#title_section button span {padding: 0 0 0 .5rem;}}@media (max-width: 1200px) {li:is(.dashboard,.franchise,.resources,.compare-results,.net-worth-calculator){display: block;}header#bottom, .logo, #connect :is(#connect_intro, .arrow) {display: none;}.back {display: flex;font-size: 1.25rem;}#top .back i:before {height: 1.5rem;width: 1.5rem;margin-right: .5rem;}}@media (min-width: 768px) {.white_shell {height: 100%;}#outro img {width: 100%;height: auto;}#outro .tcol8 {display: flex;flex-direction: column;align-items: flex-start;justify-content: center;}#request_information .guts {max-width: 40rem;}#request_information .grid2 {grid-gap: 1rem !important;}#details td {padding-bottom: 1em;font-size: 0.875rem;}#connect button {width: auto;padding: .75em 2em;}}@media (min-width: 769px) {.flex_center_right {display: flex;flex-direction: row;justify-content: flex-end;align-items: center;}}@media (min-width: 1025px) {#intro, #outro {padding: 3.5rem 0;}#title_section button {margin-top: 0;}#title_section button + button {margin: 0 0 0 1rem;}#outro :is(h2, p) {margin-bottom: 2rem;}:is(.states, .long_description).light_gray_border{margin-top: 2rem;padding-top: 2rem;}.white_shell:not(article) {padding: 1.5rem;}}@media (min-width: 1201px) {#connect {line-height: 1;}#connect .container {display: flex;align-items: center;font-size: 1.25rem;justify-content: flex-end;}#connect_intro span {font-weight: 600;}#connect_intro strong {color: var(--dark);text-transform: uppercase;margin-left: .5rem;}#arrow {margin: auto 1.5rem;}button.favorites:hover {background-color: var(--light) !important;}}@media (min-width: 1332px) {#connect .container {padding: 0 1.5rem;}}@media (min-width: 1441px) {#intro, #outro {padding: 4.5rem 0;}}#lead_step_1 .input_wrapper{margin-bottom: 0.85rem;}#one_time_password_modal{z-index: 5;}#one_time_password_modal .guts{max-width: 31.25rem;}#one_time_password_modal h3{text-align: left;}#one_time_password_modal .login_display p{margin-bottom: 1rem;}#one_time_password_modal .login_display .form_fields{margin-top: 2rem;margin-bottom: 0.5rem;}#one_time_password_modal .login_display .resend_code{font-size: 0.8rem;}#one_time_password_modal .login_display .resend_code a{color: var(--link_blue);text-decoration: underline;}#one_time_password_modal .login_display .login_btns{margin-top: 2rem;}#signup_modal.forward{z-index: 5;}#signup_modal.forward h3{margin-bottom: 0;}#signup_modal.set_password .guts{max-width: 40rem;;}#request_information .checkbox_button label span{color: var(--dark_gray);}#alert.request_alert .content{text-align: left;}#alert.request_alert .alert{max-width: 40rem;padding: 2rem;}#alert.request_alert .request_alert_btns{text-align: right;margin-top: 2rem;}#alert.request_alert .checkcircle:before {content: '';display: inline-flex;height: 2rem;width: 2rem;background-color: var(--border_green);mask-repeat: no-repeat;line-height: 1;position: absolute;right: 2rem;}</style>                            
            <link rel="apple-touch-icon" sizes="180x180" href="https://franchise-ventures-general.s3.us-east-1.amazonaws.com/fcom_cdn/icon/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="https://franchise-ventures-general.s3.us-east-1.amazonaws.com/fcom_cdn/icon/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="https://franchise-ventures-general.s3.us-east-1.amazonaws.com/fcom_cdn/icon/favicon-16x16.png">
        <link rel="manifest" href="https://www.franchise.com/icon/manifest.json">
        <link rel="mask-icon" href="https://franchise-ventures-general.s3.us-east-1.amazonaws.com/fcom_cdn/icon/safari-pinned-tab.svg" color="#5bbad5">
        <link rel="shortcut icon" href="https://www.franchise.com/favicon.ico">
        <meta name="msapplication-TileColor" content="#b91d47">
        <meta name="msapplication-config" content="/icon/browserconfig.xml">
        <meta name="theme-color" content="#ffffff">
    <script type="text/javascript" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/api.min.js" async="" data-user="164494" data-account="178508"></script><link rel="stylesheet" href="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/api.min.css" id="omapi-css" media="all"><script src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/5695332.js" type="text/javascript" async="" data-ueto="ueto_ba555d6df3"></script></head>
<body data-new-gr-c-s-check-loaded="14.1250.0" data-gr-ext-installed="">
    <script type="application/ld+json">
                {
                    "@context": "https://schema.org",
                    "@type": "BreadcrumbList",
                    "itemListElement": [
                        {
                    "@type": "ListItem",
                    "position": 1,
                    "item":
                    {
                        "@id": "https://www.franchise.com",
                        "name": "Home"
                    }
                },{
                    "@type": "ListItem",
                    "position": 2,
                    "item":
                    {
                        "@id": "https://www.franchise.com/franchise-categories-all",
                        "name": "Industries"
                    }
                },{
                    "@type": "ListItem",
                    "position": 3,
                    "item":
                    {
                        "@id": "https://www.franchise.com/home-related",
                        "name": "Home Services"
                    }
                },{
                    "@type": "ListItem",
                    "position": 4,
                    "item":
                    {
                        "@id": "https://www.franchise.com/franchise/mosquito-hunters",
                        "name": "Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise"
                    }
                }
                    ]
                }
            </script>    <main>
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting",
            "address": {
                "@type": "PostalAddress",
					"addressCountry": "US"
            },
            "foundingDate": "2014",
				"additionalProperty": [
					{
                    "@type": "PropertyValue",
                    "name": "Franchise Units",
                    "value": "140" 
                },
					{
                    "@type": "PropertyValue",
                    "name": "Franchising Since",
                    "value": "2017" 
                }
				]
        }
		</script><script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Offer",
            "name": "Financial Requirements",
            "description": "Financial requirements for the franchise",
            "priceCurrency": "USD",
            "priceSpecification": [
					{
                    "@type": "PriceSpecification",
                    "name": "Minimum Cash Required",
						"price": "60000",
						"priceCurrency": "USD"
                },
					{
                    "@type": "PriceSpecification",
                    "name": "Net Worth",
						"price": "200000",
						"priceCurrency": "USD"
                },
					{
                    "@type": "PriceSpecification",
                    "name": "Franchise Fee",
						"price": "50000",
						"priceCurrency": "USD"
                },
					{
                    "@type": "PriceSpecification",
                    "name": "Royalty Fee",
						"price": "not provided",
						"priceCurrency": "USD"
                },
					{
                    "@type": "PriceSpecification",
                    "name": "Total Investment",
						"minPrice": "141295",
						"maxPrice": "170743",
						"priceCurrency": "USD"
                }
				]
        }
		</script>
<script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Service",
            "name": "Franchise Options",
            "description": "Options available to franchisees",
            "category": [
					"Home Based",
					"Mobile Franchise",
					"Military Discount",
					"Financing",
					"Training"
				]
        }
		</script>
<section id="title_section" class="title_with_icon">
    <div class="container">
        <div class="columns">
            <div class="tcol8">
                <h1>Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting</h1>
                <h2>Home Services</h2>
            </div>
            <div class="tcol8 flex_center_right">
                <button class="favorites">
                            <input id="fav_checkbox_15683" data-category="Home Services" data-name="Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting" data-target="favorites" type="checkbox" class="favorites" data-fboid="15683" onclick="event.stopPropagation();favorites(15683);"><span>Favorite</span><i class="heart"></i>
                        </button>            </div>
        </div>
    </div>
</section>
<section class="light_bg">
    <div class="container">
        <div id="intro" class="columns">
            <div class="tcol8">
                <p>Mosquito Hunters is now Mosquito Hunters &amp; Humbug Holiday Lighting. We are a cutting-edge 3-in-1 business model that has annual revenue &amp; income opportunities. A 3-in-1 business model means an Annual Revenue Opportunity for Mosquito Hunters &amp; Humbug Holiday Lighting 1. Mosquitoes: Barrier (Traditional &amp; All Green), Stations, Smart Repellent Systems2. Perimeter Pest Control: Ticks, Ants, Spiders, Stinkbugs, Rodents, Deer Repellent3. Holiday Lighting: Design, Install, Maintenance, Take Down, etc.</p>                <div class="states light_gray_border">
                    <h3>Availability</h3>
                    <p><strong>Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting</strong> is currently accepting inquiries from the following states:</p>
                <p><strong>AL, AR, CO, CT, DE, FL, GA, IA, ID, IL, IN, KS, KY, LA, MA, MD, ME, MI, MN, MO, MS, NC, ND, NE, NH, NJ, NY, OH, OK, PA, RI, SC, SD, TN, TX, UT, VA, WI, WV, WY</strong></p>                </div>
            </div>
            <div class="tcol8 relative">
                <picture class="new">
                    <img src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/mosquitohunters-960x480.jpg" alt="Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting width=" 383"="" height="192" "="">
                </picture>
            </div>
        </div>
        <ul id="details" class="grid3"><li>
                        <div class="white_shell">
                            <h3>Requirements</h3>
                            <table>
                                <tbody><tr>
                                    <td class="subtitle" colspan="2">Financial requirements for this franchise:</td>
                                </tr><tr><td class="title">Minimum Cash Required</td><td class="value">$60,000</td></tr><tr><td class="title">Net Worth</td><td class="value">$200,000</td></tr><tr><td class="title">Franchise Fee</td><td class="value">$50,000</td></tr><tr><td class="title">Royalty Fee</td><td class="value">10%</td></tr><tr><td class="title">Total Investment</td><td class="value">$141,295 - $170,743</td></tr>
                            </tbody></table>
                        </div>
                    </li><li>
                        <div class="white_shell">
                            <h3>Options</h3>
                            <table>
                                <tbody><tr>
                                    <td class="subtitle" colspan="2">Options available to franchisees:</td>
                                </tr><tr><td class="title">Home Based</td><td class="value">Yes</td></tr><tr><td class="title">Mobile Franchise</td><td class="value">Yes</td></tr><tr><td class="title">Military Discount</td><td class="value">Available</td></tr><tr><td class="title">Financing</td><td class="value">Available</td></tr><tr><td class="title">Training</td><td class="value">Available</td></tr>
                            </tbody></table>
                        </div>
                    </li><li>
                        <div class="white_shell">
                            <h3>Franchisor Details</h3>
                            <table>
                                <tbody><tr>
                                    <td class="subtitle" colspan="2">Facts about this franchise:</td>
                                </tr><tr><td class="title">Headquarters</td><td class="value">Holmdel, NJ</td></tr><tr><td class="title">Total Units</td><td class="value">140</td></tr><tr><td class="title">Year Founded</td><td class="value">2014</td></tr><tr><td class="title">Franchising Since</td><td class="value">2017</td></tr>
                            </tbody></table>
                        </div>
                    </li></ul>        <div class="copy">
            <h2>What does a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise Cost?</h2>
            <p>The cost to own your own Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting franchise requires you to make an initial total investment of approximately <strong>$141,295 - $170,743</strong>. Additionally, you will need to have at least <strong>$60,000</strong> in liquid capital. They also offer a discount for veterans (10% off the initial license fee discount for VETFRAN, MINORITYFRAN, and First Responders). <sup>*</sup></p>            <div class="long_description light_gray_border">
                <p>Mosquito Hunters is now&nbsp;<strong>Mosquito Hunters &amp; Humbug Holiday Lighting</strong>. We are a cutting edge 3 in 1 business model that has annual revenue and income opportunities.</p><p>A Three in One business model means an Annual Revenue Opportunity for Mosquito Hunters &amp; Humbug Holiday Lighting</p><p>1. Mosquitoes: Barrier (Traditional &amp; All Green), Stations, Smart Repellent Systems</p><p>2. Perimeter Pest Control: Ticks, Ants, Spiders, Stinkbugs, Rodents, Deer Repellent</p><p>3. Holiday Lighting: Design, Install, Maintenance, Take Down, &amp; Storage</p><p>Mosquito Hunters make mosquito and general pest control affordable, effective, and environmentally responsible too. By focusing on building client relationships, we provide mosquito control services guaranteed by our Client Happiness Promise. By now offering holiday lighting services within the model, we provide an annual business opportunity to franchise owners, providing consistent cash flow throughout the entire year,&nbsp;</p><p>For owners, we provide corporate driven lead generation, a national sales and support call center to help manage growth and give owners the ability to run an office from home to help manage overhead and cut down fixed cost. In addition to being focused on results and great customer service, Mosquito Hunters strives to provide immense value to its franchise partners. Mosquito Hunters understands the importance of both lead generation and speed to lead in this fast-paced, ever-changing world we live in. We have a world class corporate media team and an in-house call center that does the heavy lifting for their franchisees advertising and day to day sales. This gives their owners a unique opportunity to focus on local customer relationship building that lends itself to long term retention. There has never been a better time to start a Mosquito Hunter’s and Humbug Holiday Lighting franchise than right now!</p><ul><li><p>No location required; home office is typical. Small industrial warehouse type location is all one would need, even for larger operators</p></li><li><p>Start-up ease: no retail location required, no costly outfitting</p></li><li><p>Strong unit-level economics (annual revenue with perimeter pest and holiday lighting service line)</p></li><li><p>Simple, scalable business model</p></li><li><p>Recurring revenue stream, enabling investments in growth</p></li><li><p>No cold calling or hard sales, very consultative process</p></li><li><p>Corporate lead generation and call center to perform sales, freeing up owners to focus on upsells and retention</p></li><li><p>We also offer a franchise fee discount of 10% to qualified candidates that are military veterans, a minority, or a first responder.</p></li><li><p>Extensive training and support from experienced franchisor</p></li><li><p>Home-based business offers personal, professional, and financial flexibility</p></li><li><p>Recurring revenue / High retention-based business offering long-range profit potential</p></li><li><p>World class branding and marketing makes Hunters/Humbug inherently memorable</p></li><li><p>State-of-the-art centralized media, in house sales center and back-office management system</p></li><li><p>Well-funded franchisor with 50+ years of experience</p></li></ul><h2>Industry Talking Points</h2><ul><li><p>Fragmented, mostly moms and pops, tons of opportunity for differentiation and market share.</p></li><li><p>$26 billion dollar pest control space, projected annual growth of 20%.</p></li><li><p>$6 Billion dollar lighting space in it’s infancy stages</p></li><li><p>Fragmented competition / Unsophisticated competition</p></li></ul><h2>Who Makes a Strong Franchisee?</h2><ul><li><p>You’re high-energy with a strong work ethic: love the idea of being in business for yourself because you’ll have the chance to work hard and build something of your own</p></li><li><p>You’re passionate about service: you believe in speed to lead, good customer service, and the relationships that develop as a result, are the cornerstone of success</p></li><li><p>You’ve got management experience: you’re not afraid to encourage, motivate, and educate employees, and to let the buck stop with you</p></li><li><p>You’ve got sales skills: you know how to help customers understand and appreciate the value of your services</p></li><li><p>You’re a Big Picture thinker: you’ve got the patience and vision to know that success has a lot of moving parts and that it takes teamwork.</p></li><li><p>You enjoy being mobile and on the go</p></li><li><p>You enjoy a consultative approach, not hard sales</p></li></ul><h2>Why Mosquito Hunters and Humbug Holiday Lighting?</h2><ul><li><p>Repeat Clientele Business Model</p></li><li><p>Protected Territories</p></li><li><p>Quick-Starting, Home-Based Business</p></li><li><p>Low Startup Costs, Low Overhead</p></li><li><p>Lightning-fast Growth Industry</p></li><li><p>Annual business combining mosquito control with perimeter pest and holiday lighting service lines</p></li><li><p>Proven Lead Generation, No Cold Calling Needed</p></li><li><p>In house call center handling sales</p></li><li><p>We are a 3 in 1 annual model:</p></li></ul><p><strong>Mosquito</strong></p><ul><li><p>Barrier</p></li><li><p>Stations</p></li><li><p>Smart Repellent Systems</p></li></ul><p><strong>Outdoor Pest</strong></p><ul><li><p>Perimeter Pest (ants, spiders, stinkbugs, etc. all in one treatment)</p></li><li><p>Rodent</p></li><li><p>Tick</p></li><li><p>Deer repellent</p></li></ul><p><strong>Holiday Lighting (Humbug Holiday Lighting)</strong></p>                            </div>
        </div>
    </div>
</section>
<div id="connect"><div class="container"><div id="connect_intro">Want to connect with the team at this franchise? <strong onclick="ga4_global_events(&#39;beta_view_item&#39;,&#39;fbo_id&#39;,15683);modal(&#39;request_information&#39;);">Start Here</strong></div>
                            <div id="arrow"><i class="arrow"></i></div>
                            <button id="small_button" onclick="ga4_global_events(&#39;beta_view_item&#39;,&#39;fbo_id&#39;,15683);modal(&#39;request_information&#39;);"><span>Connect with this Franchise</span></button></div></div><div id="request_information" class="modal" onclick="closeModalOutsideClick(&#39;request_information&#39;);">
    <div class="guts">
        <div class="content">
            <form id="submission" onsubmit="event.preventDefault();return false;">
                <input type="hidden" name="fbolist" value="15683"><input type="hidden" name="investment_level" value="60000"><input type="hidden" id="utm_medium" name="utm_medium" value=""><input type="hidden" id="utm_source" name="utm_source" value=""><input type="hidden" id="utm_campaign" name="utm_campaign" value=""><input type="hidden" id="utm_type" name="utm_type" value=""><input type="hidden" id="gclid_mlclkid" name="gclid_mlclkid" value=""><input type="hidden" id="first_name" name="first_name" value="Rakesh"><input type="hidden" id="last_name" name="last_name" value="Nookala"><input type="hidden" id="email" name="email" value="<EMAIL>"><input type="hidden" id="phone" name="phone" value="4045550100"><input type="hidden" id="zip_code" name="zip_code" value="23455"><input type="hidden" id="preferred_state" name="preferred_state" value="GA"><input type="hidden" id="profile_name" name="profile_name" value="Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting"><h3>Request Information</h3>
        <p>You are requesting additional information about Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting.</p><div class="checkbox_button">
                                <label>
                                    <input type="checkbox" value="1" name="request_info" checked="" class="request_info">
                                    <span class="italic">By pressing "Connect", you agree that Franchise.com and businesses you selected may call/text/email you, including for marketing purposes related to your inquiry. This contact may be made using automated or pre-recorded/artificial voice technology. Data and message rates may apply. You don't need to consent as a condition of any purchase. You may opt-out of SMS at any time by replying <strong>STOP</strong> to the phone number we texted you from. You also agree to our <a href="https://www.franchise.com/privacy-statement" target="_blank">Privacy Policy &amp; Terms of Use</a>.</span>
                                </label>
                            </div>
                            <div class="buttons">
                                <button type="button" onclick="submitForm();">Connect Now</button>
                            </div>            </form>
        </div>
        <div class="close_modal" onclick="closeModalOpened(&#39;submission&#39;);">×</div>
    </div>
</div>
<div id="one_time_password_modal" class="modal">
    <div class="guts">
        <div class="content">
            <form id="one_time_password_form" onsubmit="event.preventDefault();return false;">
                <input id="one_time_email" type="hidden" name="one_time_email">
                <div class="columns">
                    <div class="tcol16 login_display">
                        <h3 id="login_h3" class="submission">Check Your Email for a Verification Code</h3>
                        <p id="login_subtitle">We found an account with <span class="one_time_email_val bold"></span></p>
                        <p>We sent a one-time code to your inbox. Enter it below to verify your email and set your password.</p>
                        <div class="input_wrapper form_fields">
                            <label for="one_time_passcode">One-Time Code *</label>
                            <input id="one_time_passcode" type="text" class="form-control" name="one_time_passcode" autocomplete="off" required="">
                            <div class="error hide"></div>
                        </div>
                        <p class="resend_code"><span class="italic"><strong>Didn’t get the email?</strong> Check your spam folder or click </span><a href="javascript:void(0);" onclick="resendOneTimeCode();">Resend Code</a></p>
                        <div class="input_wrapper login_btns">
                            <button type="button" class="btn button solid w-100" data-target="login" data-name="Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting" onclick="validateOneTimePassword(this)">Verify Code</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="close_modal" onclick="closeModalOpened(&#39;one_time_password_modal&#39;);">×</div>
    </div>
</div>
<script>const item_id = "15683", item_name = "Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting", item_category = "Home Services", price = "0";</script><script>var state_code = "GA", cat_id = "12";</script><script>
    var profile_buttons_connect = null;
</script>    </main>
    <!-- HEADER START -->
<header id="top">
    <div class="header_left">
        
        <div>
            <a href="https://www.franchise.com/" class="logo">
                <img src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/franchise.com.svg" width="200" height="32" aria-label="Franchise.com" alt="Franchise.com">
            </a>
            <a href="https://www.franchise.com/franchises" class="back"><i class="back"></i><span>Franchises</span></a>
        </div>        <div class="desktop_nav">
            <ul><li class="dashboard">
                    <a href="https://www.franchise.com/dashboard">
                        
                        <span>Dashboard</span>
                    </a>
                    
                </li><li class="franchise">
                    <a href="https://www.franchise.com/franchise">
                        
                        <span>Franchises</span>
                    </a>
                    
                </li><li class="resources">
                    <a href="https://www.franchise.com/resources">
                        
                        <span>Resources</span>
                    </a>
                    
                </li><li class="tools">
                    <a href="javascript:;" onclick="menuToggle(&#39;top&#39;,&#39;tools&#39;);">
                        
                        <span>Tools</span>
                    </a>
                    <ul><li><a href="https://www.franchise.com/compare-results">Comparison Tool</a></li><li><a href="https://www.franchise.com/net-worth-calculator">Net Worth Calculator</a></li></ul>
                </li></ul>        </div>
    </div>
    <nav id="header_nav">
                        <div class="notifications_nav" data-count="6" onclick="window.location=&#39;/notifications&#39;">
                            <i class="bell"></i>
                        </div>
                        <div class="favorites_nav" data-count="7" onclick="window.location=&#39;/favorites&#39;;">
                            <i class="heart"></i>
                        </div>
                        <div onclick="menu_modal();"><div class="avatar">RN</div></div>
                    </nav></header>
<!-- HEADER END -->
<!-- MENU BAR START -->
<header id="bottom" class="hide_tablet">
    <ul><li class="dashboard">
                    <a href="https://www.franchise.com/dashboard">
                        <i class="dashboard"></i>
                        <span>Dashboard</span>
                    </a>
                    
                </li><li class="franchise">
                    <a href="https://www.franchise.com/franchise">
                        <i class="franchise"></i>
                        <span>Franchises</span>
                    </a>
                    
                </li><li class="resources">
                    <a href="https://www.franchise.com/resources">
                        <i class="resources"></i>
                        <span>Resources</span>
                    </a>
                    
                </li><li class="tools">
                    <a href="javascript:;" onclick="menuToggle(&#39;bottom&#39;,&#39;tools&#39;);">
                        <i class="tools"></i>
                        <span>Tools</span>
                    </a>
                    <ul><li><a href="https://www.franchise.com/compare-results">Comparison Tool</a></li><li><a href="https://www.franchise.com/net-worth-calculator">Net Worth Calculator</a></li></ul>
                </li></ul></header>
<!-- MENU BAR END -->
<!-- FOOTER START -->
<footer class="light_bg light_gray_border">
    <div class="container">
        <div class="columns">
                        <div class="tcol8 col6">
                <h3>Franchising Tools &amp; Resources</h3>
                <ul itemscope="" itemtype="http://www.schema.org/SiteNavigationElement">
                    <li>
                        <a href="https://www.franchise.com/franchise-glossary" class="link">Franchise Business Glossary</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/franchise-financing" class="link">Finance &amp; Start a Small Business</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/new-franchises-for-sale" class="link">New Franchises For Sale</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/business-franchising-events" class="link">Business &amp; Franchising Events</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/net-worth-calculator" class="link">Net Worth Calculator</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/frequently-asked-franchise-questions" class="link">Franchise FAQ's</a>
                    </li>
                </ul>
            </div>
            <div class="tcol8 col5">
                <h3>Company Information</h3>
                <ul itemscope="" itemtype="http://www.schema.org/SiteNavigationElement">
                    <li>
                        <a href="https://www.franchise.com/disclaimer" class="link">Disclaimer</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/privacy-statement" class="link">Privacy Policy</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/blog/" class="link">Franchise Blog</a>
                    </li>
                    <li>
                        <a href="https://www.franchiseinsights.com/" class="link" target="_blank">Franchising Insights</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/terms-of-use" class="link">Terms of Use</a>
                    </li>
                </ul>
            </div>
            <div class="tcol8 col5">
                <h3>Navigate</h3>
                <ul itemscope="" itemtype="http://www.schema.org/SiteNavigationElement">
                    <li>
                        <a href="https://www.franchise.com/about-us" class="link">About Us</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/contact-us" class="link">Contact Us</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/help-and-feedback" class="link">Help &amp; Feedback</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/sitemap" class="link">Sitemap</a>
                    </li>
                    <li>
                        <a href="https://www.franchise.com/subscribed" class="link">Subscribe</a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="light_gray_border disclaimer">
            Copyright © 2025 Franchise.com
        </div>
    </div>
</footer>

<div class="overlay" onclick="menu_modal(&#39;close&#39;);"></div>
<div id="menu_modal">
    <header class="menu_header light_bg">
                    <div class="avatar">RN</div>
                    <div class="name">Rakesh Nookala</div>
                    <div class="email"><EMAIL></div>
                </header>    <ul>
    <li class="dashboard">
                    <a href="https://www.franchise.com/dashboard">
                        <i class="dashboard"></i>
                        <span class="link">Dashboard</span>
                    </a>
                    
                </li><li class="franchise">
                    <a href="https://www.franchise.com/franchise">
                        <i class="franchise"></i>
                        <span class="link">Franchises</span>
                    </a>
                    
                </li><li class="resources">
                    <a href="https://www.franchise.com/resources">
                        <i class="resources"></i>
                        <span class="link">Resources</span>
                    </a>
                    
                </li><li class="compare-results">
                                    <a href="https://www.franchise.com/compare-results">
                                        <i class="compare-results"></i>
                                        <span class="link">Comparison Tool</span>
                                    </a>
                                </li><li class="net-worth-calculator">
                                    <a href="https://www.franchise.com/net-worth-calculator">
                                        <i class="net-worth-calculator"></i>
                                        <span class="link">Net Worth Calculator</span>
                                    </a>
                                </li>            <li><a href="https://www.franchise.com/saved"><svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M5 0v24l7-6 7 6v-24h-14zm1 1h12v20.827l-6-5.144-6 5.144v-20.827z"></path></svg><span class="link">Saved Articles</span></a></li>
        <li><a href="https://www.franchise.com/requests"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M17 3v-2c0-.552.447-1 1-1s1 .448 1 1v2c0 .552-.447 1-1 1s-1-.448-1-1zm-12 1c.553 0 1-.448 1-1v-2c0-.552-.447-1-1-1-.553 0-1 .448-1 1v2c0 .552.447 1 1 1zm13 13v-3h-1v4h3v-1h-2zm-5 .5c0 2.481 2.019 4.5 4.5 4.5s4.5-2.019 4.5-4.5-2.019-4.5-4.5-4.5-4.5 2.019-4.5 4.5zm11 0c0 3.59-2.91 6.5-6.5 6.5s-6.5-2.91-6.5-6.5 2.91-6.5 6.5-6.5 6.5 2.91 6.5 6.5zm-14.237 3.5h-7.763v-13h19v1.763c.727.33 1.399.757 2 1.268v-9.031h-3v1c0 1.316-1.278 2.339-2.658 1.894-.831-.268-1.342-1.111-1.342-1.984v-.91h-9v1c0 1.316-1.278 2.339-2.658 1.894-.831-.268-1.342-1.111-1.342-1.984v-.91h-3v21h11.031c-.511-.601-.938-1.273-1.268-2z"></path></svg><span class="link">Request History</span></a></li>
        <li><a href="https://www.franchise.com/recent"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M24 12c0 6.627-5.373 12-12 12s-12-5.373-12-12h2c0 5.514 4.486 10 10 10s10-4.486 10-10-4.486-10-10-10c-2.777 0-5.287 1.141-7.099 2.977l2.061 2.061-6.962 1.354 1.305-7.013 2.179 2.18c2.172-2.196 5.182-3.559 8.516-3.559 6.627 0 12 5.373 12 12zm-13-6v8h7v-2h-5v-6h-2z"></path></svg><span class="link">Recently Viewed</span></a></li>
        <li><a href="https://www.franchise.com/settings"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M24 14.187v-4.374c-2.148-.766-2.726-.802-3.027-1.529-.303-.729.083-1.169 1.059-3.223l-3.093-3.093c-2.026.963-2.488 1.364-3.224 1.059-.727-.302-.768-.889-1.527-3.027h-4.375c-.764 2.144-.8 2.725-1.529 3.027-.752.313-1.203-.1-3.223-1.059l-3.093 3.093c.977 2.055 1.362 2.493 1.059 3.224-.302.727-.881.764-3.027 1.528v4.375c2.139.76 2.725.8 3.027 1.528.304.734-.081 1.167-1.059 3.223l3.093 3.093c1.999-.95 2.47-1.373 3.223-1.059.728.302.764.88 1.529 3.027h4.374c.758-2.131.799-2.723 1.537-3.031.745-.308 1.186.099 3.215 1.062l3.093-3.093c-.975-2.05-1.362-2.492-1.059-3.223.3-.726.88-.763 3.027-1.528zm-4.875.764c-.577 1.394-.068 2.458.488 3.578l-1.084 1.084c-1.093-.543-2.161-1.076-3.573-.49-1.396.581-1.79 1.693-2.188 2.877h-1.534c-.398-1.185-.791-2.297-2.183-2.875-1.419-.588-2.507-.045-3.579.488l-1.083-1.084c.557-1.118 1.066-2.18.487-3.58-.579-1.391-1.691-1.784-2.876-2.182v-1.533c1.185-.398 2.297-.791 2.875-2.184.578-1.394.068-2.459-.488-3.579l1.084-1.084c1.082.538 2.162 1.077 3.58.488 1.392-.577 1.785-1.69 2.183-2.875h1.534c.398 1.185.792 2.297 2.184 2.875 1.419.588 2.506.045 3.579-.488l1.084 1.084c-.556 1.121-1.065 2.187-.488 3.58.577 1.391 1.689 1.784 2.875 2.183v1.534c-1.188.398-2.302.791-2.877 2.183zm-7.125-5.951c1.654 0 3 1.346 3 3s-1.346 3-3 3-3-1.346-3-3 1.346-3 3-3zm0-2c-2.762 0-5 2.238-5 5s2.238 5 5 5 5-2.238 5-5-2.238-5-5-5z"></path></svg><span class="link">Settings</span></a></li>
        <li><a href="https://www.franchise.com/logout.php"><svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 24 24"><path d="M16 2v7h-2v-5h-12v16h12v-5h2v7h-16v-20h16zm2 9v-4l6 5-6 5v-4h-10v-2h10z"></path></svg><span class="link">Log Out</span></a></li>
        </ul>
</div>
<div id="login_modal" class="login_modal modal" onclick="closeModalOutsideClick(&#39;login&#39;);">
    <div class="guts">
        <div class="content"><div class="login_screens">
    <div class="login_form_block">
        <form id="signin" method="post" action="https://www.franchise.com/franchise" class="ct-form login_forms">
            <input type="hidden" name="login_page" value="">
            <input type="hidden" name="is_page" value="0">
            <input type="hidden" name="fbo_id" value="">
            <input type="hidden" name="target" value="">
            <input type="hidden" name="dashboard_open" value="">
            <div class="columns">
                <div class="tcol16 login_display">
                    <h3 id="login_h3">Login to Your Account</h3>
                    <p id="login_subtitle" class="text-center hide"></p>
                    <div id="signin_step1" class="login_forms_inputs ">
                        <div class="input_wrapper">
                            <label for="login_email">Email <span>(Required)</span></label>
                            <input id="login_email" type="email" class="form-control " name="email" placeholder="Email" autocomplete="off" required="" autofocus="">
                            <div class="error hide"></div>
                        </div>
                        
                        <div class="input_wrapper login_btns">
                            <button type="button" id="signup_1" class="btn button solid w-100" onclick="signIn(event,1);">Next</button>
                        </div>
                        <div class="login_or_div input_wrapper">
                            <p class="login_or" style="text-align: center;font-weight: 600;">OR</p>
                        </div>
                        <div class="input_wrapper social-login login_btns">
                            <div class="btn google_btn">
                                <!-- Google Sign-In Button -->
                            </div>
                        </div>
                                                    <div class="input_wrapper text-center login_switch_links">New User? <a href="javascript:void(0);" data-target="login" onclick="toggleLogins(this,&#39;signup&#39;);">Click here to Sign Up.</a></div>
                                            </div>
                    <div id="signin_step2" class="login_forms_inputs hide">
                        <div class="input_wrapper mb-1"> 
                            <label for="login_password">Password <span>(Required)</span></label>
                            <div class="login_password_block">
                                <input id="login_password" type="password" class="form-control" name="password" placeholder="Password" autocomplete="off" required="" autofocus="">
                                <i class="eyeclose" onclick="togglePasswordView(this,&#39;password&#39;);"></i>
                            </div>
                            <div class="error hide"></div>
                        </div>
                        <div class="input_wrapper">
                                                            <span>Don’t remember your password?<a class="login_link" href="javascript:void(0);" onclick="toggleLogins(this,&#39;forgot_password&#39;);"> You can reset it here</a></span>
                                                    </div>
                        <div class="input_wrapper login_btns">
                            <button type="button" class="btn button solid w-100" onclick="signIn(event,2);">Login</button>
                        </div>
                    </div>
                                    </div>
            </div>
        </form>
    </div>
</div></div>
        <div class="close_modal" onclick="closeModal(&#39;login&#39;);">x</div>
    </div>
</div>
<div id="signup_modal" class="login_modal modal" onclick="closeModalOutsideClick(&#39;signup&#39;);">
    <div class="guts">
        <div class="content"><div class="login_screens">
    <div class="login_form_block">
        <form id="signup" method="post" action="https://www.franchise.com/signup" class="ct-form login_forms">
            <input type="hidden" name="login_page" value="">
            <input type="hidden" name="is_page" value="0">
            <input type="hidden" name="fbo_id" value="">
            <input type="hidden" name="target" value="">
            <input type="hidden" name="login_type" value="">
            <input type="hidden" name="social_api_response" value="">
            <input type="hidden" name="dashboard_open" value="">
            <div class="columns">
                <div class="tcol16 login_display">
                    <h3 id="signup_h3">Sign Up and Start Your Free Account Today</h3>
                    <p id="signup_subtitle" class="text-center hide"></p>
                    <div id="signup_step1" class="login_forms_inputs ">
                        <div class="input_wrapper">
                            <label for="signup_email">Email *</label>
                            <input id="signup_email" type="email" class="form-control " name="email" placeholder="Email" onblur="this.value = this.value.trim();" required="" autofocus="">
                            <div class="error hide"></div>
                        </div>
                        <div class="input_wrapper login_btns">
                            <button type="button" class="btn button solid w-100" onclick="signUp(event,1);">Next</button>
                        </div>
                        <div class="login_or_div input_wrapper">
                            <p class="login_or" style="text-align: center;font-weight: 600;">OR</p>
                        </div>
                        <div class="input_wrapper social-login login_btns">
                            <div class="btn google_btn">
                                <!-- Google Sign-In Button -->
                            </div>
                        </div>
                                                    <div class="input_wrapper text-center login_switch_links">Already have an account? <a href="javascript:void(0);" data-target="signup" onclick="toggleLogins(this,&#39;login&#39;);">Sign In.</a></div>
                                            </div>
                    <div id="signup_step2" class="login_forms_inputs hide">
                        <div class="input_wrapper">
                            <label for="signup_password">Password *</label>
                            <div class="login_password_block">
                                <input id="signup_password" type="password" class="form-control" name="create_password" autocomplete="off" placeholder="Password" required="">
                                <i class="eyeclose" onclick="togglePasswordView(this,&#39;create_password&#39;);"></i>
                            </div>
                            <div class="error hide"></div>
                        </div>
                        <div class="input_wrapper">
                            <label for="signup_cnf_password">Re-enter Password *</label>
                            <div class="login_password_block">
                                <input id="signup_cnf_password" type="password" class="form-control" name="confirm_password" autocomplete="off" placeholder="Confirm Password" required="">
                                <i class="eyeclose" onclick="togglePasswordView(this,&#39;confirm_password&#39;);"></i>
                            </div>
                            <div class="error hide"></div>
                        </div>
                        <div class="input_wrapper login_btns">
                            <button type="button" class="btn button light signup_btn" onclick="signUpBack(1);">Back</button>
                            <button type="button" id="step2_signup_btn" class="btn button solid" onclick="signUp(event,2);">Next</button>
                        </div>
                    </div>
                    <div id="signup_step3" class="login_forms_inputs hide">
                                                <input type="hidden" name="login_page" value="">
                        <input type="hidden" name="password" value="">
                        <div class="input_wrapper">
                            <label for="signup_fname">First Name *</label>
                            <input id="signup_fname" type="text" class="form-control" name="first_name" placeholder="First Name" onblur="this.value = this.value.trim();" oninput="this.value = this.value.replace(/[^a-zA-Z ]/g, &#39;&#39;)" autocomplete="off" required="" autofocus="">
                            <div class="error hide"></div>
                        </div>
                        <div class="input_wrapper">
                            <label for="signup_lname">Last Name *</label>
                            <input id="signup_lname" type="text" class="form-control" name="last_name" placeholder="Last Name" onblur="this.value = this.value.trim();" oninput="this.value = this.value.replace(/[^a-zA-Z ]/g, &#39;&#39;)" autocomplete="off" required="">
                            <div class="error hide"></div>
                        </div>
                        <div class="input_wrapper">
                            <label for="signup_zipcode">Zipcode *</label>
                            <input id="signup_zipcode" type="text" inputmode="numeric" class="form-control" name="zipcode" placeholder="Zipcode" onblur="this.value = this.value.trim();" oninput="this.value = this.value.replace(/[^0-9]/g, &#39;&#39;).slice(0, 5);getzipinfo(this.value,&#39;signup&#39;);" autocomplete="off" required="">
                            <div class="error hide"></div>
                        </div>
                        <div class="select_wrapper">
                            <label for="signup_state">Desired Franchise Location</label>
                            <div class="row_item">
                            <select id="signup_state" name="state"><option value="AL">Alabama</option><option value="AK">Alaska</option><option value="AZ">Arizona</option><option value="AR">Arkansas</option><option value="CA">California</option><option value="CO">Colorado</option><option value="CT">Connecticut</option><option value="DE">Delaware</option><option value="DC">District of Columbia</option><option value="FL">Florida</option><option value="GA">Georgia</option><option value="HI">Hawaii</option><option value="ID">Idaho</option><option value="IL">Illinois</option><option value="IN">Indiana</option><option value="IA">Iowa</option><option value="KS">Kansas</option><option value="KY">Kentucky</option><option value="LA">Louisiana</option><option value="ME">Maine</option><option value="MD">Maryland</option><option value="MA">Massachusetts</option><option value="MI">Michigan</option><option value="MN">Minnesota</option><option value="MS">Mississippi</option><option value="MO">Missouri</option><option value="MT">Montana</option><option value="NE">Nebraska</option><option value="NV">Nevada</option><option value="NH">New Hampshire</option><option value="NJ">New Jersey</option><option value="NM">New Mexico</option><option value="NY">New York</option><option value="NC">North Carolina</option><option value="ND">North Dakota</option><option value="OH">Ohio</option><option value="OK">Oklahoma</option><option value="OR">Oregon</option><option value="PA">Pennsylvania</option><option value="RI">Rhode Island</option><option value="SC">South Carolina</option><option value="SD">South Dakota</option><option value="TN">Tennessee</option><option value="TX">Texas</option><option value="UT">Utah</option><option value="VT">Vermont</option><option value="VA">Virginia</option><option value="WA">Washington</option><option value="WV">West Virginia</option><option value="WI">Wisconsin</option><option value="WY">Wyoming</option></select>                                <div class="error hide"></div>
                            </div>
                        </div>
                        <div class="input_wrapper">
                            <div class="accept_box">
                                <input id="terms_of_use" type="checkbox" class="form-control" name="terms_of_use" required="" checked="">
                                <label for="terms_of_use">I Agree to the <a href="https://www.franchise.com/terms-of-use" target="_blank">Terms of Use</a></label>
                            </div>
                            <div class="error hide"></div>
                        </div>
                        <div class="input_wrapper login_btns">
                            <button type="button" class="btn button light signup_btn" onclick="signUpBack(2);">Back</button>
                            <button type="button" id="step3_signup_btn" class="btn button solid" onclick="signUp(event,3);">Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div></div>
        <div class="close_modal" onclick="closeModal(&#39;signup&#39;);">x</div>
    </div>
</div>
<div id="forgot_password_modal" class="login_modal modal" onclick="closeModalOutsideClick(&#39;forgot_password&#39;);">
    <div class="guts">
        <div class="content"><div class="columns login_screens">
    <div class="tcol16 ">
        <form id="forgot_password" method="post" action="https://www.franchise.com/dashboard" class="ct-form login_forms">
            <input type="hidden" id="login_contact_id" data-key="login_contact_id" value="29820">
            <input type="hidden" name="target" value="">
            <input type="hidden" name="fbo_id" value="">
            <div class="columns">
                <div class="tcol16 login_display">
                                            <h3>Forgot Your Password ?</h3>
                        <p>Enter your email address and a password reset email will be sent to your registered email address.</p>
                        <br>
                        <div id="forgot_content" class="login_forms_inputs">
                            <div class="input_wrapper">
                                <label for="reset_email">Email <span>(Required)</span></label>
                                <input id="reset_email" type="email" class="form-control" name="email" placeholder="Email" autocomplete="off" required="" autofocus="">
                                <div class="error hide"></div>
                            </div>
                            <div id="pwd_success_msg" class="success_bg hide"></div>
                            <div class="input_wrapper login_btns">
                                <button type="button" class="button solid" onclick="forgetPassword();">Submit</button>
                            </div>
                        </div>
                                    </div>
            </div>
        </form>
    </div>
</div></div>
        <div class="close_modal" onclick="closeModal(&#39;forgot_password&#39;);">x</div>
    </div>
</div>

<div id="request_modal" class="request_modal modal" onclick="closeModalOutsideClick(&#39;request&#39;);">
    <div class="guts"><div class="content"><h2 class="desktop_nomargin text_center">Submission modal coming soon</h2></div></div>
    <div class="close_modal" onclick="closeModal(&#39;request&#39;);">x</div>
</div>

<div id="survey_modal" class="modal" onclick="closeModalOutsideClick(&#39;quiz&#39;);">
    <div class="guts">
        <div class="close_modal" onclick="modal(&#39;survey_modal&#39;,&#39;close&#39;);">×</div>
        <div class="content"></div>
    </div>
</div>

<div id="quiz_modal" class="modal" onclick="closeModalOutsideClick(&#39;quiz&#39;);">
    <div class="guts">
        <div class="close_modal" onclick="modal(&#39;quiz_modal&#39;,&#39;close&#39;);">×</div>
        <div class="content"></div>
    </div>
</div>

<div id="alert">
    <div class="alert">
        <div class="content text_center"></div>
    </div>
</div>

<div id="loading">
    <div class="loader"></div>
</div>

<div id="notification_modal" class="notification modal" onclick="closeModalOutsideClick(&#39;notification&#39;);">
    <div class="guts">
        <div class="content"></div>
        <button class="close_modal" onclick="modal(&#39;notification_modal&#39;,&#39;close&#39;);">×</button>
    </div>
</div>

<!-- FOOTER END -->
<!-- CSS START -->
<link rel="stylesheet" href="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/css2" media="screen and (min-width: 768px)">
<!-- CSS END-->
<!-- JAVASCRIPT START -->
<!-- <script src="https://accounts.google.com/gsi/client" async defer></script>
<script type="text/javascript" src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"></script> -->
<!-- JAVASCRIPT END -->
    <script>const page = "profile";</script>

<script>
    const login_contact_id = "29820";
    const logged_in = "1";
    const login_id = "29820";
</script>

    <script>// Lazy Load
!function(t,e){"object"==typeof exports?module.exports=e(t):"function"==typeof define&&define.amd?define([],e):t.LazyLoad=e(t)}("undefined"!=typeof global?global:this.window||this.global,function(t){"use strict";function e(t,e){this.settings=s(r,e||{}),this.images=t||document.querySelectorAll(this.settings.selector),this.observer=null,this.init()}"function"==typeof define&&define.amd&&(t=window);const r={src:"data-src",srcset:"data-srcset",selector:".lazyload",root:null,rootMargin:"0px",threshold:0},s=function(){let t={},e=!1,r=0,o=arguments.length;"[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(e=arguments[0],r++);for(;r<o;r++)!function(r){for(let o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e&&"[object Object]"===Object.prototype.toString.call(r[o])?t[o]=s(!0,t[o],r[o]):t[o]=r[o])}(arguments[r]);return t};if(e.prototype={init:function(){if(!t.IntersectionObserver)return void this.loadImages();let e=this,r={root:this.settings.root,rootMargin:this.settings.rootMargin,threshold:[this.settings.threshold]};this.observer=new IntersectionObserver(function(t){Array.prototype.forEach.call(t,function(t){if(t.isIntersecting){e.observer.unobserve(t.target);let r=t.target.getAttribute(e.settings.src),s=t.target.getAttribute(e.settings.srcset);"img"===t.target.tagName.toLowerCase()?(r&&(t.target.src=r),s&&(t.target.srcset=s)):t.target.style.backgroundImage="url("+r+")"}})},r),Array.prototype.forEach.call(this.images,function(t){e.observer.observe(t)})},loadAndDestroy:function(){this.settings&&(this.loadImages(),this.destroy())},loadImages:function(){if(!this.settings)return;let t=this;Array.prototype.forEach.call(this.images,function(e){let r=e.getAttribute(t.settings.src),s=e.getAttribute(t.settings.srcset);"img"===e.tagName.toLowerCase()?(r&&(e.src=r),s&&(e.srcset=s)):e.style.backgroundImage="url('"+r+"')"})},destroy:function(){this.settings&&(this.observer.disconnect(),this.settings=null)}},t.lazyload=function(t,r){return new e(t,r)},t.jQuery){const r=t.jQuery;r.fn.lazyload=function(t){return t=t||{},t.attribute=t.attribute||"data-src",new e(r.makeArray(this),t),this}}return e});

// OnLoad Functions
window.addEventListener('load', function() {
    lazyload();
    if (!['login','signup','forgot_password'].includes(page)) {
        // Load Favorites
        load_favorites();
        // Load Notification count
        loadNotificationCount(); 
    }

    if (page == 'favorites') {
        getFavConcepts();
    }

    if (['login','signup'].includes(page)) {
        // Load external files only on login and signup
        setTimeout(()=>{
            // adding social buttons dynamically
            addSocialButtons('.login_page',page,1);
            loadExternalLoginScripts();
        },100)
    }

    if (page == 'signup') {
        // trigger signup_1 on signup page on step 1
        ga4_global_events('signup_1');
    }

    //load survey modal
    const franchise_params = new URLSearchParams(window.location.search);
    if(!franchise_params.has('compare')){
        load_survey();
    }

    if (page == 'dashboard') {
        const dashboard_params = new URLSearchParams(window.location.search);
        if (dashboard_params.has('dashboard_open')) {
            modal(dashboard_params.get('dashboard_open')+'_modal');
            // Remove query string from URL (without reload)
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }

    async function getExternalCompareCookie() {
        const externalCompareCookie = getCookie('external_compare');
        let compareArray = [];

        try {
            if (externalCompareCookie) {
                compareArray = JSON.parse(externalCompareCookie);
                const url = absolute_path + '/compare_activity.json';
                const response = await fetch(url, { method: 'GET' });
                const data = await response.json();
                var tLength = data.length;
                const franchise_params_outer = new URLSearchParams(window.location.search);
                if (compareArray.length > 0 && tLength < 5) {
                    document.getElementById('loading').classList.add('show');
                    for (const id of compareArray) {
                        if (data.includes(id)) {
                            continue; // already in data, skip
                        }
                        if (tLength >= 5) {
                            break; // stop adding if max limit reached
                        }

                        await addCompareSelect(id); // add item
                        tLength++; // update count after adding
                        // console.log(id);
                    }
                    // Clear the cookie after processing
                    document.cookie = "external_compare=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                    document.getElementById('loading').classList.remove('show');
                    if (franchise_params_outer.has('compare') && franchise_params_outer.get('compare') == 'yes') {
                        window.location.href = '/compare-results';
                    }
                }else if(compareArray.length > 0){
                    document.cookie = "external_compare=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
                    if (franchise_params_outer.has('compare') && franchise_params_outer.get('compare') == 'yes') {
                        window.location.href = '/compare-results';
                    }
                }
            }
        } catch (e) {
            console.error('Invalid cookie JSON:', e);
        }
    }

    async function initComparePage() {
        // when non logged in user performs compare activity using ?compare=yes in url
        // we save the locaaly stored compare concepts to activity table upon login and delete local cookie
        if (login_contact_id) {
            await getExternalCompareCookie();  // ensures items are added first
        }
    }
    initComparePage();
    if (page == 'franchise') {
        // check if compare = yes is passed in url and call toggle compare functionality
        const franchise_params = new URLSearchParams(window.location.search);
        if (franchise_params.has('compare') && franchise_params.get('compare') == 'yes') {
            const compare_check_fran = document.getElementById('compare_check');
            if (compare_check_fran) {
                // when user added concepts to compare without login and when they refresh load back the toggled concepts
                toggle_compare(false);
                if (!login_contact_id) {
                    loadExternalCompareData();
                }
            }
        }else{
            if (login_contact_id) {
                toggle_compare(true);
            }
        }
    }

    saveActivityCookies();
});

document.addEventListener('click', function(event) {
    const openItems = document.querySelectorAll('.desktop_nav li.open, #bottom li.open');
  
    openItems.forEach(parentItem => {
      if (!parentItem.contains(event.target)) {
        parentItem.classList.remove('open');
      }
    });
});

function saveActivityCookies() {
    let login_fav_fboid = getCookie('login_fav_fboid');
    if(login_fav_fboid){
        favorites(login_fav_fboid);
        delete_cookie('login_fav_fboid');
    }

    let login_articleid = getCookie('login_articleid');
    if(login_articleid){
        favorites(login_articleid,true);
        delete_cookie('login_articleid');
    }
}

//to submit the form when a user clicks the enter button on the subscribe form
if (!window.newsletterEnterListenerAdded) {
    document.addEventListener('keydown', function (event) {
        if (event.key === 'Enter') {
            const activeElement = document.activeElement;
            const subscribeForm = document.querySelector('#subscribe_form');
            if (activeElement && subscribeForm && subscribeForm.contains(activeElement)) {
                event.preventDefault();
                
                const nameInput = document.getElementById('subscribe_name');
                const emailInput = document.getElementById('newsletter_email');

                // validation check
                if (nameInput.checkValidity() && emailInput.checkValidity()) {
                    subscribeForm.submit();
                } else {
                    nameInput.reportValidity();
                    emailInput.reportValidity();
                }
            }
        }
    });
    window.newsletterEnterListenerAdded = true;
}

// Request Information
function request_modal(fbo_id,investment,name) {
    document.querySelector('#request_modal').classList.add('show');
    // console.log('clicked');
}

var absolute_path = 'https://'+window.location.hostname,
    title_append = ' | My Account',
    current_location = window.location.href;

//Get Cookie
function getCookie(name) {
    var v = document.cookie.match('(^|;) ?' + name + '=([^;]*)(;|$)');
    return v ? v[2] : null;
}

//Set Cookie
function setCookie(key,value,days=1){
    var date = new Date();
    date.setTime(date.getTime()+(days*24*60*60*1000));
    var expires = "; expires="+date.toGMTString();
    document.cookie = key+"="+value+expires+"; path=/";
}

function delete_cookie(name) {
    document.cookie = name +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

function showTooltip(evt, text) {
    let tooltip = document.getElementById("tooltip");
    tooltip.innerHTML = text;
    tooltip.style.display = "block";
    tooltip.style.left = evt.pageX + 10 + 'px';
    tooltip.style.top = evt.pageY + 10 + 'px';
}
  
function hideTooltip(id=null) {
    var tid = id ? id : 'tooltip';
    var tooltip = document.getElementById(tid);
    tooltip.style.display = "none";
}

//Resort Sales Index
function salesTabActive(data_type){
    var btnContainer = document.getElementById("sort-results");
    var btns = btnContainer.getElementsByClassName("si-tab");
    var container = document.getElementById("si-results-row");
    var classname = document.getElementsByClassName('si-results-item');
    var divs = [];
    
    for (var i = 0; i < btns.length; i++) {
        btns[i].classList.remove("active");
    }
    document.getElementById("sort_"+data_type).classList.add("active");

    // console.log(data_type);
    for (var i = 0; i < classname.length; ++i) {
        divs.push(classname[i]);
    }
    container.innerHTML='';    
    container.innerHTML=cssSkeletonLoader(6,'listing');

    divs.sort(function(a, b) {
        return a.getAttribute("data-"+data_type).localeCompare(b.getAttribute("data-"+data_type));
    });
    
    var br = '';
    setTimeout(() => {
        container.innerHTML=''
        divs.forEach(function(el) {
        container.appendChild(el);
    });
    }, 500);
    
}

function cssSkeletonLoader(val, type = "listing") {
    let skeletonHTML = '';
    for (let index = 0; index < val; index++) {
        if (type === "listing") {
            skeletonHTML += `
                <li class="skeleton-item">
                    <article class="white_shell">
                        <div class="skeleton-image"></div>
                        <div class="wrap">
                            <div class="details">
                                <div class="skeletion-heading"></div>
                                <div class="skeleton-text"></div>
                                <div class="skeleton-text"></div>
                                <div class="skeleton-text"></div>
                                <div class="skeleton-text"></div>
                            </div>
                        </div>
                    </article>
                </li>`;
        } else if(type=='modal'){
            skeletonHTML += `
                    <div class="skeleton-modal" >
                        <div class="skeletion-heading"></div>
                        <div class="skeleton-text"></div>
                        <div class="skeleton-text"></div>
                        <div class="skeleton-text"></div>
                        <div class="skeleton-text"></div>
                        <div class="skeleton-text"></div>
                        <div class="skeleton-text"></div>
                    </div>`;
        }
    }

    return skeletonHTML;
}

function toggleSitemap(el){
    const id = el.getAttribute('data-type');
    var coll = document.getElementById(id).style.display;
    if(coll == 'block'){
        el.classList.remove('open');
        document.getElementById(id).style.display = 'none';
    }else{
        el.classList.add('open');
        document.getElementById(id).style.display = 'block';
    }
}

function loadExternalLoginScripts() {
    // Load Google login script
    const existingGoogle = document.querySelector('script[src="https://accounts.google.com/gsi/client"]');
    if (existingGoogle) existingGoogle.remove();
    const googleScript = document.createElement('script');
    googleScript.src = 'https://accounts.google.com/gsi/client';
    googleScript.async = true;
    googleScript.defer = true;
    document.body.appendChild(googleScript);

    // Load Apple login script
    // const existingApple = document.querySelector('script[src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js"]');
    // if (existingApple) existingApple.remove();
    // const appleScript = document.createElement('script');
    // appleScript.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
    // appleScript.onload = () => {
    //     console.log('Apple script loaded');
    //     initAppleLogin(); // initialize AFTER script is loaded
    // };
    // document.body.appendChild(appleScript);
}

function initAppleLogin() {
  // Wait for the modal and #appleid-signin to be visible
  const appleButton = document.getElementById('appleid-signin');
  if (window.AppleID && appleButton) {
    AppleID.auth.init({
        clientId: 'com.franchise.beta',  // Your app's client ID (Services ID)
        scope: 'name email',             // Specify the scope for the data you need
        redirectURI: absolute_path + '/callbackApple',  // The redirect URI after successful login
        state: 'state',
        usePopup: true
    });
  }
}

function openLoginModals(id) {
  const content = document.querySelector('#'+id+'_modal .content');

  if (!content.getAttribute('data-loaded')) {
    if (id == 'forgot_password') {
        id = 'forgot-password';
    }
    fetch('/pages/'+id+'.php')
      .then(res => res.text())
      .then(html => {
        content.innerHTML = html;
        content.setAttribute('data-loaded', 'true');
        // loading external files for social media signups
        setTimeout(() => {
            loadExternalLoginScripts();
        }, 200);
      })
      .catch(err => {
        loader.innerHTML = '<p>Error loading content.</p>';
      });
  }
}

function addSocialButtons(target,page_type,is_page=0) {
    const googleBtn = document.querySelector(`${target} .google_btn`);
    const appleBtn = document.querySelector(`${target} .apple_btn`);
    if (googleBtn) {
        googleBtn.innerHTML = `
            <div id="g_id_onload"
                data-client_id="87966341561-j7d5uufdc1i3b5dg4a8p2c038nlg84cd.apps.googleusercontent.com" 
                data-login_uri="`+absolute_path+`/google_login.json"
                data-callback="onSignIn"
                data-auto_prompt="false">
            </div>
            <div id="g_id_signin" class="g_id_signin" 
                data-logo_alignment="center"
                data-type="standard"
                data-shape="pill"
                data-theme="outline"
                data-width="400"
                data-height="40"
                data-text="continue_with"
                data-longtitle="true"
                data-page="`+is_page+`"
                data-pagetype="`+page_type+`"
                data-size="large"
                data-id="signin">
            </div>`;
    }

    if (appleBtn) {
        appleBtn.innerHTML = `
            <div id="appleid-signin" 
                data-color="black" 
                data-border="true" 
                data-type="sign in" 
                data-width="350" 
                data-height="40">
            </div>`;
    }
}

function toggleLogins(e, modal) {
    document.body.style.overflow = 'hidden';
    //this alert code is to handle when already user is logged in or signed up
    //when user is already signed up and try to signup again it will ask you to login, so onclicking on it the email will auto populate on login screen
    //so below is the code to pre poulate it and close the alert modal
    const alert = document.getElementById('alert');
    if (alert && alert.classList.contains('show')) {
        const emailFromAttr = e.getAttribute('data-value');
        if (emailFromAttr) {
            document.querySelector(`#${modal}_modal input[name=email]`).value=emailFromAttr;
            document.querySelector(`#${modal}_modal input[name=email]`).classList.add('readonly');
        }
        alert.classList.remove('show');
    }

    if (['login','signup'].includes(modal)) {
        document.querySelector('#'+modal+'_modal #'+modal+'_h3').classList.remove('submission');
        document.querySelector('#'+modal+'_modal #'+modal+'_subtitle').classList.add('hide');
    }
    // this data-target code is used to determine from where the login/signup modal got triggered
    const targetFromAttr = e.getAttribute('data-target');
    const target = ['favorites', 'compare', 'request', 'article','submission'].includes(targetFromAttr)
        ? targetFromAttr
        : document.querySelector(`#${modal}_modal input[name=target]`).value;

    //here we will get fbo_id or article id based on the target 
    const fbo_id = target == 'favorites' ? e.getAttribute('data-fboid') : getCookie('login_fav_fboid');
    const articleid = target == 'article' ? e.getAttribute('data-articleid') : getCookie('login_articleid');

    // Determine login text based on modal and target
    const loginTexts = {
        signup: {
            default: 'Sign Up and Start Your Free Account Today',
            favorites: 'Sign up to save this franchise',
            compare: 'Sign up to compare these franchises',
            article: 'Sign up to save this article',
            submission: 'Sign up to submit your franchise'
        },
        login: {
            default: 'Login to Your Account',
            favorites: 'Sign in to save this franchise',
            compare: 'Sign in to compare these franchises',
            article: 'Sign in to save this article',
            submission: 'Sign in to submit your franchise'
        },
        forgot_password: {
            default: 'Forgot Your Password ?',
        }
    };

    const loginText = loginTexts[modal]?.[target] || loginTexts[modal]?.default || '';

    // Hide all modals first
    ['login_modal', 'signup_modal', 'forgot_password_modal'].forEach(id => {
        const el = document.getElementById(id);
        if (el) el.classList.remove('show');
    });

    // Set target in the opposite modal (preserve for switching)
    const oppositeModal = modal === 'signup' ? 'login' : modal === 'login' ? 'signup' : null;
    if (oppositeModal) {
        const input = document.querySelector(`#${oppositeModal}_modal input[name=target]`);
        if (input) input.value = target;
    }

    if (fbo_id && !getCookie('login_fav_fboid')) {
        setCookie('login_fav_fboid', fbo_id);
    }

    if (articleid && !getCookie('login_articleid')) {
        setCookie('login_articleid', articleid);
    }

    // Show current modal
    const currentModal = document.getElementById(`${modal}_modal`);
    if (currentModal) currentModal.classList.add('show');
    if (modal == 'signup') {
        // trigger signup_1 on signup modal on step 1
        ga4_global_events('signup_1');
    }

    // Clear existing buttons
    ['google_btn'].forEach(cls => {
        const btns = document.querySelectorAll(`.${cls}`);
        btns.forEach(btn => btn.innerHTML = '');
    });

    // Set heading text
    const heading = currentModal.querySelector('h3');
    if (heading) heading.innerHTML = loginText;

    // Inject Google and Apple sign-in only if not forgot password
    if (modal != 'forgot_password') {
        addSocialButtons(`#${modal}_modal`,modal,0);
    }

    // Load external scripts (Google/Apple) with slight delay
    setTimeout(() => loadExternalLoginScripts(), 200);
}

function closeModal(id) {
    if(id=='survey'){
        closeSurveyModal();
    }
    if(page!='profile'){
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.value = '';
        });
    }
    document.body.style.overflow = '';
    document.getElementById(id+'_modal').classList.remove('show');
    if (id == 'login') {
        document.getElementById('signin_step1').classList.remove('hide');
        document.getElementById('signin_step2').classList.add('hide');
    }

    if (id == 'signup') {
        document.getElementById('signup_step1').classList.remove('hide');
        document.getElementById('signup_step2').classList.add('hide');
        document.getElementById('signup_step3').classList.add('hide');
    }
    let login_fav_fboid = getCookie('login_fav_fboid');
    if(login_fav_fboid){
        delete_cookie('login_fav_fboid');
    }
    let login_articleid = getCookie('login_articleid');
    if(login_articleid){
        delete_cookie('login_articleid');
    }
}

function closeModalOpened(modal=''){
    var openModal = document.querySelector('.modal.show');
    if (openModal) {
        openModal.classList.remove('show');
        if (modal == 'submission' && !login_contact_id) {
            if(document.getElementById('lead_step_1')){
                document.getElementById('lead_step_1').classList.remove('hide');
                document.getElementById('lead_step_2').classList.add('hide');
            }
        }
    }
}

function closeModalOutsideClick(id){
    const modal = document.getElementById(id+'_modal');
    if (modal) {
        const content = modal.querySelector('.guts');
        if (modal.classList.contains('show') && !content.contains(event.target)) {
            document.body.style.overflow = '';
            modal.classList.remove('show');

            if (id == 'login') {
                document.getElementById('signin_step1').classList.remove('hide');
                document.getElementById('signin_step2').classList.add('hide');
            }

            if (id == 'signup') {
                document.getElementById('signup_step1').classList.remove('hide');
                document.getElementById('signup_step2').classList.add('hide');
                document.getElementById('signup_step3').classList.add('hide');
            }

            let login_fav_fboid = getCookie('login_fav_fboid');
            if(login_fav_fboid){
                delete_cookie('login_fav_fboid');
            }

            let login_articleid = getCookie('login_articleid');
            if(login_articleid){
                delete_cookie('login_articleid');
            }
        }
    }
}

function formFieldsValidation(idF,text) {
    document.querySelector(idF).classList.add('error-outline');
    const input = document.querySelector(idF);
    const errorEl = input?.closest('.input_wrapper')?.querySelector('.error');

    if (errorEl && errorEl.classList.contains('error')) {
        errorEl.classList.remove('hide');
        errorEl.innerHTML = text;
    }
}

function addToExternalCompare(valueToAdd,type='add') {
    let existing = getCookie('external_compare');
    let arr = [];

    try {
        if (existing) arr = JSON.parse(existing);
    } catch (e) {
        console.warn('Corrupt cookie, resetting external_compare.');
    }

    if (type == 'remove') {
        if(arr.includes(valueToAdd)){
            arr = arr.filter(item => item !== valueToAdd);
        }
    }else{
        if (!arr.includes(valueToAdd)) {
            arr.push(valueToAdd);
        }
    }

    setCookie('external_compare', JSON.stringify(arr));
}

async function addCompareSelect(id,type='add'){
    if (login_contact_id) {
        var url = absolute_path+'/comparison.json?v='+id+'&t='+type;
        await fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }else{
        if (is_search_compare) {
           addToExternalCompare(id,type);
        }
    }
}

function loadNotificationCount() {
    setTimeout(function () {
        var url = absolute_path+'/notification_list.json?count=yes';
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            const nav = document.querySelector('.notifications_nav');
            if (nav) nav.setAttribute('data-count', data);
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }, 100);
}

function checkFavConcepts(favorites) {
    if (favorites && favorites.length > 0) {
        let query = '?per_page=999min=0&max=500000&net_worth_min=0&net_worth_max=2500000&units_min=0&units_max=8500&max_units=8500&max_net_worth=2500000&state_code=&idlist=';
        favorites.forEach(id => {
            query += id+','
        });

        query = query.replace(/,\s*$/, "");
        var url = absolute_path+'/concepts_count.json'+query;
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            const nav = document.querySelector('.favorites_nav');
            if (nav) nav.setAttribute('data-count', data.total);
            const dasboard_favorites = document.getElementById('dasboard_favorites');
            if (dasboard_favorites) dasboard_favorites.innerText = data.total;
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }else{
        const nav = document.querySelector('.favorites_nav');
        if (nav) nav.setAttribute('data-count', 0);
        const dasboard_favorites = document.getElementById('dasboard_favorites');
        if (dasboard_favorites) dasboard_favorites.innerText = 0;
    }
}

function getFavConcepts() {
    const favorites = JSON.parse(window.localStorage.getItem('favorites'));
    if (favorites && favorites.length > 0) {
        let query = '?per_page=999&basic=true&min=0&max=500000&net_worth_min=0&net_worth_max=2500000&units_min=0&units_max=8500&max_units=8500&max_net_worth=2500000&state_code=&idlist=';
        favorites.forEach(id => {
            query += id+','
        });

        query = query.replace(/,\s*$/, "");
        var url = absolute_path+'/concepts_count.json'+query;
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data) {
                const favArray = data['data'].map(item => ({
                    item_id: String(item.fbo_id),
                    item_name: item.name,
                    category: item.category
                }));
                ga4_global_events('view_favorite','items',favArray);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
}

// Check Favorites on Load
function load_favorites() {
    let count = 0;
    let favorites = [];
    const url = absolute_path + '/all_favorites.json';

    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        favorites = data;
        if (Object.keys(favorites).length > 0) {
            // Uncheck all favorite inputs
            document.querySelectorAll('input.favorites').forEach(input => {
                input.checked = false;
            });

            // Reset modal icons and text
            document.querySelectorAll('.modal_fav i').forEach(icon => {
                icon.classList.remove('checked');
            });
            document.querySelectorAll('.modal_fav span').forEach(span => {
                span.innerHTML = 'Add to Favorites';
            });

            // Store in localStorage
            window.localStorage.setItem('favorites', JSON.stringify(favorites[296]));
            window.localStorage.setItem('article_favorites', JSON.stringify(favorites[315]));

            // Update UI for favorites[296]
            favorites[296].forEach(id => {
                const inputs = document.querySelectorAll(`input[data-fboid="${id}"]`);
                if (inputs){
                    inputs.forEach(element => {
                        element.checked = true;
                    });
                } 

                const favIcon = document.querySelector(`#modal_fav_${id} i`);
                if (favIcon) favIcon.classList.add('checked');

                const favText = document.querySelector(`#modal_fav_${id} span`);
                if (favText) favText.innerHTML = 'Remove from Favorites';
            });

            // Update UI for article favorites[315]
            favorites[315].forEach(id => {
                const inputs = document.querySelectorAll(`input[data-articleid="${id}"]`);
                inputs.forEach(input => input.checked = true);
            });

            // Update count
            //checkFavConcepts(favorites[296]);
            const nav = document.querySelector('.favorites_nav');
            if (nav) nav.setAttribute('data-count', favorites.count);
            const dasboard_favorites = document.getElementById('dasboard_favorites');
            if (dasboard_favorites) dasboard_favorites.innerText = favorites.count;
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// Button Add/Remove Favorites
function favorites(fbo_id, article = false, login_contact='') {
    let favorites = [];
    let url = absolute_path + '/favorites.json?v=' + fbo_id + '&t=';

    if (login_contact) {
        url += '&login='+login_contact;
    }

    // Load from localStorage
    if (localStorage.getItem('favorites') != null) {
        favorites = JSON.parse(localStorage.getItem('favorites'));
    }

    if (article) {
        url = absolute_path + '/article_favorites.json?v=' + fbo_id + '&t=';
        if (localStorage.getItem('article_favorites')) {
            favorites = JSON.parse(localStorage.getItem('article_favorites'));
        }
    }
    var post_type = '';
    if (favorites) {
        if (!favorites.includes(fbo_id)) {
            post_type = 'add';
            url += post_type;
        } else {
            post_type = 'remove';
            url += post_type;
            const icon = document.querySelector(`#modal_fav_${fbo_id} i`);
            if (icon) {
                icon.classList.remove('checked');
            }
        }

        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (!article) {
                try {
                    const dataname = document.getElementById('fav_checkbox_'+fbo_id).getAttribute('data-name');
                    const datacategory = document.getElementById('fav_checkbox_'+fbo_id).getAttribute('data-category');
                    if (post_type == 'add') {
                        ga4_global_events('add_favorite','items',[{
                            item_id : String(fbo_id),
                            item_name : dataname,
                            category : datacategory
                        }]);
                    }else{
                        ga4_global_events('remove_favorite','items',[{
                            item_id : String(fbo_id),
                            item_name : dataname,
                            category : datacategory
                        }]);
                    }
                } catch (error) {
                    console.log(error);
                }
                
            }
            load_favorites();
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
}

function moreFavorite(id,concepts_id) {
    document.getElementById('more_modal_'+concepts_id).classList.toggle('hide');
    favorites(id);
}

function correctPwdPattern(e) {
    let passwordInput = e.value;
    let sanitizedValue = passwordInput.replace(/[^a-zA-Z0-9!@$]/g, '');
    return sanitizedValue;
}

function showSnackbar(id) {
  var x = document.getElementById(id);
  x.classList.add('show')
  setTimeout(function(){ x.className = x.className.replace(" show", ""); }, 3000);
}

function formatPhoneNumber(phoneNumberString) {
    var input = phoneNumberString.replace(/\D/g,'');
    var size = input.length;
    if (size>0) {input="("+input}
    if (size>3) {input=input.slice(0,4)+") "+input.slice(4,11)}
    if (size>6) {input=input.slice(0,9)+"-" +input.slice(9)}
    return input;
}

function formatNumber(numberString) {
    return numberString.replace(/\D/g,'');
}

function removeErrors(id, errors){
    if (errors) {
        for (let i = 0; i < errors.length; i++) {
            const element = errors[i];
            if(element=='comments' || element=='visitor_message'){
                document.querySelector('#'+id+' textarea[name='+element+']').classList.remove('error-outline');
                const nextElem = document.querySelector('#'+id+' textarea[name='+element+']').nextElementSibling;
                if (nextElem && nextElem.classList.contains('error')) {
                    nextElem.classList.add('hide');
                }
            }
            else{
                if (document.querySelector('#'+id+' input[name='+element+']:not([type=hidden])')) {
                    document.querySelector('#'+id+' input[name='+element+']:not([type=hidden])').classList.remove('error-outline');
                    const inputElem = document.querySelector('#'+id+' input[name='+element+']:not([type=hidden])');
                    if (inputElem) {
                        inputElem.classList.remove('error-outline');

                        const wrapper = inputElem.closest('.input_wrapper');
                        if (wrapper) {
                            const errorElem = wrapper.querySelector('.error');
                            if (errorElem) {
                                errorElem.classList.add('hide');
                            }
                        }
                    }
                }
            }   
        }
    }
}

async function zipcode_check(zip_code) {
    var url = absolute_path + '/zip.json?z=' + zip_code;
    const signupbtn = document.querySelector('.step2_signup_btn');
    document.getElementById('loading').classList.add('show');
    if (signupbtn) {
        signupbtn.classList.add('noevents');
    }

    try {
        const response = await fetch(url, { method: 'GET' });
        const data = await response.json();

        document.getElementById('loading').classList.remove('show');
        if (signupbtn) {
            signupbtn.classList.remove('noevents');
        }

        return data;

    } catch (error) {
        document.getElementById('loading').classList.remove('show');
        console.error('Error:', error);
        if (signupbtn) {
            signupbtn.classList.remove('noevents');
        }
        return null;
    }
}

function togglePasswordView(e,name){
    const passwordInput = document.querySelector('input[name="'+name+'"]');
    if (passwordInput.type == 'password') {
        passwordInput.type = 'text';
        e.classList.add('open');
    }else{
        passwordInput.type = 'password';
        e.classList.remove('open');
    }
}

// Menu Modal
function menu_modal(action = null) {
    const method = action === 'close' ? 'remove' : 'add';
    document.querySelectorAll('.overlay, #menu_modal').forEach(el => el.classList[method]('show'));
}


function modal(name,action = null){
    if(action == 'close'){
        if(document.body.style.overflow == 'hidden'){
            document.body.style.overflow = '';
        }
        if (name == 'survey_modal') {
            closeSurveyModal();
        }
        if(name=='quiz-modal'){
            unloadAssetsByTag('quiz-assets');
        }
        document.getElementById(name).classList.remove('show');
    }else{
        document.getElementById(name).classList.add('show');
    }
}

// fetch profiles for compare - this is in default because both franchises and compare results page use this
async function fetch_compare_img_data(img_ids, callback) {
    try {
        let query = '?per_page=999&basic=true&min=0&max=500000&net_worth_min=0&net_worth_max=2500000&units_min=0&units_max=8500&max_units=8500&max_net_worth=2500000&state_code=&comparison=true&conceptlist=';
        img_ids.forEach(id => {
            query += id+','
        });

        query = query.replace(/,\s*$/, "");
        var url = absolute_path+'/concepts_count.json'+query;
        fetch(url, {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data) {
                const results = data['data'];
                callback(results); // Return the results array
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
        //document.getElementById('loading').classList.remove('show');
       
    } catch (error) {
        console.error('Error fetching data:', error);
        document.getElementById('loading').classList.remove('show');
        return []; // Return an empty array or handle the error as needed
    }
}

async function fetch_compare_main_data(img_ids=null) {
    if (img_ids) {
        var ids = img_ids;
        var filter = '&get_image=true';
    }else{
        var ids = document.querySelectorAll('.cmp_ids');
        var filter = '';
    }
    var endpoints = [];
    // document.getElementById('loading').classList.add('show');
    ids.forEach(item => {
        if (img_ids) {
            var vl = item;
        }else{
            var vl = item.value;
        }
        endpoints.push(absolute_path+'/profile.json?id='+vl+filter);
    });

    try {
        const results = await Promise.all(
          endpoints.map(endpoint => fetch(endpoint).then(res => res.json()))
        );
        document.getElementById('loading').classList.remove('show');
        return results; // Return the results array
    } catch (error) {
        console.error('Error fetching data:', error);
        document.getElementById('loading').classList.remove('show');
        return []; // Return an empty array or handle the error as needed
    }
}

function isValidEmail(email) {
    const regex = /^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/;
    return regex.test(email);
}

function menuToggle(id, tab) {
    const menu = document.getElementById(id);
    if (!menu) return;

    const item = menu.querySelector(`li.${tab}`);
    if (item) {
        item.classList.toggle('open');
    }
}

// Button Add/Remove Favorites
function recentlyViewed(fbo_id) {
    let url = absolute_path + '/recently_viewed.json?v=' + fbo_id + '&t=add';

    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // 
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function goToURL(url,fbo_id=null) {
    const loader = document.getElementById('loading');
    if (loader) {
      loader.classList.add('show');
    }
  
    setTimeout(() => {
        if (fbo_id) {
            //recentlyViewed(fbo_id);
        }
        window.location.href = url;
    }, 100);
}

document.addEventListener('click', function(event) {
    var modal = event.target.closest('.modal');
    if (modal && !event.target.closest('.guts') && !['request_information','one_time_password_modal'].includes(modal.id)){
        if(modal.id === 'survey_modal') {
            closeSurveyModal();
        }
        modal.classList.remove('show');
    }
});

function closeSurveyModal() {
    document.body.style.overflow = '';
    unloadAssetsByTag('quiz-assets');
    let userId = localStorage.getItem('session_id');
    if (userId) {
         window.localStorage.setItem('survey', userId);
    }
}

// Lazy‑load a style sheet exactly once
function loadCss(url, tag = 'dynamic') {
  return new Promise((resolve, reject) => {
    if (document.querySelector(`link[href="${url}"]`)) return resolve();
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;
    link.setAttribute('data-tag', tag);
    link.onload = () => resolve();
    link.onerror = () => reject(`Failed to load CSS: ${url}`);
    document.head.appendChild(link);
  });
}

// Lazy‑load a script exactly once
function loadScript(url, tag = 'dynamic') {
  return new Promise((resolve, reject) => {
    if (document.querySelector(`script[src="${url}"]`)) return resolve();
    const script = document.createElement('script');
    script.src = url;
    script.setAttribute('data-tag', tag);
    script.onload = () => resolve();
    script.onerror = () => reject(`Failed to load JS: ${url}`);
    document.head.appendChild(script);
  });
}

//Load all assests for quiz
async function loadQuizAssets() {
  await loadCss('/css/quiz.css', 'quiz-assets');
  await loadCss('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css', 'quiz-assets');
  await loadScript('https://ajax.googleapis.com/ajax/libs/jquery/1.8.3/jquery.min.js', 'quiz-assets');
  await loadScript('https://code.jquery.com/ui/1.9.2/jquery-ui.js', 'quiz-assets');
  await loadScript('https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js', 'quiz-assets');
  await loadScript('/js/quiz.js', 'quiz-assets');
}

async function load_survey(quiz) {
    let userId=localStorage.getItem('session_id');
    let survey=localStorage.getItem('survey');
    if(quiz){
        if(survey){
            localStorage.removeItem('survey');
        }
    }
    if (!quiz && (userId == survey || survey == 1)) {
        document.getElementById('loading').classList.remove('show');
            return;
    }

    if(userId && userId != 'null' && userId != 'undefined') {
        try {
            document.getElementById('loading').classList.add('show');
            const response = await fetch(absolute_path + '/survey.json');
            const data = await response.json();
            if ((data.quiz_taken === 0 && data.count<6) || data.quiz_taken === 1) {
                await loadQuizAssets();
                const html = await fetch(absolute_path + '/quiz_survey.json?quiz_id=26&quiz_only=true').then(r => r.text());
                const modal = document.getElementById('survey_modal');
                const content = modal.querySelector('.content');
                content.innerHTML = html;
                runInlineScripts(content);
                document.body.style.overflow = 'hidden';
                let learnModel=document.getElementById('learn_modal')
                if(learnModel){
                    learnModel.classList.remove('show');
                }
                document.getElementById('loading').classList.remove('show');
                modal.classList.add('show');
                ga4_global_events('quiz_started');
            } 
            else {
                document.getElementById('loading').classList.remove('show');
                localStorage.setItem('survey', userId);
            }
        } 
        catch (err) {
            document.getElementById('loading').classList.remove('show');
            console.error('Error loading survey:', err);
        }
    }
}

//To run Inline JS when loaded as text
function runInlineScripts(container) {
  const scripts = container.querySelectorAll('script');
  scripts.forEach(script => {
    const newScript = document.createElement('script');
    if (script.src) {
      newScript.src = script.src;
    } else {
      newScript.textContent = script.textContent;
    }
    Array.from(script.attributes).forEach(attr =>
      newScript.setAttribute(attr.name, attr.value)
    );
    document.head.appendChild(newScript);
    script.remove();
  });
}

function unloadAssetsByTag(tag) {
  document.querySelectorAll(`[data-tag="${tag}"]`).forEach(el => el.remove());
}

async function submitNewsletter(){
    const name = document.getElementById('subscribe_name').value;
    const email = document.getElementById('newsletter_email').value;
    const url = absolute_path + '/validate_email.json?email=' + email;
    removeErrors('subscribe_form', ['subscribe_name', 'newsletter_email']);
    if (!name) {
        formFieldsValidation('#subscribe_form input[name=subscribe_name]', 'Name is required.');
    } else if (!email) {
        formFieldsValidation('#subscribe_form input[name=newsletter_email]', 'Email is required.');
    }else{
        document.getElementById('loading').classList.add('show');
        try {
            const response = await fetch(url);
            const data = await response.json();
            document.getElementById('loading').classList.remove('show');
            if (data == 0) {
                formFieldsValidation('#subscribe_form input[name=newsletter_email]', 'Not a valid email address');
            }else{
                document.getElementById('subscribe_form').submit();
            }
        } catch (error) {
            console.log(error);
        }
    }
}

function setAutoGenPassword(e) {
    toggleLogins(e,'signup');
    const email = e.getAttribute('data-value');
    document.querySelector('#signup input[name=login_type]').value = 'temporary';
    document.querySelector('#signup input[name=email]').value = email;
    document.getElementById('signup_step1').classList.add('hide');
    document.getElementById('signup_step2').classList.remove('hide');
    document.getElementById('step2_signup_btn').innerText = 'Submit';
}

function trackSurvey() {
    const url = absolute_path + '/track_survey.json?master_type=332&v=quiz';
    fetch(url)
        .then(response => response.json())
        .then(data => console.log(data))
        .catch(error => console.error('Error:', error));
        location.reload();
    
}

//GA4 DataLayer Push
function ga4_global_events(event,parameter = '',value = ''){
    if (typeof dataLayer !== 'undefined' && typeof login_id !== 'undefined' && typeof logged_in !=='undefined') {
        if(parameter == 'items'){
            dataLayer.push({
                items: undefined
            });
        }
        var dataLayer_array = {'event': event,'user_id': login_id};
        if(parameter != '' && value != ''){
            dataLayer_array[parameter] = value;
        }
        window.dataLayer = window.dataLayer || [];
        dataLayer.push(dataLayer_array);
    }
}document.addEventListener('DOMContentLoaded', function () {
    // Bing default
    bing_defaults();
    // loadExternalLoginScripts();
});

function validatePassword(password) {
    // Regular expression to check the conditions:
    // - At least one uppercase letter
    // - include at least one of the following characters !@$
    // - At least 7 characters long
    // const regex = /^(?=.*[A-Z])(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{7,}$/;
    const regex = /^(?=.*[A-Z])(?=.*[!@$]).{7,}$/;
    return regex.test(password);
}

function checkLoginUser(email, callback) {
    var url = absolute_path + '/check_email_exists.json?email=' + email + '&type=login';
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data.status);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                callback(data);
            } else {
                callback(data);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

function checkEmailExists(email, id, callback) {
    if (email) {
        if (id == 'signin') {
            var url = absolute_path + '/check_email_exists.json?email=' + encodeURIComponent(email) + '&type=login';
        } else {
            var url = absolute_path + '/check_email_exists.json?email=' + encodeURIComponent(email);
        }
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').classList.remove('show');
                if (data.status == 'error') {
                    // console.log(data.message);
                    document.querySelector('#' + id + ' input[name=email]').classList.remove('error-outline');
                    document.querySelector('#' + id + ' input[name=email]').nextElementSibling.classList.add('hide');
                }
                callback(data);
            })
            .catch(error => {
                document.getElementById('loading').classList.remove('show');
                console.error('Error:', error);
            });
    }
}

function login(email, password, callback) {
    var url = absolute_path + '/login.json?email=' + email + '&password=' + password;
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            if (data.status == 'success') {
                var timeNow = new Date().toLocaleString();
                setCookie('logged_in', timeNow, 365);
                ga4_global_events('user_login','user_status','logged_in');
                callback(data.status);
            } else {
                document.getElementById('loading').classList.remove('show');
                callback(data.status);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

function welcome_message() {
    var url = absolute_path + '/welcome_message.json';
    // document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.text())
        .then(data => {
            // console.log(data);
        })
        .catch(error => {
            // document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

//calling signin when a user clicks enter button while inside the form
if (!window.enterListenerAdded) {
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Enter') { //if the pressed key matches the Enter
            const activeElement = document.activeElement;
            if (activeElement && document.querySelector('#signin').contains(activeElement)) { //check what is focused element form or not
                event.preventDefault();

                // Determine which step is visible
                const step1 = document.querySelector('#signin_step1');

                if (!step1.classList.contains('hide') && step1.offsetParent !== null) {
                    signIn(event, 1);
                } else{
                    signIn(event, 2);
                }
            }
        }
    });
    window.enterListenerAdded = true; // mark as added so this block won't run again
}

function signUpUser(callback) {
    var url = absolute_path + '/signup.json';
    document.getElementById('loading').classList.add('show');
    var email = (document.querySelector('#signup input[name=email]').value).trim();
    var password = (document.querySelector('#signup input[name=password]').value).trim();
    var first_name = (document.querySelector('#signup input[name=first_name]').value).trim();
    var last_name = (document.querySelector('#signup input[name=last_name]').value).trim();
    var state = document.querySelector('#signup select[name=state]').value;
    var zipcode = (document.querySelector('#signup input[name=zipcode]').value).trim();
    var login_type = (document.querySelector('#signup input[name=login_type]').value).trim();
    var social_api_response = (document.querySelector('#signup input[name=social_api_response]').value).trim();
    var dataObj = {
        first_name: first_name,
        last_name: last_name,
        state: state,
        email: email,
        zipcode: zipcode, 
        password: atob(password),
        login_type:login_type,
        social_api_response:social_api_response
    }

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataObj)
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loading').classList.remove('show');
        if (data.status == 'success') {
            localStorage.setItem('session_id', data.data.id);
            ga4_global_events('signup_button');
            callback(data);
        } else {
            callback(data);
        }
    })
    .catch(error => {
        document.getElementById('loading').classList.remove('show');
        console.log('Error:', error);
    });
}

function signUpBack(step=null) {
    if (step == 1) {
        document.getElementById('signup_step1').classList.remove('hide');
        document.getElementById('signup_step2').classList.add('hide');
    }if (step == 2) {
        document.getElementById('signup_step2').classList.remove('hide');
        document.getElementById('signup_step3').classList.add('hide');
        const passwordInputs = document.querySelectorAll('input[type="password"]');
        passwordInputs.forEach(input => {
            input.value = '';
        });
    }
}

async function getzipinfo(zipcode, id) {
    if (zipcode.length == 5) {
        const zipobj = await zipcode_check(zipcode);
        const zipFieldName = (id == 'lead_step_2' ? 'zip_code' : 'zipcode');
        if (zipobj) {
            if (id == 'lead_step_2') {
                document.querySelector('#'+id+' select[name=state_code]').value = zipobj[0]['state'];
            }else{
                document.querySelector('#'+id+' select[name=state]').value = zipobj[0]['state'];
            }
            removeErrors(id, [zipFieldName]);
            return true; // success
        } else {
            formFieldsValidation('#'+id+' input[name='+zipFieldName+']', 'Zipcode is not valid.');
            return false; // failure
        }
    }else {
        return false; // not valid zip
    }
}

if (!window.signupEnterListenerAdded) {
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Enter') {
            const activeElement = document.activeElement;
            const signupForm = document.querySelector('#signup');
            if (activeElement && signupForm && signupForm.contains(activeElement)) {
                event.preventDefault();

                // Determine which step is visible
                const step1 = document.querySelector('#signup_step1');
                const step2 = document.querySelector('#signup_step2');
                const step3 = document.querySelector('#signup_step3');

                if (!step1.classList.contains('hide') && step1.offsetParent !== null) {
                    signUp(event, 1);
                } else if (!step2.classList.contains('hide') && step2.offsetParent !== null) {
                    signUp(event, 2);
                } else if (!step3.classList.contains('hide') && step3.offsetParent !== null) {
                    signUp(event, 3);
                }
            }
        }
    });
    window.signupEnterListenerAdded = true;
}

function signIn(e, step = null) {
    e.preventDefault();
    var is_page = document.querySelector('#signin input[name=is_page]').value;
    var dashboard_open = document.querySelector('#signin input[name=dashboard_open]').value;
    $query_url = '/dashboard';
    if (step == 1) {
        removeErrors('signin', ['email']);
        var email = document.querySelector('#signin input[name=email]').value;
        if (!email) {
            formFieldsValidation('#signin input[name=email]', 'Email is required.');
        }else if (!isValidEmail(email)) {
            formFieldsValidation('#signin input[name=email]', 'Email is not valid.');
        }else{
            checkEmailExists(email, 'signin', function (data) {
                if (data.status == 'success') {
                    if (data.data) {
                        localStorage.setItem('session_id', data.data[0].id);
                        if (data.data[0]['login_type'] == 'temporary') {
                            let signin_link = '';
                            if (is_page == 1) {
                                let link_para = '';
                                if (dashboard_open) {
                                    link_para = '&dashboard_open='+dashboard_open;
                                }
                                signin_link = `<a href="/signup?email=`+email+link_para+`" class="link link_blue">Sign Up</a>`;
                            }else{
                                signin_link = `<a href="javascript:void(0);" data-value="`+email+`" data-target="signup" onclick="setAutoGenPassword(this);" class="link link_blue">Sign Up</a>`;
                            }
                            var content = `<p>We found your account, but it looks like you haven't set a password yet. To continue, you will need to set one. Click here to `+signin_link+`</p>
                            <p class="text-center"><a href="javascript:void(0);" onclick="modal('alert','close')" class="button d-inline-block">Okay</a></p>`;
                            document.querySelector('#alert .content').innerHTML = content;
                            document.getElementById('alert').classList.add('show');
                        }else if(['google', 'apple'].includes(data.data[0].login_type)) {
                            var content = `<p>You've already logged in via ` + data.data[0].login_type + `. You can login via ` + data.data[0].login_type + ` again.</p>
                            <p class="text-center"><a href="javascript:void(0);" onclick="modal('alert','close')" class="button d-inline-block">Okay</a></p>`;
                            document.querySelector('#alert .content').innerHTML = content;
                            document.getElementById('alert').classList.add('show');
                        }else{
                            document.getElementById('signin_step1').classList.add('hide');
                            document.getElementById('signin_step2').classList.remove('hide');
                        }
                    }
                } else {
                    formFieldsValidation('#signin input[name=email]', 'Couldn\'t find your account, Sign Up to continue.');
                }
            })
        }
    }else if (step == 2){
        removeErrors('signin', ['password']);
        var email = document.querySelector('#signin input[name=email]').value;
        var password = document.querySelector('#signin input[name=password]').value;
        if (!password) {
            formFieldsValidation('#signin input[name=password]', 'Password is required');
        }else{
            login(email, password, function (status) {
                if (status == 'success') {
                    saveActivityCookies();
                    if (is_page == 1) {
                        if (dashboard_open) {
                            // setCookie('dashboard_open',dashboard_open);
                            $query_url += '?dashboard_open='+dashboard_open;
                        }
                        document.getElementById('signin').action = absolute_path + $query_url;
                    }else{
                        document.getElementById('signin').action = window.location.href;
                    }
                    document.getElementById('signin').submit();
                } else {
                    formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                }
            });
        }
    }
}

function signUpCreate() {
    var is_page = document.querySelector('#signup input[name=is_page]').value;
    var dashboard_open = document.querySelector('#signup input[name=dashboard_open]').value;
    signUpUser(function (data) {
        if (data.status == 'success') {
            // if (typeof dataLayer !== 'undefined') {
            //     window.dataLayer.push({
            //         'event': 'signup_button'
            //     });
            // }
            if (typeof fbq === "function") {
                fbq('track', 'CompleteRegistration');
            }
            saveActivityCookies();
            if (is_page == 1) {
                // redirect to intended page
                if (dashboard_open) {
                    // setCookie('dashboard_open',dashboard_open);
                    document.getElementById('signup').action = '/dashboard?dashboard_open='+dashboard_open;
                }else{
                    document.getElementById('signup').action = '/dashboard';
                }
            }else{
                closeModalOpened();
                document.getElementById('signup').action = window.location.href;
            }
            bing_event('signup');
            ga4_global_events('account_created','registrationMethod','registration now');
            ga4_global_events('user_login','user_status','logged_in');
            // welcome_message();
            document.getElementById('signup').submit();
            // const target = document.querySelector('#signup input[name=target]');
            // if (target && target.value != 'temporary_submit') {
            //     document.getElementById('signup').submit();
            // }
        } else {
            document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
            document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
        }
        // delete_cookie('beta_email');
        delete_cookie('beta_password');
    });
}

async function signUp(e, step = null) {
    e.preventDefault();
    var is_page = document.querySelector('#signup input[name=is_page]').value;
    if (step == 1) {
        removeErrors('signup', ['email']);
        var email = (document.querySelector('#signup input[name=email]').value).trim();
        if (!email) {
            formFieldsValidation('#signup input[name=email]', 'Email is required.');
        }else if (!isValidEmail(email)) {
            formFieldsValidation('#signup input[name=email]', 'Email is not valid.');
        }else {
            checkEmailExists(email, 'signup', function (data) {
                if (data.status == 'success') {
                    localStorage.setItem('session_id', data.data[0].id);
                    setCookie('beta_email', email);
                    document.getElementById('signup_step1').classList.add('hide');
                    document.getElementById('signup_step2').classList.remove('hide');
                    // trigger signup_2 on signup page on step 2
                    ga4_global_events('signup_2');
                } else {
                    if(data.data == "blacklisted"){         
                        formFieldsValidation('#signup input[name=email]','Not a valid email address');
                    }else{          
                        if (data.data[0]['login_type'] != 'temporary') {
                            let signin_link = '';
                            if (is_page == 1) {
                                signin_link = `<a href="/login?email=`+email+`" class="link link_blue">Sign In</a>`;
                            }else{
                                signin_link = `<a href="javascript:void(0);" data-value="`+email+`" data-target="login" onclick="toggleLogins(this,'login');" class="link link_blue">Sign In</a>`;
                            }
                            var content = `<p>Your account already exists, please `+signin_link+`.</p>
                                <p class="text-center"><a href="javascript:void(0);" onclick="modal('alert','close')" class="button d-inline-block">Okay</a></p>`;
                                document.querySelector('#alert .content').innerHTML = content;
                                document.getElementById('alert').classList.add('show');              

                        }else{
                            document.querySelector('#signup input[name=login_type]').value = data.data[0]['login_type'];
                            document.getElementById('signup_step1').classList.add('hide');
                            document.getElementById('signup_step2').classList.remove('hide');
                            if (data.data[0]['login_type'] == 'temporary') {
                                document.getElementById('step2_signup_btn').innerText = 'Set Password';
                            }
                            // trigger signup_2 on signup page on step 2
                            ga4_global_events('signup_2');
                        }
                        // formFieldsValidation('#signup input[name=email]',data.message);
                    }
                }
            });
        }
    }else if (step == 2) {
        removeErrors('signup', ['create_password', 'confirm_password']);
        var password = (document.querySelector('#signup input[name=create_password]').value).trim();
        var confirm_password = (document.querySelector('#signup input[name=confirm_password]').value).trim();
        var login_type = document.querySelector('#signup input[name=login_type]').value;
        var passwordValidate = validatePassword(password);
        if (!password) {
            formFieldsValidation('#signup input[name=create_password]', 'Password is required');
        } else if (!confirm_password) {
            formFieldsValidation('#signup input[name=confirm_password]', 'Confirm Password is required');
        } else if (!passwordValidate) {
            formFieldsValidation('#signup input[name=create_password]', 'Password must be At least one uppercase letter, include at least one of the following characters <strong>!@$</strong> and At least 7 characters long');
        } else if (password != confirm_password) {
            formFieldsValidation('#signup input[name=confirm_password]', 'Password & Confirm Password not match');
        } else {
            document.querySelector('#signup input[name=password]').value = btoa(password);
            if (login_type == 'temporary') {
                signUpCreate();
            }else{
                document.getElementById('signup_step2').classList.add('hide');
                document.getElementById('signup_step3').classList.remove('hide');
                // trigger signup_3 on signup page on step 3
                ga4_global_events('signup_3');
            }
        }
    } else if (step == 3) {
        removeErrors('signup', ['first_name', 'last_name', 'zipcode', 'terms_of_use']);
        var first_name = (document.querySelector('#signup input[name=first_name]').value).trim();
        var last_name = (document.querySelector('#signup input[name=last_name]').value).trim();
        var zipcode = (document.querySelector('#signup input[name=zipcode]').value).trim();
        var state = document.querySelector('#signup select[name=state]').value;
        var agree = document.querySelector('#signup input[name=terms_of_use]').checked;
        if (!first_name) {
            formFieldsValidation('#signup input[name=first_name]', 'First Name is required.');
        } else if (!last_name) {
            formFieldsValidation('#signup input[name=last_name]', 'Last Name is required.');
        } else if (!first_name && !last_name) {
            formFieldsValidation('#signup input[name=last_name]', 'First Name & Last Name is required.');
        } else if (!zipcode) {
            formFieldsValidation('#signup input[name=zipcode]', 'Zipcode is required.');
        } else if (zipcode.length != 5) {
            formFieldsValidation('#signup input[name=zipcode]', 'Zipcode is not valid.');
        } else if (!state) {
            formFieldsValidation('#signup select[name=state]', 'State is required.');
        } else if(!agree){
            formFieldsValidation('#signup input[name=terms_of_use]', 'Agree to Terms and conditions is required.');
        } else {
            if(zipcode){
                const valid_zip = await getzipinfo(zipcode, 'signup');
                if (!valid_zip) return; // STOP here if zipcode is invalid
            }
            signUpCreate();
        }
    }
}

function getQueryParam(param) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(param);
}

window.addEventListener('DOMContentLoaded', function () {
    if (document.getElementById('signup')) {
        if (document.querySelector('#signup input[name=email]')) {
            // First try URL param:
            var email = getQueryParam('email');
            // If not in URL, fallback to cookie:
            if (!email) {
                email = getCookie('beta_email');
            }

            if (email) {
                document.querySelector('#signup input[name=email]').value = email;
            }
        }
    }

    if (window.history.replaceState) {
        window.history.replaceState(null, null, window.location.href);
    }
});

function forgetPassword() {
    var email = document.querySelector('#forgot_password input[name=email]').value;
    var emailValidate = isValidEmail(email);
    if (!email) {
        formFieldsValidation('#forgot_password input[name=email]', 'Email is required.');
        return;
    }
    if(!emailValidate) {
        formFieldsValidation('#forgot_password input[name=email]', 'Email is not valid.');
        return;
    }
    document.getElementById('loading').classList.add('show');
    var url = absolute_path + '/forgot_password.json?email=' + email;
    // console.log(url);
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                closeModalOpened();
                var content = '<h3 class="text-center">Password Change Request</h3><p>Password reset mail has been sent to your mail address, please check and click on the reset link to reset your password.</p><div class="text-center"><button type="button" onclick="modal(\'alert\',\'close\')">Close</button></div>';
                document.querySelector('#alert .content').innerHTML = content;
                document.getElementById('alert').classList.add('show');
            } else {
                formFieldsValidation('#forgot_password input[name=email]', data.message);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}

function updatePassword() {
    removeErrors('update_password', ['update_password', 'update_password2']);

    var email = document.querySelector('#forgot_password input[name=email]').value;
    var token = document.querySelector('#forgot_password input[name=token]').value;
    var password = document.querySelector('#forgot_password input[name=update_password]').value;
    var confirm_password = document.querySelector('#forgot_password input[name=update_password2]').value;
    var passwordValidate = validatePassword(password);
    if (!password) {
        formFieldsValidation('#forgot_password input[name=update_password]', 'Password is required');
    } else if (!confirm_password) {
        formFieldsValidation('#forgot_password input[name=update_password2]', 'Confirm Password is required');
    } else if (!passwordValidate) {
        formFieldsValidation('#forgot_password input[name=update_password]', 'Password must be At least one uppercase letter, include at least one of the following characters <strong>!@$</strong> and At least 7 characters long');
    } else if (password != confirm_password) {
        formFieldsValidation('#forgot_password input[name=update_password2]', 'Password & Confirm Password not match');
    } else {
        document.getElementById('loading').classList.add('show');
        var url = absolute_path + '/update_password.json';
        var dataObj = {
            email: email,
            password: password,
            token: token
        }
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dataObj)
        })
            .then(response => response.json())
            .then(data => {
                // console.log(data);
                document.getElementById('loading').classList.remove('show');
                if (data.status == 'success') {
                    var content = '<h3 class="text-center">Password Updated</h3><p>Your password has been successfully updated, please click on below Login button to go to login page.</p><div class="text-center"><button type="button" onclick="window.location.href=\'/login\'">Login</button></div>';
                    document.querySelector('#alert .content').innerHTML = content;
                    document.getElementById('alert').classList.add('show');
                } else {
                    formFieldsValidation('#forgot_password input[name=update_password2]', data.message);
                }
            })
            .catch(error => {
                document.getElementById('loading').classList.remove('show');
                console.error('Error:', error);
            });
    }
}

document.addEventListener('DOMContentLoaded', (event) => {
    const form = document.querySelector('.login_forms');
    if (form) {
        form.addEventListener('keypress', function (event) {
            // Check if the pressed key is Enter
            if (event.key === 'Enter') {
                event.preventDefault(); // Prevent the form submission
            }
        });
    }
});

//Bing Defaults
function bing_defaults() {
    window.uetq = window.uetq || [];
    if (typeof uetq !== 'undefined') {
        window.uetq.push('set', {
            'pid': {
                'em': getCookie('email') ? getCookie('email') : '',
                'ph': getCookie('phone') ? getCookie('phone') : ''
            }
        });
    }
}

//Bing Events (single item)
function bing_event(event, price = null) {
    if (typeof uetq !== 'undefined') {
        if (event == 'purchase') {
            window.uetq.push('event', '', { 'revenue_value': price, 'currency': 'USD' });
        } else {
            window.uetq.push('event', event, {});

        }
    }
}
function checkEmailExistsForSocialLogin(email, id, callback) {
    if (email) {
        var url = absolute_path + '/check_email_exists.json?email=' + encodeURIComponent(email) + '&type=login';
        document.getElementById('loading').classList.add('show');
        fetch(url, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').classList.remove('show');
                if (data.status == 'error') {
                    // console.log(data.message);
                    // document.querySelector('#' + id + ' input[name=email]').classList.remove('error-outline');
                    // document.querySelector('#' + id + ' input[name=email]').nextElementSibling.classList.add('hide');
                }
                callback(data);
            })
            .catch(error => {
                document.getElementById('loading').classList.remove('show');
                console.error('Error:', error);
            });
    } else {
        // console.log(email,'email');
        formFieldsValidation('#' + id + ' input[name=email]', 'Invalid email address');
    }

}
// Callback function that will be triggered when the user successfully signs in
function onSignIn(response) {
    // Ensure response.credential is available
    var is_page = document.getElementById('g_id_signin').getAttribute('data-page');
    var page_type = document.getElementById('g_id_signin').getAttribute('data-pagetype');
    var formId;
    if (page_type == 'login') {
        formId = 'signin';
    } else if (page_type == 'signup') {
        formId = 'signup';
    }
    if (response.credential) {
        const idToken = response.credential; // The ID token
        // console.log("ID Token:", idToken);
        var googleUrl = absolute_path + '/google_login.json?idToken=' + idToken;
        // Send the ID token to your server for validation
        // console.log(googleUrl,'url');
        fetch(googleUrl, {
            method: 'GET',
            // body: JSON.stringify({ 'idToken': idToken }),
            // data : {'idToken':idToken},
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                // Check if the response contains an error or success message
                if (data.error) {
                    // console.error('Error during authentication:', data.error);
                    alert('Authentication failed: ' + data.error);
                } else {
                    // console.log('User authenticated:', data.user);
                    // Do something with the authenticated user's information
                    let email = data.user.email,
                        first_name = data.user.given_name,
                        last_name = data.user.family_name,
                        password = '!Test1234',
                        login_type = 'google',
                        social_api_response = data.user.all;
                    let query_url = '/dashboard';
                        
                    checkEmailExistsForSocialLogin(email, formId, function (data) {
                        // alert(data.status);
                        if (data.status == 'success' && data.data[0].login_type !== 'temporary') {
                            localStorage.setItem('session_id', data.data[0].id);
                            socialLogin(email, password, login_type, function (status) {
                                if (status == 'success') {
                                    saveActivityCookies();
                                    if (is_page == 1) {
                                        document.getElementById(formId).action = absolute_path + query_url;
                                    }else{
                                        document.getElementById(formId).action = window.location.href;
                                    }
                                    document.getElementById(formId).submit();
                                } else {
                                    formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                                }
                            });
                        } else if (data.status == 'success' && data.data[0].login_type == 'temporary') {                             
                            signUpUserSocialLogin(email, first_name, last_name, login_type,social_api_response, function (data) {
                                // console.log(data);
                                var url = '';
                                if (is_page == 1) {
                                    url = '/dashboard';
                                }else{
                                    url = window.location.href;
                                }
                                if (data.status == 'success') {
                                    if (typeof dataLayer !== 'undefined') {
                                        window.dataLayer.push({
                                            'event': 'signup_button'
                                        });
                                    }
                                    if (typeof fbq === "function") {
                                        fbq('track', 'CompleteRegistration');
                                    }
                                    bing_event('signup');
                                    // delete_cookie('beta_email');
                                    delete_cookie('beta_password');
                                    closeModalOpened();
                                    saveActivityCookies();
                                    document.getElementById(formId).action = url;
                                    document.getElementById(formId).submit();
                                } else {
                                    document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
                                    document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
                                }
                            });
                        }else {
                            if(is_page == 1){
                                if(formId == 'signin') {
                                    var form = document.getElementById("signin");
                                    form.id = "signup";    
                                    document.getElementById("login_h3").textContent = "Sign Up and Start your Free Account today";
                                    setFormValues("signup", login_type, first_name, last_name, email, password, social_api_response); 
                                    document.getElementById('signin_step1').classList.add('hide');
                                    document.getElementById('signin_step2').classList.add('hide');
                                    document.getElementById('signup_step3').classList.remove('hide');
                                }else{                                                                 
                                    setFormValues("signup", login_type, first_name, last_name, email, password, social_api_response); 
                                    document.getElementById('signup_step1').classList.add('hide');
                                    document.getElementById('signup_step2').classList.add('hide');
                                    document.getElementById('signup_step3').classList.remove('hide');
                                }
                            }else if(page_type == 'login'){  
                                setFormValues("signup", login_type, first_name, last_name, email, password, social_api_response);
                                document.getElementById('login_modal').classList.add('hide');
                                document.getElementById('signup_modal').classList.add('show');
                                document.getElementById('signin_step1').classList.add('hide');
                                document.getElementById('signup_step1').classList.add('hide');
                                document.getElementById('signup_step3').classList.remove('hide');
                            }
                            else{
                                setFormValues("signup", login_type, first_name, last_name, email, password, social_api_response);
                                document.getElementById('signup_step1').classList.add('hide');
                                document.getElementById('signup_step2').classList.add('hide');
                                document.getElementById('signup_step3').classList.remove('hide');
                            }                            
                            // formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                        }
                    })
                }
                console.log("Authentication successful:", data);
                // Handle server response (e.g., store user data or session)
            })
            .catch(error => {
                console.error('Network or other error:', error);
                alert('An error occurred: ' + error.message);
                console.error("Error during authentication:", error);
            });
    } else {
        console.error("ID Token not available in response.");
    }
}
function signUpUserSocialLogin(email, first_name, last_name, login_type = null,social_api_response = null, callback) {
    var url = absolute_path + '/signup.json';
    if (login_type == 'google') {
        url += '?login_type=google';
    }
    if (login_type == 'apple') {
        url += '?login_type=apple';
    }
    if (login_type == 'linkedin') {
        url += '?login_type=linkedin';
    }
    document.getElementById('loading').classList.add('show');
    var dataObj = {
        first_name: first_name,
        last_name: last_name,
        email: email,
        password: '!Test1234',
        login_type: login_type,
        social_api_response: social_api_response // Optional, if you want to send the full response
    }
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(dataObj)
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            document.getElementById('loading').classList.remove('show');
            if (data.status == 'success') {
                callback(data);
            } else {
                callback(data);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.log('Error:', error);
        });
}
function socialLogin(email, password, login_type = null, callback) {
    // console.log(callback);
    var url = absolute_path + '/login.json?email=' + email + '&password=' + password;
    if (login_type == 'google') {
        url += '&login_type=google';
    }
    if (login_type == 'apple') {
        url += '&login_type=apple';
    }
    if (login_type == 'linkedin') {
        url += '&login_type=linkedin';
    }
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
        .then(response => response.json())
        .then(data => {
            // console.log(data);
            if (data.status == 'success') {
                var timeNow = new Date().toLocaleString();
                setCookie('logged_in', timeNow, 365);
                callback(data.status);
            } else {
                document.getElementById('loading').classList.remove('show');
                callback(data.status);
            }
        })
        .catch(error => {
            document.getElementById('loading').classList.remove('show');
            console.error('Error:', error);
        });
}
// Initialize Apple ID Auth only on button click
document.addEventListener("DOMContentLoaded", function () {
    if (typeof AppleID !== "undefined") {
        AppleID.auth.init({
            clientId: 'com.franchise.beta',  // Your app's client ID (Services ID)
            scope: 'name email',             // Specify the scope for the data you need
            redirectURI: absolute_path + '/callbackApple',  // The redirect URI after successful login
            state: 'state',
            usePopup: true                   // Use a popup for login flow
        });

        // Attach event listener to the button to trigger the Apple login popup on click
        if (document.getElementById('appleid-signin')) {
            document.getElementById('appleid-signin').addEventListener('click', function () {
                const data = AppleID.auth.signIn();  // This will open the popup when the button is clicked
            });
        }

        // Listen for authorization success.
        document.addEventListener('AppleIDSignInOnSuccess', (response) => {
            // Handle successful response.
            var formId = document.getElementById('appleid-signin').getAttribute('data-id');
            var is_page = document.getElementById('appleid-signin').getAttribute('data-page');
            const authData = response.detail.authorization;
            const userData = response.detail.user;
            // console.log(authData);
            // console.log(userData,'data2');
            if (response && authData && userData) {
                // If response, authData, and userData exist
                let email = userData.email,
                    first_name = userData.name.firstName,
                    last_name = userData.name.lastName,
                    password = 'apple@123',
                    login_type = 'apple';
                let query_url = '/dashbaord';
                checkEmailExistsForSocialLogin(email, formId, function (data) {
                    // alert(data.status);
                    if (data.status == 'success') {
                        socialLogin(email, password, login_type, function (status) {
                            if (status == 'success') {
                                if (is_page == 1) {
                                    document.getElementById(formId).action = absolute_path + query_url;
                                }else{
                                    document.getElementById(formId).action = absolute_path + window.location.href;
                                }
                                document.getElementById(formId).submit();
                            } else {
                                formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                            }
                        });
                    } else {
                        formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                    }
                });
                // Proceed with further logic, such as saving the data or handling it
            } else if (response && authData) {
                // If response and authData exist
                const result = onSignInApple(authData);
                //  console.log(result);
                // Proceed with further logic, such as saving the data or handling it
            } else {
                console.log("No user data available.");
            }
        });

        // Listen for authorization failures.
        document.addEventListener('AppleIDSignInOnFailure', (event) => {
            // Handle error.
            console.log(event);
        });
    }
});

function onSignInApple(authData) {
    // Ensure response.credential is available
    var formId = document.getElementById('appleid-signin').getAttribute('data-id');
    var is_page = document.getElementById('appleid-signin').getAttribute('data-page');
    if (authData.id_token) {
        const idToken = authData.id_token; // The ID token
        const authorizationCode = authData.code; // The authorizationCode token
        // console.log("ID Token:", idToken);
        var appleUrl = absolute_path + '/apple_login.json?idToken=' + idToken + '&authorizationCode=' + authorizationCode;
        // Send the ID token to your server for validation
        fetch(appleUrl, {
            method: 'GET',
            // body: JSON.stringify({ 'idToken': idToken,'authorizationCode':authorizationCode }),
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                // console.log(data); return;
                // Check if the response contains an error or success message
                if (data.error) {
                    console.error('Error during authentication:', data.error);
                    alert('Authentication failed: ' + data.error);
                } else {
                    console.log('User authenticated:', data.user);
                    // Do something with the authenticated user's information
                    let email = data.user.email,
                        first_name = data.user.firstName,
                        last_name = data.user.lastName,
                        password = '!Test1234',
                        login_type = 'apple';
                    let query_url = '/dashboard';
                    checkEmailExistsForSocialLogin(email, formId, function (data) {
                        // alert(data.status);
                        if (data.status == 'success') {
                            socialLogin(email, password, login_type, function (status) {
                                if (status == 'success') {
                                    if (is_page == 1) {
                                        document.getElementById(formId).action = absolute_path + query_url;
                                    }else{
                                        document.getElementById(formId).action = absolute_path + window.location.href;
                                    }
                                    document.getElementById(formId).submit();
                                } else {
                                    formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                                }
                            });
                        } else {
                            formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                        }
                    })
                }
                console.log("Authentication successful:", data);
                // Handle server response (e.g., store user data or session)
            })
            .catch(error => {
                console.error('Network or other error:', error);
                alert('An error occurred: ' + error.message);
                console.error("Error during authentication:", error);
            });
    } else {
        console.error("ID Token not available in response.");
    }
}
//linkedin login
function openLinkedInPopup() {
    const clientId = '86md91w0p0gxqz'; // LinkedIn Client ID
    const redirectUri = absolute_path + '/callbackLinkedin'; // Your redirect URI
    const scope = 'openid profile email'; // Define the required scopes
    const state = 'randomString'; // Optional state for security
    const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&scope=${encodeURIComponent(scope)}&state=${state}`;

    // Open LinkedIn login in a pop-up
    const width = 600;
    const height = 600;
    const left = (window.innerWidth - width) / 2;
    const top = (window.innerHeight - height) / 2;
    // Open the LinkedIn authorization popup
    const popup = window.open(authUrl, 'LinkedInLogin', `width=${width},height=${height},top=${top},left=${left}`);
    // Interval to check if the popup is closed or if the user has been redirected
    const interval = setInterval(function () {
        try {
            // Check if popup is closed
            if (popup.closed) {
                clearInterval(interval);
                return; // Exit if popup is closed
            }

            // Check if popup has redirected to the callback URL
            if (popup.location.href.indexOf(redirectUri) === 0) {
                clearInterval(interval);
                const urlParams = new URLSearchParams(popup.location.search);
                const code = urlParams.get('code');
                const error = urlParams.get('error');

                // If there's an error, show an alert
                if (error) {
                    alert('Error during LinkedIn login: ' + error);
                    popup.close();
                    return;
                }

                if (code) {
                    // Send the authorization code to the server to get the access token
                    // getLinkedInData(code,state);
                    var interval = setInterval(function () {
                        if (popup.closed) {
                            clearInterval(interval);
                            // getLinkedInData(); // Fetch LinkedIn data after popup closes
                            getLinkedInData(redirectUri, code, state);
                        }
                    }, 10);
                }

                // Close the popup after processing the response
                popup.close();
            }
        } catch (e) {
            // Catch any cross-origin errors here (e.g., accessing popup location)
        }
    }, 1000); // Check every 100ms
}

function getLinkedInData(redirectUri, code, state) {
    console.log(redirectUri, 'redirectUri');
    // Wait for the popup to be closed and get the authorization code

    var getUrl = redirectUri + '?code=' + encodeURIComponent(code) + '&state=' + encodeURIComponent('randomString1111');
    var formId = document.getElementById('g_id_signin').getAttribute('data-id');
    // Send an AJAX request to fetch user data from the server
    fetch(getUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    }).then(response => response.json())
        .then(data => {
            if (data.error) {
                // console.error('Error during authentication:', data.error);
                alert('Authentication failed: ' + data.error);
            } else {
                // console.log('User authenticated:', data.user);
                // Do something with the authenticated user's information
                let email = data.user.email,
                    first_name = data.user.firstName,
                    last_name = data.user.lastName,
                    password = '!Test1234',
                    login_type = 'linkedin',

                    $query_url = '/franchise';
                checkEmailExistsForSocialLogin(email, formId, function (data) {
                    // alert(data.status);
                    if (data.status == 'success') {
                        socialLogin(email, password, login_type, function (status) {
                            if (status == 'success') {
                                document.getElementById(formId).action = absolute_path + $query_url;
                                document.getElementById(formId).submit();
                            } else {
                                formFieldsValidation('#signin input[name=password]', 'Username and password does not match!');
                            }
                        });
                    } else {
                        signUpUserSocialLogin(email, first_name, last_name, login_type, function (data) {
                            // console.log(data);
                            var url = '/dashboard/franchise';
                            if (data.status == 'success') {
                                if (typeof dataLayer !== 'undefined') {
                                    window.dataLayer.push({
                                        'event': 'signup_button'
                                    });
                                }
                                if (typeof fbq === "function") {
                                    fbq('track', 'CompleteRegistration');
                                }
                                bing_event('signup');
                                // delete_cookie('beta_email');
                                delete_cookie('beta_password');
                                var content = `<h2>Thank You</h2>`;
                                content += `<p>Your account as been created! Click "Okay" to view your My Account dashboard.</p>
                                    <p class="text-center"><a href="`+ url + `" class="button">Okay</a></p>`;
                                document.querySelector('#alert .content').innerHTML = content;
                                document.getElementById('alert').classList.add('show');
                                welcome_message();
                            } else {
                                document.querySelector('#signup select[name=state]').nextElementSibling.classList.remove('hide');
                                document.querySelector('#signup select[name=state]').nextElementSibling.innerText = data.message;
                            }
                        });
                        // formFieldsValidation('#signin input[name=email]','Couldn\'t find your account');
                    }
                })
            }
            console.log("Authentication successful:", data);
            // Handle server response (e.g., store user data or session)
        })
        .catch(error => {
            console.error('Network or other error:', error);
            alert('An error occurred: ' + error.message);
            console.error("Error during authentication:", error);
        });
}
// Event listener for the LinkedIn login button
// document.getElementById('linkedin-login-button').addEventListener('click', openLinkedInPopup);

// Function to set the common form fields
function setFormValues(formId, login_type, first_name, last_name, email, password, social_api_response) {
    const form = document.getElementById(formId);
    form.querySelector('input[name=login_type]').value = login_type;
    form.querySelector('input[name=first_name]').value = first_name;
    form.querySelector('input[name=last_name]').value = last_name;
    form.querySelector('input[name=email]').value = email;
    form.querySelector('input[name=password]').value = btoa(password);
    form.querySelector('input[name=social_api_response]').value = social_api_response;
}document.addEventListener('DOMContentLoaded', () => {
    const resultsEl = document.getElementById('results');
    if (!resultsEl) {
      console.warn('#results element not found.');
      return;
    }
    const loader = document.getElementById('loading');
    if (loader) {
      loader.classList.remove('show');
    }
    fetch('/more-listings.json?s='+state_code+'&c='+cat_id)
      .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
      })
      .then(json => {
        if (json.status === 'success' && json.data) {
          const decodedHTML = decodeHTML(json.data);
          resultsEl.innerHTML = decodedHTML;
          lazyload();
        } else {
          console.error('Unexpected JSON structure:', json);
        }
      })
      .catch(error => {
        console.error('Fetch error:', error);
      });
  
    function decodeHTML(html) {
      const txt = document.createElement('textarea');
      txt.innerHTML = html;
      return txt.value;
    }
});  // On Load
const telEl = document.getElementById('phone');
document.addEventListener('DOMContentLoaded', function () {
    const form = document.getElementById('submission');
    populateFieldsFromFVCampaign();
    initPhoneFormatter();
    ga4_ecommerce('view_item');
    
    form.querySelectorAll('input, select, textarea').forEach(input => {
        input.addEventListener('input', clearFieldError);
        input.addEventListener('blur', clearFieldError);
    });

    if (profile_buttons_connect && profile_buttons_connect == 'submission_profile') {
        document.querySelector('#connect button#small_button').click();
    }
});

telEl.addEventListener('keyup', (e) => {
    initPhoneFormatter();
});


// Telephone Listener
function formatPhoneInput(el) {
    if (!el) return;

    // If input type doesn't support selection, skip it
    const allowedTypes = ['text', 'tel'];
    if (!allowedTypes.includes(el.type)) return;

    let cursorPosition = el.selectionStart;
    let raw = el.value.replace(/\D/g, '');
    let formatted = '';

    if (raw.length > 0) {
        formatted = '(' + raw.substring(0, 3);
    }
    if (raw.length >= 4) {
        formatted += ') ' + raw.substring(3, 6);
    }
    if (raw.length >= 7) {
        formatted += '-' + raw.substring(6, 10);
    }

    el.value = formatted;
    // Try to restore cursor position
    el.setSelectionRange(el.value.length, el.value.length);
}

// Telephone Formatter
function initPhoneFormatter() {
  if (!telEl) return;

  formatPhoneInput(telEl);

  telEl.addEventListener('input', () => {
    formatPhoneInput(telEl);
  });
}

document.addEventListener('DOMContentLoaded', () => {
  initPhoneFormatter();
});


//to submit the form when a user clicks the enter button on the lead-submit form
if (!window.requestInfoEnterListenerAdded) {
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Enter') {
            const activeElement = document.activeElement;
            const submissionForm = document.querySelector('#submission');

            if (activeElement && submissionForm && submissionForm.contains(activeElement)) {
                event.preventDefault();

                // Check if submitForm function is available
                if (typeof submitForm === 'function') {
                    submitForm();
                } else {
                    const requestBtn = submissionForm.querySelector('button[onclick*="submitForm"]');
                    if (requestBtn) requestBtn.click();
                }
            }
        }
    });
    window.requestInfoEnterListenerAdded = true;
}

//Submit Newsletter
function submitNewsletter(email) {
    var xhr1 = new XMLHttpRequest(),
        url = "/newsletter.json",
        newsletter = document.getElementById('request_info_newsletter'),
        form_type = 'step_1',
        data = 'form_type=' + form_type;
    if (newsletter.checked == true) {
        data += '&newsletter=1';
    } else {
        data += '&newsletter=0';
    }
    data += '&subscribe=' + email;
    xhr1.open("POST", url);
    xhr1.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr1.onload = function () {
        // ga4_datalayer('fv_submit_form', null, null, 'step1');
    }
    xhr1.send(data);
}

function alterLoginModals(e, id, email) {
    const login_ty = id;
    if (id == 'login_google') {
        id = 'login';
    }
    const h3Element = document.querySelector('#'+id+'_modal #'+id+'_h3');
    h3Element.textContent = 'You have an existing account';
    const pElement = document.querySelector('#'+id+'_modal #'+id+'_subtitle');
    const franName = e.getAttribute('data-name');
    if (id == 'signup') {
        toggleLogins(e,'signup');
        document.querySelector('#signup input[name=login_type]').value = 'temporary';
        document.querySelector('#signup input[name=email]').value = email;
        document.querySelector('#signup input[name=target]').value = 'submission_profile';
        document.getElementById('signup_step1').classList.add('hide');
        document.getElementById('signup_step2').classList.remove('hide');
        document.querySelector('#signup_step2 .signup_btn').classList.add('hide');
        document.getElementById('signup_modal').classList.add('set_password');
        h3Element.innerHTML = 'Set Your Password to Connect with <span class="link_blue">'+franName+'</span>';
        // pElement.innerHTML = 'We found an existing account with <strong>'+email+'</strong>. Please complete your Sign Up to continue.';
        pElement.innerHTML = 'Please complete your Sign Up by setting up the password to continue.';
        document.getElementById('step2_signup_btn').innerText = 'Set Password';
    }else if (login_ty == 'login_google') {
        toggleLogins(e,'login');
        document.querySelector('#signin input[name=target]').value = 'submission_profile';
        h3Element.innerHTML = 'Sign in to Connect with <span class="link_blue">'+franName+'</span>';
        pElement.innerHTML = 'You\'ve already logged in via google with <strong>'+email+'</strong>. Please login via google to continue.';
    }else{
        toggleLogins(e,'login');
        document.querySelector('#signin input[name=email]').value = email;
        document.querySelector('#signin input[name=target]').value = 'submission_profile';
        document.getElementById('signin_step1').classList.add('hide');
        document.getElementById('signin_step2').classList.remove('hide');
        h3Element.innerHTML = 'Sign in to Connect with <span class="link_blue">'+franName+'</span>';
        pElement.innerHTML = 'We found an existing account with <strong>'+email+'</strong>. Please log in to continue.';
    }
    h3Element.classList.add('submission');
    pElement.classList.remove('hide');
    document.querySelector('#'+id+'_modal input[name=email]').value = email;
}

async function leadFormNext(e) {
    
    removeErrors('lead_step_1', ['step1_email']);
    const email = document.getElementById('step1_email').value;
    const emailPattern = /^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,}$/i;
    if (!email.trim() || !emailPattern.test(email)) {
        formFieldsValidation('#lead_step_1 input[name=step1_email]', 'Enter a valid email address.');
        return;
    }else{
        document.getElementById('loading').classList.add('show');
        const validateurl = absolute_path + '/validate_email.json?email=' + email;
        const response = await fetch(validateurl);
        const validatedata = await response.json();
        if (validatedata == 0) {
            formFieldsValidation('#lead_step_1 input[name=step1_email]', 'Not a valid email address.');
            document.getElementById('loading').classList.remove('show');
            return;
        }
        if (!login_contact_id) {
            const checkEmail_url = absolute_path + '/check_email_exists.json?email=' + email;
            const response_check = await fetch(checkEmail_url);
            const validatedata_check = await response_check.json();
            if (validatedata_check && validatedata_check['status'] == 'error') {
                closeModalOpened('submission');
                if (Array.isArray(validatedata_check['data']) && validatedata_check['data'].length > 0) {
                    const validateData = validatedata_check['data'][0];
                    if (validateData['login_type'] == 'temporary') {
                        //alterLoginModals(e, 'signup', email);
                        await setOneTimePassword(email);
                    }else if (validateData['login_type'] == 'google') {
                        alterLoginModals(e, 'login_google', email);
                    }else{
                        alterLoginModals(e, 'login', email);
                    }
                }

                document.getElementById('loading').classList.remove('show');
                return;
            }
        }
    }
    submitNewsletter(email);
    document.getElementById('loading').classList.remove('show');
    document.getElementById('lead_step_1').classList.add('hide');
    document.getElementById('lead_step_2').classList.remove('hide');
    document.querySelector('#lead_step_2 input[name=email]').value = email;
    document.querySelector('#request_information input[name=user_type]').value = 'new';
    setCookie("lead_email", email);
    
}

async function setOneTimePassword(email) {
    var url = absolute_path + '/set_one_time_password.json?email=' + email;
    document.getElementById('loading').classList.add('show');
    await fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        if (data.status == 'success') {
            console.log(data);
            //closeModalOpened();
            document.getElementById('one_time_password_modal').classList.add('show');
            document.querySelector('#one_time_password_modal input[name=one_time_email]').value = email;
            document.querySelector('#one_time_password_modal .one_time_email_val').textContent = email;
        }
        document.getElementById('loading').classList.remove('show');
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function resendOneTimeCode() {
    const email = document.querySelector('#one_time_password_modal input[name=one_time_email]').value;
    var url = absolute_path + '/set_one_time_password.json?email=' + email;
    document.getElementById('loading').classList.add('show');
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        // console.log(data);
        if (data.status == 'success') {
            document.querySelector('#alert .content').innerHTML = `
            <h3>One-Time Code Has Been Resent!</h3>
            <p class="italic">One-Time Code has been sent to your email address, please check and verify.</p>
            <div class="request_alert_btns"><button type="button" onclick="modal('alert','close');">Okay</button></div>`;
            document.getElementById('alert').classList.add('show');
            
        }
        document.getElementById('loading').classList.remove('show');
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function validateOneTimePassword(e) {
    const email = document.getElementById('one_time_email').value;
    const one_time_passcode = document.getElementById('one_time_passcode').value;
    var url = absolute_path + '/validate_one_time_password.json?email=' + email+'&one_time_passcode='+one_time_passcode;

    if (!one_time_passcode) {
        formFieldsValidation('#one_time_password_form input[name=one_time_passcode]', 'One time Password is not valid.'); 
        return;
    }else if (one_time_passcode.length != 6) {
        formFieldsValidation('#one_time_password_form input[name=one_time_passcode]', 'One time Password is not valid.');
        return;
    }
    fetch(url, {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        console.log(data);
        if (data.status == 'success') {
            document.getElementById('one_time_password_modal').classList.remove('show');
            alterLoginModals(e, 'signup', email);
        } else {
            formFieldsValidation('#one_time_password_form input[name=one_time_passcode]', 'One time Password is not valid.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

function showThankyouMessage(profile_name,user_type='') {
    if (user_type) {
        document.querySelector('#alert .content').innerHTML = `
        <i class="checkcircle"></i>
        <h3>Request Sent!</h3>
        <p class="italic">${profile_name} will be contacting you soon.</p>
        <p class="italic">While you wait, answer a few quick questions to see the best franchise matches for your goals and budget.</p>
        <div class="request_alert_btns"><button type="button" onclick="modal('alert','close');">Next: Personalize My Matches</button></div>`;
    }else{
        document.querySelector('#alert .content').innerHTML = `
        <i class="checkcircle"></i>
        <h3>Request Sent!</h3>
        <p class="italic">${profile_name} will be contacting you soon.</p>
        <div class="request_alert_btns"><button type="button" onclick="modal('alert','close');">Continue</button></div>`;
    }
}

async function submitForm() {
    const form = document.getElementById('submission');
    let isValid = true;

    // Reset previous state (only for visible inputs)
    form.querySelectorAll('input:not([type="hidden"]), select, textarea').forEach(el => {
        el.classList.remove('error-outline');
        const errorEl = el.parentElement.querySelector('.error');
        if (errorEl) errorEl.textContent = '';
    });

    // Helper
    function addError(input, message) {
        if (!input || input.type === 'hidden') return;
    
        input.classList.add('error-outline');
    
        const errorEl = input.parentElement.querySelector('.error');
        if (errorEl) {
            errorEl.textContent = message;
        }
    
        isValid = false;
    }    

    // VALIDATION

    const firstName = document.getElementById('first_name');
    const firstNameValue = firstName.value.trim();
    if (!firstNameValue || !/^[a-zA-Z]+$/.test(firstNameValue) || firstNameValue.length < 2) {
        addError(firstName, 'First name must be at least 2 alphabetic characters.');
    }

    const lastName = document.getElementById('last_name');
    const lastNameValue = lastName.value.trim();
    if (lastNameValue.length < 3 || !/^[a-zA-Z\s]+$/.test(lastNameValue) || !/[aeiouyAEIOUY]/.test(lastNameValue)) {
        addError(lastName, 'Last name must be at least 3 alphabetic characters and contain a vowel.');
    }

    if (firstNameValue === lastNameValue) {
        addError(lastName, 'First and Last name cannot be the same.');
    }

    const phone = document.getElementById('phone');
    const phonePattern = /^[0-9\-\+\s\(\)]{14}$/;
    if (!phone.value.trim() || !phonePattern.test(phone.value)) {
        addError(phone, 'Enter a valid phone number.');
    }

    const zipcode = document.getElementById('zip_code');
    let isValidZip = await isValidZipOrPostal(zipcode.value)
    if (!isValidZip) {
        addError(zipcode, 'Enter a valid ZIP code.');
    }

    const desiredLocation = document.getElementById('state');
    if (desiredLocation) {
        const desiredLocationValue = desiredLocation.value;
        if (desiredLocationValue == 'none') {
            addError(desiredLocation, 'Select Desired Franchise Locaion.');
        }
    }

    // Require .request_info checkbox to be checked
    const requestCheckbox = document.querySelector('.request_info');
    if (!requestCheckbox?.checked) {
        requestCheckbox.classList.add('error-input');
        isValid = false;
    }

    const user_type_field = document.getElementById('user_type');
    var user_type = user_type_field ? user_type_field.value : '';
    const profile_name_element = document.getElementById('profile_name');
    var profile_name = '';
    if (profile_name_element) {
        profile_name = profile_name_element.value;
    }

    if (!isValid) return;

    document.getElementById('loading').classList.add('show');
    submitFormDataAsQuery().then(async data => {
        if (typeof dataLayer !== 'undefined') {
            const fbolist = document.querySelector('input[name=fbolist]');
            const investment = document.querySelector('input[name=investment_level]').value;
            var transaction_id = Date.now();
            if (data != null) {
                transaction_id = data.session_id;
            }
            dataLayer.push({ ecommerce: null });  // Clear the previous ecommerce object.
            dataLayer.push({
                event: "beta_purchase",
                ecommerce: {
                    transaction_id: transaction_id,
                    value: investment,
                    currency: "USD",
                    items: [{item_id: fbolist.value,affiliation: 60,price: '.00',quantity: 1}]
                }
            });
            if(data.temp_user.toLowerCase() == 'new') {
                ga4_global_events('account_created','registrationMethod','lead submission');
            }
            if(data.status.toLowerCase() != 'error') {
                ga4_ecommerce('purchase');
            }
        }
        document.getElementById('loading').classList.remove('show');
        closeModalOpened('submission');
        replaceButtonsIfExist();

        // if(data.login_type == 'temporary' && data.temp_user == 'new'){
        //     await setOneTimePassword(email);
        // }
        document.getElementById('alert').classList.add('request_alert');
        if (data.show_survey === 1) {
            // <p>${data.message}</p>
            showThankyouMessage(profile_name,user_type);
            
            document.getElementById('alert').classList.add('show');
            // Load quiz assets only if survey is shown
            await loadQuizAssets();
            let quiz_id = 27;
            // Show the Account survey quiz modal for temporary users            
            // set variable when new temporary user found
            if(data.login_type == 'temporary' && data.temp_user == 'new'){
                quiz_id = 26;
                localStorage.setItem('reload_after_modal_close', 'true');
                localStorage.setItem('survey', 1);
            }
            const html = await fetch(`${absolute_path}/quiz_survey.json?quiz_id=${quiz_id}&quiz_only=true`)
                        .then(r => r.text())
                        .catch(err => { console.error(err); return ''; });

            if (!html) return;
            const quizModal  = document.getElementById('quiz_modal');
            const contentEl  = quizModal.querySelector('.content');
            contentEl.innerHTML = html;
            runInlineScripts(contentEl);
            document.body.style.overflow = 'hidden';
            quizModal.classList.add('show');
            //ga4_global_events('quiz_started');
            return;
        }
 
        showThankyouMessage(profile_name,user_type);
        document.getElementById('alert').classList.add('show');
    });
}

// Replace buttons after submission
function replaceButtonsIfExist() {
    const smallBtn = document.getElementById('small_button');
    const bigBtn = document.getElementById('big_button');
    const target = document.querySelector('#top button.outlined');

    if (target) {
        const nav = document.createElement('nav');
        nav.id = 'header_nav';
        nav.innerHTML = `
            <div class="notifications_nav" data-count="0" onclick="window.location='/notifications'">
                <i class="bell"></i>
            </div>
            <div class="favorites_nav" data-count="0" onclick="window.location='/favorites';">
                <i class="heart"></i>
            </div>
            <div onclick="menu_modal();"><div class="avatar">&nbsp;</div></div>
        `;

        target.replaceWith(nav);
    }

    if (smallBtn) {
        smallBtn.outerHTML = '<button class="disabled" disabled><span>Connected</span></button>';
    }

    if (bigBtn) {
        bigBtn.outerHTML = '<button class="big disabled" disabled><span>Connected</span></button>';
    }
}

// Valid ZIP Code
async function isValidZipOrPostal(zip) {
    const usZip = /^\d{5}$/;

    if (!usZip.test(zip)) {
        return Promise.resolve(false); // not a valid U.S. ZIP
    }

    const url = `${absolute_path}/zip.json?z=${encodeURIComponent(zip)}`;
    // console.log(url);

    return fetch(url)
        .then(response => {
            if (!response.ok) return false;
            return response.json();
        })
        .then(data => {
            // If your API returns something like { valid: true }
            return !!data; // or return data.valid === true;
        })
        .catch(error => {
            console.error('ZIP fetch failed:', error);
            return false;
        });
}


// Clear error message
function clearFieldError(event) {
    const input = event.target;
    input.classList.remove('error-outline');
    input.classList.remove('error-input');
    
    const errorEl = input.parentElement.querySelector('.error');
    if (errorEl) {
        errorEl.textContent = '';
    }
}

// Prepopulate fields
function populateFieldsFromFVCampaign() {
    const fields = ['first_name', 'last_name', 'email', 'phone', 'zip_code', 'preferred_state'];
    const campaignCookieName = 'fv_campaign';

    function getCookie(name) {
        const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
        return match ? decodeURIComponent(match[2]) : null;
    }

    // 1. Direct cookie-to-field matching
    fields.forEach(fieldId => {
        const el = document.getElementById(fieldId);
        if (!el) return;

        const currentValue = el.value?.trim();
        if (currentValue) return; // Skip if already populated

        const cookieVal = getCookie(fieldId);
        if (!cookieVal) return;

        if (el.tagName === 'SELECT' && fieldId === 'preferred_state') {
            const option = el.querySelector(`option[value="${cookieVal}"]`);
            if (option) option.selected = true;
        } else {
            el.value = cookieVal;
        }
    });

    // 2. Parse and apply `fv_campaign` cookie data
    const rawCampaign = getCookie(campaignCookieName);
    if (!rawCampaign) return;

    try {
        const campaignData = JSON.parse(rawCampaign);

        Object.entries(campaignData).forEach(([key, value]) => {
            const el = document.getElementById(key);
            if (!el) return;

            const currentValue = el.value?.trim();
            if (currentValue) return; // Skip if already populated

            el.value = value;
        });
    } catch (e) {
        //console.error('Invalid JSON in fv_campaign cookie:', e);
    }
}

// Form Submit
async function submitFormDataAsQuery() {
    const form = document.getElementById('submission');
    if (!form) throw new Error('Form not found');
  
    const elements = form.querySelectorAll('input, select');
    const params = new URLSearchParams();
  
    elements.forEach(el => {
      if (!el.name || el.disabled) return;
      if ((el.type === 'checkbox' || el.type === 'radio') && !el.checked) return;
      params.append(el.name, el.value);
    });
  
    const url = absolute_path+`/submission.json?${params.toString()}`;
  
    try{
        const response = await fetch(url);
        const data = await response.json();

        if (!response.ok) {
            console.error('Server error:', data);
            return data; // or null if you prefer
        }

        return data;
    }catch (err) {
        console.error('Fetch error:', err);
        return null;
    }
  }

// reload page if new temporary user is found
const quizModal = document.getElementById('quiz_modal');
const observer = new MutationObserver(() => {
    if (!quizModal.classList.contains('show')) {
        observer.disconnect();
        // Only reload if localStorage flag is set
        if (localStorage.getItem('reload_after_modal_close') === 'true') {
            saveActivityCookies();
            localStorage.removeItem('reload_after_modal_close'); // clean up
            location.reload();
        }
    }
});
observer.observe(quizModal, {
    attributes: true,
    attributeFilter: ['class']
});

// GA4 ecommerce events
function ga4_ecommerce(type) {
    var item = {
        item_id: item_id,
        item_name: item_name,
        item_category: item_category,
        price: price,
        quantity: 1
    };

    var ecommerceData = {
        currency: "USD",
        value: price + ".00",
        items: [item]
    };

    if (type === "purchase") {
        ecommerceData.transaction_id = getCookie('udid'); 
        ecommerceData.affiliation = "FCOM";
    }

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    window.dataLayer.push({
        event: type,
        ecommerce: ecommerceData
    });
}
</script>
<script type="text/javascript" id="" charset="">!function(b,e,f,g,a,c,d){b.fbq||(a=b.fbq=function(){a.callMethod?a.callMethod.apply(a,arguments):a.queue.push(arguments)},b._fbq||(b._fbq=a),a.push=a,a.loaded=!0,a.version="2.0",a.queue=[],c=e.createElement(f),c.async=!0,c.src=g,d=e.getElementsByTagName(f)[0],d.parentNode.insertBefore(c,d))}(window,document,"script","https://connect.facebook.net/en_US/fbevents.js");fbq("init","194010784310142");fbq("track","PageView");</script>
<noscript><img alt="Facebook Pixel" height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=194010784310142&amp;ev=PageView&amp;noscript=1"></noscript><script type="text/javascript" id="" charset="">(function(c,d,f,g,e){c[e]=c[e]||[];var h=function(){var b={ti:"5695332"};b.q=c[e];c[e]=new UET(b);c[e].push("pageLoad")};var a=d.createElement(f);a.src=g;a.async=1;a.onload=a.onreadystatechange=function(){var b=this.readyState;b&&"loaded"!==b&&"complete"!==b||(h(),a.onload=a.onreadystatechange=null)};d=d.getElementsByTagName(f)[0];d.parentNode.insertBefore(a,d)})(window,document,"script","//bat.bing.com/bat.js","uetq");</script>
<script type="text/javascript" id="" charset="">(function(a,e,b,f,g,c,d){a[b]=a[b]||function(){(a[b].q=a[b].q||[]).push(arguments)};c=e.createElement(f);c.async=1;c.src="https://www.clarity.ms/tag/"+g;d=e.getElementsByTagName(f)[0];d.parentNode.insertBefore(c,d)})(window,document,"clarity","script","e2iolzu7nc");</script>
<script type="text/javascript" id="" charset="">(function(b,c,d){var a=b.createElement("script");a.type="text/javascript";a.src="https://a.omappapi.com/app/js/api.min.js";a.async=!0;a.dataset.user=c;a.dataset.account=d;b.getElementsByTagName("head")[0].appendChild(a)})(document,164494,178508);</script>

<link rel="dns-prefetch" href="https://app.omniconvert.com/">
<script type="text/javascript" id="" charset="">window._mktz=window._mktz||[];</script>
<script id="" text="" charset="" type="text/javascript" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/y25952f.js"></script>
<script type="text/javascript" id="" charset="">!function(){var a,f,b=window,g=document,c=arguments,h="script",k="call cancelAction config identify push track trackClick trackForm update visit".split(" "),n=function(){var d,e=this,m=function(l){e[l]=function(){return e._e.push([l].concat(Array.prototype.slice.call(arguments,0))),e}};e._e=[];for(d=0;d<k.length;d++)m(k[d])};b.__woo=b.__woo||{};for(a=0;a<c.length;a++)b.__woo[c[a]]=b[c[a]]=b[c[a]]||new n;(a=g.createElement(h)).async=1;a.src="https://static.woopra.com/js/w.js";(f=g.getElementsByTagName(h)[0]).parentNode.insertBefore(a,
f)}("woopra");woopra.config({domain:"franchise.com",outgoing_tracking:!0,download_tracking:!0,click_tracking:!0});woopra.track();</script>
<script type="text/javascript" id="" charset="">window.dataLayer&&window.dataLayer.push({event:"login_status",login_status:logged_in==1});</script><iframe height="0" width="0" style="display: none; visibility: hidden;" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/saved_resource.html"></iframe>
<div id="batBeacon268437580204" style="width: 0px; height: 0px; display: none; visibility: hidden;"><img id="batBeacon612635018243" width="0" height="0" alt="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/0" style="width: 0px; height: 0px; display: none; visibility: hidden;"></div>
</body><script type="text/javascript" async="" src="./Own a Pest Hunters-Mosquito Hunters-Humbug Holiday Lighting Franchise_files/f.txt"></script><grammarly-desktop-integration data-grammarly-shadow-root="true"><template shadowrootmode="open"><style>
      div.grammarly-desktop-integration {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select:none;
        user-select:none;
      }

      div.grammarly-desktop-integration:before {
        content: attr(data-content);
      }
    </style><div aria-label="grammarly-integration" role="group" tabindex="-1" class="grammarly-desktop-integration" data-content="{&quot;mode&quot;:&quot;full&quot;,&quot;isActive&quot;:true,&quot;isUserDisabled&quot;:false}"></div></template></grammarly-desktop-integration></html>